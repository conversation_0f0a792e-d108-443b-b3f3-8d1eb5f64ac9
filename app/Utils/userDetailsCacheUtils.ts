import AsyncStorage from "@react-native-async-storage/async-storage";

interface UserDetailsCache {
  profilePicture?: { url: string; timestamp: number };
  lastName?: { name: string; timestamp: number };
}

/**
 * Utility functions for managing user details cache across the app
 * This helps coordinate cache invalidation when profile updates happen
 */

export const getUserDetailsCache = async (): Promise<UserDetailsCache> => {
  try {
    const cachedDetails = await AsyncStorage.getItem('userDetailsCache');
    return cachedDetails ? JSON.parse(cachedDetails) : {};
  } catch (error) {
    console.error("Error loading user details cache:", error);
    return {};
  }
};

export const saveUserDetailsCache = async (cache: UserDetailsCache): Promise<void> => {
  try {
    await AsyncStorage.setItem('userDetailsCache', JSON.stringify(cache));
  } catch (error) {
    console.error("Error saving user details cache:", error);
  }
};

export const clearUserDetailsCache = async (keys?: ('profilePicture' | 'lastName')[]): Promise<void> => {
  try {
    if (!keys) {
      // Clear entire cache
      await AsyncStorage.removeItem('userDetailsCache');
    } else {
      // Clear specific keys
      const currentCache = await getUserDetailsCache();
      const updatedCache = { ...currentCache };
      keys.forEach(key => {
        delete updatedCache[key];
      });
      await saveUserDetailsCache(updatedCache);
    }
  } catch (error) {
    console.error("Error clearing user details cache:", error);
  }
};

/**
 * Call this function from profile screen after successful profile picture update
 * to invalidate the cache and force refresh on other screens
 */
export const invalidateProfilePictureCache = async (): Promise<void> => {
  await clearUserDetailsCache(['profilePicture']);
};

/**
 * Call this function if last name is ever updated (though it requires admin contact)
 * to invalidate the cache and force refresh on other screens
 */
export const invalidateLastNameCache = async (): Promise<void> => {
  await clearUserDetailsCache(['lastName']);
};

/**
 * Clear all user-related cache (useful when user logs out)
 */
export const clearAllUserCache = async (): Promise<void> => {
  await clearUserDetailsCache();

  // Also clear wallet address cache
  try {
    const { clearAllWalletAddressCache } = await import('./walletAddressCacheUtils');
    await clearAllWalletAddressCache();
  } catch (error) {
    console.error("Error clearing wallet address cache:", error);
  }
};

/**
 * Check if cached data is expired (older than 24 hours)
 */
export const isCacheExpired = (timestamp: number): boolean => {
  const currentTime = new Date().getTime();
  const hoursDiff = (currentTime - timestamp) / (1000 * 60 * 60);
  return hoursDiff >= 24;
};
