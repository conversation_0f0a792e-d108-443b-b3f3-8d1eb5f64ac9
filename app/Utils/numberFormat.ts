/**
 * Formats a number to exactly 2 decimal places without rounding
 * @param value - The number to format
 * @returns The formatted number as a string with exactly 2 decimal places
 */
export const formatToTwoDecimals = (value: number): string => {
  if (value === null || value === undefined || isNaN(value)) return '0.00';

  // Convert to string and split by decimal point
  const [wholePart, decimalPart = ''] = value.toString().split('.');

  // Format the whole number part with commas as thousand separators
  const formattedWholePart = wholePart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // Get exactly 2 decimal places, padding with zeros if needed
  const formattedDecimal = decimalPart.padEnd(2, '0').slice(0, 2);

  // Combine whole part with thousand separators and decimal part
  return `${formattedWholePart}.${formattedDecimal}`;
};
export const formatToFourDecimals = (value: number): string => {
  if (value === null || value === undefined || isNaN(value)) return '0.0000';

  // Convert to string and split by decimal point
  const [wholePart, decimalPart = ''] = value.toString().split('.');

  // Format the whole number part with commas as thousand separators
  const formattedWholePart = wholePart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // Get exactly 4 decimal places, padding with zeros if needed
  const formattedDecimal = decimalPart.padEnd(4, '0').slice(0, 4);

  // Combine whole part with thousand separators and decimal part
  return `${formattedWholePart}.${formattedDecimal}`;
};

/**
 * Formats a number to exactly 2 decimal places and adds thousand separators
 * @param value - The number to format
 * @returns The formatted number as a string with thousand separators and exactly 2 decimal places
 */
export const formatNumberWithCommas = (value: number): string => {
  if (
    value === null ||
    value === undefined ||
    isNaN(Number(value)) ||
    !isFinite(Number(value))
  ) {
    return '0.00';
  } 

  // Round to exactly 2 decimal places
  const roundedValue = Number(value).toFixed(2);
  const [wholePart, decimalPart] = roundedValue.split('.');
  const formattedWholePart = wholePart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return `${formattedWholePart}.${decimalPart}`;
};