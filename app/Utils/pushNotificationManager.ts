import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import Constants from 'expo-constants';
import { AddPushToken } from '../RequestHandlers/User';

export class PushNotificationManager {
  /**
   * Register for push notifications
   */
  public static async registerForPushNotifications(): Promise<void> {
    try {
      const existingToken = await AsyncStorage.getItem("push_token");
      if (existingToken) {
        console.log('Push token already exists');
        return;
      }

      const pushToken = await this.registerForPushNotificationsAsync();
      if (pushToken) {
        await AsyncStorage.setItem("push_token", pushToken);
        
        await this.addPushNotificationToken({
          token: pushToken,
          deviceType: Platform.OS === "ios" ? "ios" : "android",
        });
        
        console.log('Push notifications registered successfully');
      }
    } catch (error) {
      console.error('Error registering for push notifications:', error);
    }
  }

  /**
   * Get push notification token
   */
  private static async registerForPushNotificationsAsync(): Promise<string | null> {
    let token = null;

    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    if (Device.isDevice) {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      
      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return null;
      }
      
      try {
        const projectId = Constants.expoConfig?.extra?.eas?.projectId ?? Constants.easConfig?.projectId;
        if (!projectId) {
          throw new Error('Project ID not found');
        }
        
        token = (await Notifications.getExpoPushTokenAsync({
          projectId,
        })).data;
        
        console.log('Push token obtained:', token);
      } catch (e) {
        console.error('Error getting push token:', e);
        token = `${e}`;
      }
    } else {
      console.log('Must use physical device for Push Notifications');
    }

    return token;
  }

  /**
   * Add push token to backend
   */
  private static async addPushNotificationToken(tokenData: {
    token: string;
    deviceType: string;
  }): Promise<void> {
    try {
      await AddPushToken(tokenData);
      console.log('Push token added to backend');
    } catch (error) {
      console.error('Error adding push token to backend:', error);
    }
  }

  /**
   * Clear stored push token
   */
  public static async clearPushToken(): Promise<void> {
    try {
      await AsyncStorage.removeItem("push_token");
      console.log('Push token cleared');
    } catch (error) {
      console.error('Error clearing push token:', error);
    }
  }

  /**
   * Get stored push token
   */
  public static async getStoredPushToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem("push_token");
    } catch (error) {
      console.error('Error getting stored push token:', error);
      return null;
    }
  }
}
