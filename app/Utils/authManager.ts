import AsyncStorage from '@react-native-async-storage/async-storage';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { CheckSession } from '../RequestHandlers/User';
import { clearAllUserCache } from './userDetailsCacheUtils';

export class AuthManager {
  /**
   * Clear all login data and sign out user
   */
  public static async clearLogin(setStoredCredentials?: (value: any) => void) {
    try {
      // Clear all cache before logging out
      await clearAllUserCache();

      // Sign out from Google
      await GoogleSignin.signOut();

      // Remove stored data
      await Promise.all([
        AsyncStorage.removeItem("login2fa"),
        AsyncStorage.removeItem("cookies")
      ]);

      // Clear credentials state
      if (setStoredCredentials) {
        setStoredCredentials(null);
      }

      console.log('User logged out successfully');
    } catch (error) {
      console.error('Error during logout:', error);
    }
  }

  /**
   * Parse JWT token and check expiration
   */
  public static parseJwt(token: any, clearLoginCallback?: () => void) {
    try {
      if (!token?.token) {
        return null;
      }

      const tkn = token.token;
      const payload = tkn.split(".")[1];
      
      if (!payload) {
        console.error('Invalid token format');
        return null;
      }

      const decodedPayload = JSON.parse(atob(payload));
      
      // Check if token is expired
      if (decodedPayload.exp * 1000 < new Date().getTime()) {
        console.log('Token expired, clearing login');
        if (clearLoginCallback) {
          clearLoginCallback();
        }
        return null;
      }

      return decodedPayload;
    } catch (error) {
      console.error('Error parsing JWT:', error);
      return null;
    }
  }

  /**
   * Check session validity with backend
   */
  public static async checkSession(clearLoginCallback?: () => void) {
    try {
      const res = await CheckSession();
      
      if (res.status === false || res.error) {
        console.log('Session invalid, clearing login');
        if (clearLoginCallback) {
          clearLoginCallback();
        }
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error checking session:', error);
      return false;
    }
  }

  /**
   * Validate token and session
   */
  public static async validateUserSession(
    token: any, 
    clearLoginCallback?: () => void
  ): Promise<boolean> {
    // First check token expiration
    const decodedToken = this.parseJwt(token, clearLoginCallback);
    if (!decodedToken) {
      return false;
    }

    // Then check session with backend
    return await this.checkSession(clearLoginCallback);
  }
}
