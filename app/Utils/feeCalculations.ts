/**
 * Utility functions for calculating fees
 */

/**
 * Calculates withdrawal fee and returns detailed breakdown
 * @param amount The amount to calculate fees for (in NGN)
 * @returns Object containing totalFee, receiveAmount, and totalAmount
 */
export async function calculateWithdrawalFee(
  amount: number
): Promise<{ totalFee: number; receiveAmount: number; totalAmount: number }> {
  if (amount <= 0) {
    throw new Error("Amount must be greater than 0");
  }

  // Define fee calculation logic
  const percentageFee = amount * 0.01; // 1% of the amount
  const additionalFee = 110; // Fixed additional fee
  const cappedFee = 650; // Maximum fee cap

  let totalFee = percentageFee + additionalFee;

  // Apply the cap if the percentage fee exceeds the cap
  if (percentageFee > cappedFee) {
    totalFee = cappedFee + additionalFee;
  }

  // Ensure the total fee does not exceed 760 NGN
  totalFee = Math.min(totalFee, 760);

  // Calculate the final amount after removing the fee
  const receiveAmount = amount - totalFee;
  const totalAmount = Number(amount);
  
  return {
    totalFee,
    receiveAmount,
    totalAmount,
  };
}

/**
 * Synchronous version of calculateWithdrawalFee for immediate UI calculations
 * @param amount The amount to calculate fees for (in NGN)
 * @returns Object containing totalFee, receiveAmount, and totalAmount
 */
export function calculateWithdrawalFeeSync(
  amount: number
): { totalFee: number; receiveAmount: number; totalAmount: number } {
  if (amount <= 0) {
    return {
      totalFee: 0,
      receiveAmount: 0,
      totalAmount: 0
    };
  }

  // Define fee calculation logic
  const percentageFee = amount * 0.01; // 1% of the amount
  const additionalFee = 110; // Fixed additional fee
  const cappedFee = 650; // Maximum fee cap

  let totalFee = percentageFee + additionalFee;

  // Apply the cap if the percentage fee exceeds the cap
  if (percentageFee > cappedFee) {
    totalFee = cappedFee + additionalFee;
  }

  // Ensure the total fee does not exceed 760 NGN
  totalFee = Math.min(totalFee, 760);

  // Calculate the final amount after removing the fee
  const receiveAmount = amount - totalFee;
  const totalAmount = Number(amount);
  
  return {
    totalFee,
    receiveAmount,
    totalAmount,
  };
}
