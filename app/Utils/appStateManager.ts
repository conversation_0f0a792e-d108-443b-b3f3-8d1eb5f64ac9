import { AppState, AppStateStatus, Platform } from 'react-native';
import { navigate } from '../navigation/navigationRef';

export interface AppStateConfig {
  inactivityThreshold?: number; // seconds
  hasPin?: boolean;
}

export class AppStateManager {
  private appState = { current: AppState.currentState };
  private lastActiveTime = { current: 0 };
  private hasNavigatedToLogin = { current: false };
  private subscription: any;
  private config: AppStateConfig;

  constructor(config: AppStateConfig = {}) {
    this.config = {
      inactivityThreshold: 300, // 5 minutes default
      hasPin: false,
      ...config
    };
  }

  /**
   * Initialize app state monitoring
   */
  public initialize() {
    this.subscription = AppState.addEventListener(
      'change',
      this.handleAppStateChange.bind(this)
    );
  }

  /**
   * Clean up listeners
   */
  public cleanup() {
    if (this.subscription) {
      this.subscription.remove();
      this.subscription = null;
    }
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Partial<AppStateConfig>) {
    this.config = { ...this.config, ...config };
  }

  /**
   * Handle app state changes
   */
  private handleAppStateChange(nextAppState: AppStateStatus) {
    // Skip if user doesn't have a pin
    if (!this.config.hasPin) {
      return;
    }

    // Track state transitions
    const prevState = this.appState.current;
    const isEnteringBackground = nextAppState === 'background';
    const isEnteringForeground = nextAppState === 'active';
    const isIOSInactive = nextAppState === 'inactive' && Platform.OS === 'ios';

    console.log(`App state change: ${prevState} → ${nextAppState}`);

    // Handle iOS transition sequence: active → inactive → background
    if (isIOSInactive && prevState === 'active') {
      console.log('iOS inactive state detected');
      // Treat as early background transition start
      this.lastActiveTime.current = Date.now();
      this.hasNavigatedToLogin.current = false;
    }

    // When app returns to active state
    if (isEnteringForeground) {
      console.log('App entering foreground');
      this.handleForegroundEntry(prevState);
    }

    // When app fully enters background
    if (isEnteringBackground) {
      console.log('App entering background');
      this.handleBackgroundEntry();
    }

    // Update state tracking
    this.appState.current = nextAppState;
  }

  /**
   * Handle app entering foreground
   */
  private handleForegroundEntry(prevState: AppStateStatus) {
    const backgroundEntryTime = this.lastActiveTime.current;
    const currentTime = Date.now();

    if (backgroundEntryTime > 0) {
      const inactiveDuration = (currentTime - backgroundEntryTime) / 1000;

      // iOS may report "inactive" instead of "background"
      const wasInBackground =
        prevState === 'background' ||
        (Platform.OS === 'ios' && prevState === 'inactive');

      console.log(`Inactive duration: ${inactiveDuration}s, threshold: ${this.config.inactivityThreshold}s`);

      if (
        wasInBackground &&
        inactiveDuration > this.config.inactivityThreshold! &&
        !this.hasNavigatedToLogin.current
      ) {
        console.log('Navigating to login due to inactivity');
        this.hasNavigatedToLogin.current = true;
        
        // Small delay to ensure navigation is ready
        setTimeout(() => {
          navigate('ExistingLoginScreenReturn');
        }, 100);
      }
    }
  }

  /**
   * Handle app entering background
   */
  private handleBackgroundEntry() {
    this.lastActiveTime.current = Date.now();
    this.hasNavigatedToLogin.current = false;
  }

  /**
   * Reset navigation state (call when user successfully logs in)
   */
  public resetNavigationState() {
    this.hasNavigatedToLogin.current = false;
    this.lastActiveTime.current = 0;
  }

  /**
   * Get current app state
   */
  public getCurrentState(): AppStateStatus {
    return this.appState.current;
  }

  /**
   * Get last active time
   */
  public getLastActiveTime(): number {
    return this.lastActiveTime.current;
  }
}

// Export singleton instance
export const appStateManager = new AppStateManager();
