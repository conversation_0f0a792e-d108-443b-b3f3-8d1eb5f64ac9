import { countries } from "../components/counties";
export const TransactionClick = (item, navigation) => {    
  if (
    (item?.type?.toLowerCase() === "internal-tranfer" &&
      item?.internalTransferReceiver) ||
    (item?.paymentGayway === "momo" && item?.type === "WITHDRAW") ||
    (item?.paymentGayway === "bank" && item?.type === "WITHDRAW") ||
    (item?.type === "TRANSFER" && item.fromWallet) ||
    (item?.provider === "link" && item?.type === "WITHDRAW") ||
    (item.type === "TRANSFER" && item.provider === "circle")
  ) {
    // if (
    //   item?.status != "completed" &&
    //   item?.status != "failed"
    // ) {
    //   if (item.provider === "link") {
    //     navigation.navigate("LinkSendStatus", {
    //       response: item,
    //     });
    //   } else {
    //     navigation.navigate("SendMoneyStatus", {
    //       response: item,
    //     });
    //   }
    // } else {
    if (item.provider === "link") {
      navigation.navigate("LinkSendTransactionDetails", {
        id: item.id || item._id,
      });
    } else {

      navigation.navigate("AllTransactionDetails", {
        id: item.id || item._id,
        transactionType:
          item?.type === "internal-tranfer"
            ? "sfx money app"
            : item?.paymentGayway === "momo"
            ? "mobile money"
            : item?.paymentGayway === "bank"
            ? "bank transfer"
            : item.provider === "circle"
            ? "p2p"
            : "",
      });
    }
    // }
  } else if (
    (item?.type?.toLowerCase() === "internal-tranfer" &&
      item?.internalTransferSender) ||
    (item?.paymentGayway === "momo" && item?.type === "DEPOSIT") ||
    (item?.paymentGayway === "bank" && item?.type === "DEPOSIT") ||
    (item?.provider === "link" && item?.type === "DEPOSIT") ||
    (item.type === "DEPOSIT" && item.provider === "circle")
  ) {
    if (item?.status != "completed" && item?.status != "failed") {
      if (item?.paymentGayway === "momo") {
        navigation.navigate("MoneySentScreen2", {
          transaction: item,
        });
      } else if (item.provider === "link") {
        navigation.navigate("LinkSentMoney", {
          transaction: item,
        });
      } else if (item.provider === "circle") {
        navigation.navigate("AllRTransactionDetails", {
          id: item.id || item._id,
          transactionType: "p2p",
        });
      } else {
        navigation.navigate("MoneySentScreen1", {
          transaction: item,
        });
      }
    } else {
      if (item.provider === "link") {
        navigation.navigate("LinkTransactionDetails", {
          id: item.id || item._id,
        });
      } else {
        navigation.navigate("AllRTransactionDetails", {
          id: item.id || item._id,
          transactionType:
            item?.type === "internal-tranfer"
              ? "sfx money app"
              : item?.paymentGayway === "momo"
              ? "mobile money"
              : item?.paymentGayway === "bank"
              ? "bank transfer"
              : item.provider === "circle"
              ? "p2p"
              : "",
        });
      }
    }
  } else if (
    item?.type === "fund-card" ||
    item?.type === "withdraw-from-card" ||
    item?.type === "create-card" ||
    item?.type === "card-debit" ||
    item.type === "card-transaction-reversal"
  ) {
    if (item?.status != "completed") {
      if (item?.type === "fund-card") {
        navigation.navigate("CardTopStatus", {
          response: item,
          destination: "home",
        });
      } else if (item?.type === "withdraw-from-card") {
        navigation.navigate("CardSendStatus", {
          response: item,
          destination: "home",
        });
      } else if (item?.type === "create-card") {
        navigation.navigate("CardPayStatusScreen", {
          response: item,
          destination: "home",
        });
      } else {
        navigation.navigate("CardTransactionDetails", {
          id: item.id || item._id,
        });
      }
    } else {
      if (item?.type === "create-card") {
        navigation.navigate("CardFeeTransactionDetails", {
          id: item.id || item._id,
        });
      } else {
        navigation.navigate("CardTransactionDetails", {
          id: item.id || item._id,
        });
      }
    }
  } else {
  }
};

export const getTransactionLabel = (item) => {

  
  if (!item?.type) return "";

  switch (item.type.toLowerCase()) {
    case "deposit":
      return "Recieved";
    case "internal-tranfer":
      return item.internalTransferSender ? `Recieved` : `Sent`;
    case "withdraw":
      return `Sent`;
    case "transfer":
      return item.provider === "circle" ? "Sent" : "";
    case "fund-card":
      return "Card top-up";
    case "withdraw-from-card":
      return "Card withdrawal";
    case "create-card":
      return "Card payment";
    case "card-debit":
      return "Spent USD";
    case "card-transaction-reversal":
      return "Reversal";
    default:
      return "";
  }
};

export const getTransactionIcon = (item, svg) => {
  if (!item?.type) return svg.p2p; // Default icon

  switch (item.type.toLowerCase()) {
    case "internal-tranfer":
      return svg.smalllogo;
    case "fund-card":
      return svg.ccaddFill;
    case "withdraw-from-card":
      return svg.withdraw;
    case "create-card":
      return svg.cardF;
    case "card-debit":
    case "card-transaction-reversal":
      return svg.bb;
    default:
      if (item?.paymentGayway === "momo") return svg.mobile;
      if (item?.paymentGayway === "bank" || item?.provider === "link")
        return svg.bankGreen;
      return svg.p2p;
  }
};


 export const getSymbol = (symbol) => {
    if (symbol === "TRY") {
      return "₺";
    }
    const curSymbol = countries.find((item) => item.currencyCode === symbol);
    return curSymbol ? curSymbol.symbol : "$";
  };
// if (
//   uDetails.cardholderId &&
//   !uDetails.cardHolderVerified
// ) {
//   // don't allow user to create card
//   handleToast(uDetails.cardHolderNote, "error");
// } else {
// }
// onPress={() => {
//   if (isReferalProgram) {
//     navigation.navigate("ReferralProgramScreen");
//   } else {
//     navigation.navigate("ReferralScreen");
//   }

{
  /* <View style={[styles.card, { height: 94 }]}>
                <View style={styles.accountBalance}>
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <P style={{ fontSize: 12 }}>Available asset balance</P>
                    <TouchableOpacity
                      style={{
                        marginLeft: 8,
                        width: 20,
                        height: 20,
                        justifyContent: "center",
                      }}
                      onPress={() => setHideBal((prevState) => !prevState)}
                    >
                      <SvgXml
                        xml={hideBal === true ? svg.eyeClose : svg.eyeOpen}
                      />
                  
                    </TouchableOpacity>
                  </View>
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      width: "100%",
                      alignItems: "center",
                      marginTop: "2%",
                    }}
                  >
              
                    <TouchableOpacity onPress={() => toggleModal()}>
                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                        }}
                      >
                        <P
                          style={{
                            fontSize: 24,
                            lineHeight: 36,
                          }}
                        >
                          {hideBal
                            ? "******"
                            : `$${amount?.toLocaleString(undefined, {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              })}`}{" "}
                          <Text
                            style={{
                              fontSize: 16,
                              fontFamily: fonts.poppinsMedium,
                            }}
                          >
                            {hideBal ? "***" : amc}
                          </Text>
                        </P>
                        <SvgXml xml={svg.arrowDown} style={{ marginLeft: 8 }} />
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.addMoneyButton}
                      onPress={() =>
                        isKycDone === "true"
                          ? navigation.navigate("AddMoneyScreen")
                          : isKycDone === "pending"
                          ? navigation.navigate("AccountVerificationPending")
                          : navigation.navigate("AccountVerificationPromt")
                      }
                    >
                      <P style={styles.addMoneyText}>Add money</P>
                    </TouchableOpacity>
                  </View>
                </View>
              </View> */

  {
    /* <View style={styles.tab}>
                                {tabLinks.map((item, index) => (
                                  <TouchableOpacity
                                    key={index}
                                    onPress={() => {
                                      setActive(item);
                                    }}
                                  >
                                    <View
                                      style={[
                                        styles.tabBtn,
                                        {
                                          backgroundColor:
                                            activeTab === item ? "#fff" : "transparent",
                                        },
                                      ]}
                                      key={index}
                                    >
                                      <P
                                        //@ts-ignore
                                        style={[
                                          styles.tabBtnP,
                                          {
                                            color:
                                              activeTab === item
                                                ? colors.primary
                                                : colors.white,
                                          },
                                        ]}
                                      >
                                        {item}
                                      </P>
                                    </View>
                                  </TouchableOpacity>
                                ))}
                              </View> */
  }

  {
    /* Account Balance */
  }
}
