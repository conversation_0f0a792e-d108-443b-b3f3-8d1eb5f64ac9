// Usage:
// import { useToast } from '../context/ToastContext';
// const { handleToast } = useToast();
// await withApiErrorToast(GetUserDetails(), handleToast);

import AsyncStorage from '@react-native-async-storage/async-storage';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { clearAllUserCache } from './userDetailsCacheUtils';

// Simple logout function to avoid circular dependencies
const performLogout = async () => {
  try {
    await GoogleSignin.signOut();
    await AsyncStorage.removeItem("login2fa");
    await AsyncStorage.removeItem("cookies");
    // Clear the credentials context by setting it to null
    // This will be handled by the app's credential context
    // The app will automatically redirect to login screen when credentials are null
  } catch (error) {
    console.error("Error during logout:", error);
  }
};

export async function withApiErrorToast<T>(
  promise: Promise<T>,
  handleToast: (msg: string, type?: "error" | "success" | "pending") => void
): Promise<T | undefined> {
  try {
    const result = await promise;

    // Check if the result indicates a 401 error (unauthorized)
    if (result && typeof result === 'object' && 'status' in result) {
      const resultObj = result as any;
      if (resultObj.status === 401 || (resultObj.error && resultObj.message?.includes('Unauthorized'))) {
        // Clear all cache and perform logout
        await clearAllUserCache();
        await performLogout();
        handleToast("Session expired. Please log in again.", "error");
        return undefined;
      }
    }

    return result;
  } catch (error: any) {
    if (error?.code === 'TIMEOUT') {
      handleToast("Request timed out. Please try again.", "error");
    } else if (error?.code === 'NETWORK_ERROR') {
      handleToast("An error occurred. Please try again.", "error");
    } else {
      handleToast(error?.message || "An error occurred. Please try again.", "error");
    }
    return undefined;
  }
}