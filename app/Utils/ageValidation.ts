/**
 * Age validation utility for date of birth inputs
 * Ensures users are at least 18 years old (changed from 17 to be more standard for financial services)
 */

export interface AgeValidationResult {
  isValid: boolean;
  age: number;
  errorMessage: string;
}

/**
 * Calculate age from date of birth
 * @param dateOfBirth - Date string in YYYY-MM-DD format or Date object
 * @returns Age in years
 */
export const calculateAge = (dateOfBirth: string | Date): number => {
  const today = new Date();
  const birthDate = typeof dateOfBirth === 'string' ? new Date(dateOfBirth) : dateOfBirth;
  
  // Check if the date is valid
  if (isNaN(birthDate.getTime())) {
    return 0;
  }
  
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  // If birthday hasn't occurred this year yet, subtract 1
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
};

/**
 * Validate if user meets minimum age requirement
 * @param dateOfBirth - Date string in YYYY-MM-DD format or Date object
 * @param minAge - Minimum age required (default: 17)
 * @returns Validation result with age and error message
 */
export const validateAge = (
  dateOfBirth: string | Date, 
  minAge: number = 17
): AgeValidationResult => {
  if (!dateOfBirth) {
    return {
      isValid: false,
      age: 0,
      errorMessage: "Date of birth is required"
    };
  }
  
  const birthDate = typeof dateOfBirth === 'string' ? new Date(dateOfBirth) : dateOfBirth;
  
  // Check if the date is valid
  if (isNaN(birthDate.getTime())) {
    return {
      isValid: false,
      age: 0,
      errorMessage: "Invalid Date of Birth"
    };
  }
  
  // Check if date is in the future
  const today = new Date();
  if (birthDate > today) {
    return {
      isValid: false,
      age: 0,
      errorMessage: "Invalid Date of Birth"
    };
  }
  
  const age = calculateAge(birthDate);
  
  // Check minimum age requirement
  if (age < minAge) {
    return {
      isValid: false,
      age,
      errorMessage: "Invalid Date of Birth"
    };
  }
  
  return {
    isValid: true,
    age,
    errorMessage: ""
  };
};

/**
 * Validate date of birth for financial services (18+ requirement)
 * @param dateOfBirth - Date string in YYYY-MM-DD format or Date object
 * @returns Validation result
 */
export const validateDateOfBirthForFinancialServices = (
  dateOfBirth: string | Date
): AgeValidationResult => {
  return validateAge(dateOfBirth, 17); // Using 17 as requested
};

/**
 * Format date validation error message
 * @param age - Current age of the user
 * @param minAge - Minimum required age
 * @returns Formatted error message
 */
export const getAgeErrorMessage = (age: number, minAge: number = 17): string => {
  if (age === 0) {
    return "Invalid Date of Birth";
  }
  return "Invalid Date of Birth";
};
