export const formatEmail = (email: string): string => {
  const [username, domain] = email?.split("@");
  if (username?.length > 2) {
    return `${username[0]}${"*".repeat(username?.length - 2)}${username?.slice(
      -1
    )}@${domain}`;
  }
  return email;
};


export const truncateAddress = (
  address,
  startChars = 6,
  endChars = 6,
  mask = "*****"
) => {
  if (address?.length <= startChars + endChars) {
    return address;
  }
  const start = address?.slice(0, startChars);
  const end = address?.slice(-endChars);
  return `${start}${mask}${end}`;
};