import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "../RequestHandlers/CredentailsContext";

interface UserDetails {
  firstName?: string;
  lastName?: string;
  middleName?: string;
  phoneNumber?: string;
  dob?: string;
  homeCountry?: string;
  residentAddress?: string;
  username?: string;
  hasPin?: boolean;
  [key: string]: any;
}

export const updateCredentials = async (
  setStoredCredentails: (credentials: any) => void,
  updates: UserDetails
) => {
  try {
    // Get current credentials from AsyncStorage
    const currentCredentialsStr = await AsyncStorage.getItem("cookies");
    if (!currentCredentialsStr) {
      throw new Error("No credentials found in storage");
    }

    const currentCredentials = JSON.parse(currentCredentialsStr);

    // Update the user object with new details
    const updatedCredentials = {
      ...currentCredentials,
      user: {
        ...currentCredentials.user,
        ...updates,
      },
    };

    // Save to AsyncStorage
    await AsyncStorage.setItem("cookies", JSON.stringify(updatedCredentials));

    // Update context
    setStoredCredentails(updatedCredentials);

    return true;
  } catch (error) {
    console.error("Error updating credentials:", error);
    return false;
  }
};
