import Aes from "react-native-aes-crypto";
import Constants from "expo-constants";

const algorithm = "aes-256-cbc";
const ENCRYPTION_KEY = Constants.expoConfig.extra.ENCRYPTION_KEY;
const ENCRYPTION_KEY2 = Constants.expoConfig.extra.KEY;
const ENCRYPTION_IV = Constants.expoConfig.extra.ENCRYPTION_IV;
if (!ENCRYPTION_KEY || !ENCRYPTION_IV) {
  throw new Error(
    "ENCRYPTION_KEY and ENCRYPTION_IV must be defined in the environment variables"
  );
}

export async function encryptPIN(text: string): Promise<string> {
  try {
    const encryptedBase64 = await Aes.encrypt(
      text,
      ENCRYPTION_KEY,
      ENCRYPTION_IV,
      algorithm
    );
    // Convert Base64 to Hex
    const encryptedHex = Buffer.from(encryptedBase64, "base64").toString("hex");
    return ENCRYPTION_IV + ":" + encryptedHex;
  } catch (error) {
    throw error;
  }
}

export async function decryptPIN(text: string): Promise<string> {
  try {
    const [iv, encryptedText] = text.split(":");
    const decrypted = await Aes.decrypt(
      encryptedText,
      ENCRYPTION_KEY,
      iv,
      algorithm
    );
    return decrypted; // Return the decrypted plaintext
  } catch (error) {
    throw error;
  }
}
export async function encryptGCM(
  text: string,
): Promise<string> {
  const iv = generateRandomHex(16); // 96-bit = 12 bytes
  const cipher = await Aes.encrypt(text, ENCRYPTION_KEY2, iv, algorithm);
  const cipherString = Buffer.from(cipher, "base64").toString("hex");
  return `${iv}:${cipherString}`;
}

// export async function encryptGCM(

//   text: string,
//   // hexKey: string
// ): Promise<string> {

//   const iv = generateRandomHex(12); // 96-bit = 12 bytes
//   const algorithm = "aes-256-gcm"
//   const cipher = await AesGcmCrypto.encrypt(text, false, ENCRYPTION_KEY2);
//   console.log( "ffff" ,cipher);

//   return `${iv}:${cipher}`;
// }

function generateRandomHex(byteLength: number): string {
  return Array.from({ length: byteLength }, () =>
    Math.floor(Math.random() * 256)
      .toString(16)
      .padStart(2, "0")
  ).join("");
}
