import AsyncStorage from "@react-native-async-storage/async-storage";

interface WalletAddressCache {
  [walletId: string]: {
    address: string;
    timestamp: number;
  };
}

/**
 * Utility functions for managing wallet address cache across the app
 * This helps coordinate cache management for different users
 */

export const getWalletAddressCache = async (): Promise<WalletAddressCache> => {
  try {
    const cachedAddresses = await AsyncStorage.getItem('walletAddressCache');
    return cachedAddresses ? JSON.parse(cachedAddresses) : {};
  } catch (error) {
    console.error("Error loading wallet address cache:", error);
    return {};
  }
};

export const saveWalletAddressCache = async (cache: WalletAddressCache): Promise<void> => {
  try {
    await AsyncStorage.setItem('walletAddressCache', JSON.stringify(cache));
  } catch (error) {
    console.error("Error saving wallet address cache:", error);
  }
};

export const clearWalletAddressCache = async (walletIds?: string[]): Promise<void> => {
  try {
    if (!walletIds) {
      // Clear entire wallet address cache
      await AsyncStorage.removeItem('walletAddressCache');
    } else {
      // Clear specific wallet addresses
      const currentCache = await getWalletAddressCache();
      const updatedCache = { ...currentCache };
      walletIds.forEach(walletId => {
        delete updatedCache[walletId];
      });
      await saveWalletAddressCache(updatedCache);
    }
  } catch (error) {
    console.error("Error clearing wallet address cache:", error);
  }
};

/**
 * Clear all wallet address cache (useful when user logs out)
 */
export const clearAllWalletAddressCache = async (): Promise<void> => {
  await clearWalletAddressCache();
};

/**
 * Check if cached wallet address is expired (older than 24 hours)
 */
export const isWalletAddressCacheExpired = (timestamp: number): boolean => {
  const currentTime = new Date().getTime();
  const hoursDiff = (currentTime - timestamp) / (1000 * 60 * 60);
  return hoursDiff >= 24;
};

/**
 * Get a specific wallet address from cache if not expired
 */
export const getCachedWalletAddress = async (walletId: string): Promise<string | null> => {
  try {
    const cache = await getWalletAddressCache();
    const cachedData = cache[walletId];

    if (cachedData && !isWalletAddressCacheExpired(cachedData.timestamp)) {
      return cachedData.address;
    }

    return null;
  } catch (error) {
    console.error("Error getting cached wallet address:", error);
    return null;
  }
};

/**
 * Cache a wallet address
 */
export const cacheWalletAddress = async (walletId: string, address: string): Promise<void> => {
  try {
    const currentCache = await getWalletAddressCache();
    const updatedCache = {
      ...currentCache,
      [walletId]: {
        address,
        timestamp: new Date().getTime()
      }
    };
    await saveWalletAddressCache(updatedCache);
  } catch (error) {
    console.error("Error caching wallet address:", error);
  }
};
