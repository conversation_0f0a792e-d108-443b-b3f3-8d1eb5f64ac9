import React from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import Content from "../../components/Content";
import Content2 from "../../components/Content2";

const { width, height } = Dimensions.get("window");

export default function SelectAccountScreen({ navigation }) {
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="SFx money app" navigation={navigation} />
        <View>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <P style={{ width: width * 0.8, marginBottom: 6 }}>
                Select account
              </P>
              <Content2
                svgg={svg.theter}
                header="Tether"
                body="1 USDT ~ 1 USD"
                containerStyle={{
                  justifyContent: "flex-start",
                  paddingLeft: 16,
                }}
                itemWrapper={{ marginLeft: 8 }}
                headerStyle={{ marginBottom: 4 }}
                textStyle={{ fontFamily: fonts.poppinsRegular, fontSize: 12 }}
              />
              <Content2
                svgg={svg.usdt}
                header="USD coin"
                body="1 USD ~ 1 USD"
                containerStyle={{
                  justifyContent: "flex-start",
                  paddingLeft: 16,
                }}
                itemWrapper={{ marginLeft: 8 }}
                headerStyle={{ marginBottom: 4 }}
                textStyle={{ fontFamily: fonts.poppinsRegular, fontSize: 12 }}
              />
            </View>
            <View style={{ width: "80%", marginTop: 32 }}>
              <Button
                btnText="Next"
                onPress={() => navigation.navigate("TransactionScreen")}
              />
            </View>
          </View>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    // justifyContent:"center",
    alignItems: "center",
  },
  detailWrap: {
    padding: 24,
    width: "90%",
    alignSelf: "center",
    // height:200,
    backgroundColor: "white",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },

  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
});
