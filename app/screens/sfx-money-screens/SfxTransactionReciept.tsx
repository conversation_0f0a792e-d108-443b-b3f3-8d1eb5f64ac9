import React, { useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import SendMoneyStatus from "../../components/SeendMoneyStatus";
import Link from "../../components/Link";

const { width, height } = Dimensions.get("window");

export default function SfxTransactionReciept({ navigation }) {
  const [showSendStatus, setShowSendStatus] = useState(false);
  const [tranStat, setTranStat] = useState("pending");
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Transaction receipt"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <DetailCard
                image={
                  <Image
                    source={require("../../assets/sfx2.png")}
                    style={{ width: 66.98, height: 24, objectFit: "contain" , marginBottom: 24}}
                  />
                }
                amount={
                  <>
                    <P style={{ fontSize: 24, lineHeight: 36, marginRight: 2 }}>
                      $900,000.00
                    </P>
                    <P style={{ marginTop: 5 }}>USD</P>
                  </>
                }
                convertedAmount={
                  <>
                    <P style={{ fontSize: 16, lineHeight: 24, marginRight: 2 }}>
                      ₦900,000.00
                    </P>
                    <P style={{ marginTop: 2, fontSize: 12, lineHeight: 18 }}>
                      NGN
                    </P>
                  </>
                }
                lineStyle={{ borderStyle: "dashed", marginTop: 24 }}
                bottomComponent={
                  <View style={styles.desCont}>
                    <View
                      style={{
                        paddingBottom: 24,
                        borderBottomWidth: 1,
                        borderColor: colors.stroke,
                        borderStyle: "dashed",
                      }}
                    >
                      <View style={styles.items}>
                        <P style={styles.holder}>Recipient</P>
                        <View style={{ justifyContent: "flex-end" }}>
                          <P style={styles.value}>John Doe</P>
                          <P
                            // @ts-ignore
                            style={[
                              styles.value,
                              {
                                color: colors.gray,
                                marginTop: 4,
                                fontFamily: fonts.poppinsRegular,
                              },
                            ]}
                          >
                           1234567890 | Mato
                          </P>
                        </View>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Sender</P>
                        <View style={{ justifyContent: "flex-end" }}>
                          <P style={styles.value}>Peter Japhet</P>
                          <P
                            // @ts-ignore
                            style={[
                              styles.value,
                              {
                                color: colors.gray,
                                marginTop: 4,
                                fontFamily: fonts.poppinsRegular,
                              },
                            ]}
                          >
                            1234567890 | Peter11
                          </P>
                        </View>
                      </View>
                    </View>
                    <View style={{ paddingTop: 24 }}>
                      <View style={styles.items}>
                        <P style={styles.holder}>Reference number</P>
                        <View
                          style={{
                            flexDirection: "row",
                            width: 150,
                            justifyContent: "flex-end",
                          }}
                        >
                          <P
                            // @ts-ignore
                            style={[
                              styles.value,
                              { textAlign: "right", width: 120 },
                            ]}
                          >
                            1224556352145232093874982
                          </P>
                        </View>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Payment method</P>
                        <P style={styles.value}>Send money</P>
                      </View>
                      <View style={[styles.items]}>
                        <P style={styles.holder}>Type</P>
                        <P style={styles.value}>SFx money app</P>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Timestamp</P>
                        <P style={styles.value}>
                          {"6:00 am"} • {"12 jul 2014"}
                        </P>
                      </View>
                    </View>
                  </View>
                }
              />
              <View style={styles.buttonWrap}>
                <Button
                  btnText="Share receipt"
                  onPress={() => {
                    // navigation.navigate("BankTransferScreen");
                    // setShowSendStatus(true);
                  }}
                />
                <View
                  style={{
                    flexDirection: "row",
                    width: "100%",
                    alignItems: "center",
                    justifyContent: "center",
                    marginTop: 26,
                  }}
                >
                  <SvgXml xml={svg.download} style={{ marginRight: 4 }} />
                  <Link style={{ fontSize: 12, lineHeight: 24 }}>
                    Download receipt
                  </Link>
                </View>
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
    textAlign: "right",
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  indicatorCont: {
    padding: 19.5,
    paddingTop: 4,
    paddingBottom: 4,
    backgroundColor: colors.secBackground,
    marginTop: 8,
    borderRadius: 99,
    flexDirection: "row",
    alignItems: "center",
  },
  indicatorDot: {
    width: 8,
    height: 8,
    borderRadius: 99,
    marginRight: 4,
  },
});
