import React, { useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import SendMoneyStatus from "../../components/SeendMoneyStatus";
import Link from "../../components/Link";

const { width, height } = Dimensions.get("window");
export default function SfxTransactionDetails({ navigation }) {
  const [showSendStatus, setShowSendStatus] = useState(false);
  const [tranStat, setTranStat] = useState("pending");
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Transaction details"
          navigation={navigation}
          iconComp={
            <TouchableOpacity style={{ position: "absolute", right: 0 }}>
              <SvgXml xml={svg.helpSq} />
            </TouchableOpacity>
          }
        />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <Image
                source={require("../../assets/face.png")}
                style={{
                  position: "absolute",
                  zIndex: 10,
                  width: 32,
                  height: 32,
                  alignSelf: "center",
                  top: -15,
                }}
              />
              <DetailCard
                amount={
                  <>
                    <P style={{ fontSize: 24, lineHeight: 36, marginRight: 2 }}>
                      $900,000
                    </P>
                    <P style={{ marginTop: 5 }}>USD</P>
                  </>
                }
                convertedAmount={
                  <>
                    <P style={{ fontSize: 16, lineHeight: 24, marginRight: 2 }}>
                      900,000
                    </P>
                    <P style={{ marginTop: 2, fontSize: 12, lineHeight: 18 }}>
                      USDT
                    </P>
                  </>
                }
                timer={
                  <View style={styles.indicatorCont}>
                    <View
                      style={[
                        styles.indicatorDot,
                        {
                          backgroundColor:
                            tranStat == "pending"
                              ? colors.yellow
                              : tranStat === "failed"
                              ? colors.red
                              : colors.green,
                        },
                      ]}
                    ></View>
                    <P style={{ fontSize: 10, lineHeight: 16 }}>{tranStat}</P>
                  </View>
                }
                lineStyle={{ borderStyle: "dashed", marginTop: 24 }}
                bottomComponent={
                  <View style={styles.desCont}>
                    <View
                      style={{
                        paddingBottom: 24,
                        borderBottomWidth: 1,
                        borderColor: colors.stroke,
                        borderStyle: "dashed",
                      }}
                    >
                      <View style={styles.items}>
                        <P style={styles.holder}>Account number</P>
                        <P style={styles.value}>**********</P>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Username</P>
                        <P style={styles.value}>Mato</P>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Account name</P>
                        <P style={styles.value}>John Doe</P>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Note</P>
                        <P style={styles.value}>N/A</P>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Reference number</P>
                        <View
                          style={{
                            flexDirection: "row",
                            width: 150,
                            justifyContent: "flex-end",
                            alignItems: 'center'
                          }}
                        >
                          <SvgXml xml={svg.lightCopy} style={{marginRight: 10}} />
                          <P
                            // @ts-ignore
                            style={[
                              styles.value,
                              { textAlign: "right", width: 120 },
                            ]}
                          >
                            1224556352145232093874982
                          </P>
                        </View>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Timestamp</P>
                        <P style={styles.value}>
                          {"6:00 am"} • {"12 jul 2014"}
                        </P>
                      </View>
                    </View>
                    <View style={{ paddingTop: 24 }}>
                      <View style={styles.items}>
                        <P style={styles.holder}>Fee</P>
                        <P style={styles.value}>
                          0{" "}
                          <P
                            // @ts-ignore
                            style={[
                              styles.value,
                              { fontFamily: fonts.poppinsRegular },
                            ]}
                          >
                            USD
                          </P>
                        </P>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Exchange rate</P>
                        <P style={styles.value}>1 USD ~1 USDT</P>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Payment method</P>
                        <P style={styles.value}>Send money</P>
                      </View>
                      <View style={[styles.items]}>
                        <P style={styles.holder}>Type</P>
                        <P style={styles.value}>SFx money app</P>
                      </View>
                      <View style={[styles.items, { marginBottom: 0 }]}>
                        <P style={styles.holder}>Account</P>
                        <P style={styles.value}>USDT</P>
                      </View>
                    </View>
                  </View>
                }
              />
              <View style={styles.buttonWrap}>
                {tranStat === "successful" && (
                  <Button
                    btnText="View receipt"
                    onPress={() => {
                      navigation.navigate("SfxTransactionReciept");
                    }}
                  />
                )}
                <View
                  style={{
                    alignItems: "center",
                    flexDirection: "row",
                    justifyContent: "center",
                  }}
                >
                  <SvgXml xml={svg.chat} style={{ marginRight: 4 }} />
                  <Link style={{fontSize: 12}}>Report transaction</Link>
                </View>
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  indicatorCont: {
    padding: 19.5,
    paddingTop: 4,
    paddingBottom: 4,
    backgroundColor: colors.secBackground,
    marginTop: 8,
    borderRadius: 99,
    flexDirection: "row",
    alignItems: "center",
  },
  indicatorDot: {
    width: 8,
    height: 8,
    borderRadius: 99,
    marginRight: 4,
  },
});
