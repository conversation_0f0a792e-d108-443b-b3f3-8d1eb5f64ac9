import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Linking,
  Image,
  ImageBackground,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import * as Clipboard from "expo-clipboard";
import Link from "../../components/Link";
import NoteComponent2 from "../../components/NoteComponent2";
import VirtualCard from "../../components/VirtualCard";
import { GetUserDetails } from "../../RequestHandlers/User";
import Loader from "../../components/ActivityIndicator";
import { color } from "react-native-reanimated";

const baseHeight = 800;
const { width, height } = Dimensions.get("window");
export default function CardTypeScreen({ navigation, route }) {
  const [activeColor, setActiveColor] = useState("#8B52FF");
  const Colors = ["#8B52FF", "#914C2C", "#BD0000", "#22C26E"];
  const [cardColor, setCardColor] = useState("purple");
  const { fee } = route?.params || 0;
  const { type } = route?.params || "";
  const {name} = route?.params || {};
  const [loader, setLoader] = useState(false);
  const [name1, setName] = useState("");

  const userDetails = async () => {
    setLoader(true);
    try {
      const userDetails = await GetUserDetails();
      if (userDetails?.firstName) {
        setName(`${userDetails?.firstName} ${userDetails?.lastName}`);
        setLoader(false);
      }
    } catch (error) {
      ;
    }
  };

  useEffect(() => {
    userDetails();
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Card Type" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentCard}>
            <View style={styles.section1Wrap}>
              <H4 style={styles.amt}>Your money, your way</H4>

              <H4
                // @ts-ignore
                style={[
                  styles.amt,
                  {
                    fontSize: 12,
                    lineHeight: (19.2 / baseHeight) * height,
                    color: colors.gray,
                    textAlign: "center",
                    fontFamily: fonts.poppinsRegular,
                  },
                ]}
              >
                Instantly available for online transactions. Secure, flexible,
                and ready to use.
              </H4>
              <VirtualCard
                cardColor={cardColor}
                name={name1?.toUpperCase()}
                cardNumber={"1234 1234 1234 1234"}
              />
              <View style={styles.section2Wrap}></View>
              <View style={styles.section3Wrap}>
                <H4
                  // @ts-ignore
                  style={[
                    {
                      fontSize: 12,
                      lineHeight: (19.2 / baseHeight) * height,
                      color: colors.gray,
                      textAlign: "center",
                      fontFamily: fonts.poppinsRegular,
                    },
                  ]}
                >
                  Select the SFx card color that fits{"\n"}your lifestyle
                </H4>
                <View
                  style={{
                    flexDirection: "row",
                    marginTop: (24 / baseHeight) * height,
                    alignItems: "center",
                    width: "100%",
                    justifyContent: "center",
                  }}
                >
                  {Colors.map((item, index) => (
                    <TouchableOpacity
                      onPress={() => {
                        setActiveColor(item);
                        item === "#8B52FF"
                          ? setCardColor("purple")
                          : item === "#914C2C"
                          ? setCardColor("brown")
                          : item === "#BD0000"
                          ? setCardColor("red")
                          : item === "#22C26E"
                          ? setCardColor("green")
                          : setCardColor("purple");
                      }}
                      key={index}
                      style={{
                        width: 24,
                        height: 24,
                        // backgroundColor: item,
                        borderWidth: activeColor === item ? 1 : 0,
                        borderColor: item,
                        borderRadius: 100,
                        marginRight: 16,
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <View
                        style={{
                          width: 16,
                          height: 16,
                          backgroundColor: item,
                          borderRadius: 100,
                        }}
                      ></View>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </View>
          </View>

          <P
            style={{
              textAlign: "center",
              marginTop: 16,
              fontSize: 12,
              lineHeight: 18,
              width: "85%",
              alignSelf: "center",
              color: colors.gray,
              fontFamily: fonts.poppinsRegular,
            }}
          >
            Start making online purchases and managing your finances from
            anywhere
          </P>
          <View style={styles.btnCont}>
            <Button
              btnText={"Get virtual card"}
              onPress={() =>
                navigation.navigate("CreateCardPin", {
                  fee: fee,
                  type: type,
                  name: name,
                  color: cardColor,
                })
              }
            />
          </View>
        </ScrollView>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentCard: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    // marginTop: 24,
    marginTop: (2.7 * height) / 100,
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 16,
    paddingRight: 16,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    marginBottom: 4,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  copyBtn: {
    paddingTop: 4,
    paddingBottom: 4,
    padding: 13,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  addMoney: {
    fontSize: 12,
    lineHeight: 19.2,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  amt: {
    fontSize: 16,
    // fontSize: 32,
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  amtCur: {
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  timer: {
    paddingTop: 4,
    paddingBottom: 4,
    paddingLeft: 16,
    paddingRight: 16,
    borderRadius: 99,
    marginTop: 16,
  },
  statusText: {
    fontSize: 10,
    lineHeight: 16,
    fontFamily: fonts.poppinsRegular,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    // marginTop: (2.7 * height) / 100,
    paddingTop: (2.7 * height) / 100,
    borderTopWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  section3Wrap: {
    width: "100%",
    marginTop: (2.7 * height) / 100,
    borderColor: colors.stroke,
    // paddingLeft: 16,
    // backgroundColor: 'red',
    alignItems: "center",
  },
  barCont: {
    // backgroundColor: "red",
    alignItems: "center",
  },
  bar1: {
    height: 46,
    width: 2,
    borderRadius: 2,
    backgroundColor: colors.green,
    marginTop: 4,
    marginBottom: 4,
  },
  bar2: {
    height: 28,
    width: 2,
    borderRadius: 2,
    marginTop: 4,
    marginBottom: 4,
  },
  progressDesCont: {
    flexDirection: "row",
  },
  progTextHead: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  progTextBody: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: (5 * height) / 100,
    // marginTop: 42,
    // alignItems: 'center',
    // justifyContent: 'center',
  },
});
