import React, { useState, useEffect, useCallback } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Linking,
  Platform,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import * as Clipboard from "expo-clipboard";
import Link from "../../components/Link";
import NoteComponent2 from "../../components/NoteComponent2";
import { CheckSession, GetUserDetails } from "../../RequestHandlers/User";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../../components/ActivityIndicator";
import { useToast } from "../../context/ToastContext";
import Dash from "../../components/Dash";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import AsyncStorage from "@react-native-async-storage/async-storage";

const baseHeight = 800;
const { width, height } = Dimensions.get("window");
export default function AccountVerificationPromt({ navigation }) {
  const [isPInfoCreated, setPInfoCreated] = useState(false);
  const [isUserNameCreated, setIsUserNameCreated] = useState(false);
  const [isTPinCreated, setIsTPinCreated] = useState(false);
  const [isIdVerified, setIsIdVerified] = useState(false);
  const [isKycPedding, setIsKycPedding] = useState(false);
  const [isKycFailed, setKycFailed] = useState(false);
  const [loader, setLoader] = useState(false);
  const { handleToast } = useToast();
  // const [isAccVerified, setAccVerified] = useState(false);

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  const getUserDetails = async () => {
    setLoader(true);
    try {
      const userDetails = await GetUserDetails();
      if (userDetails) {
        setLoader(false);
      }
      if (userDetails?.homeCountry) {
        setPInfoCreated(true);
      } else {
        setPInfoCreated(false);
      }
      if (userDetails?.username) {
        setIsUserNameCreated(true);
      } else {
        setIsUserNameCreated(false);
      }
      if (userDetails?.hasPin) {
        setIsTPinCreated(true);
      } else {
        setIsTPinCreated(false);
      }
      if (userDetails?.verified === "true") {
        setIsIdVerified(true);
      } else if (userDetails?.verified === "pending") {
        setIsKycPedding(true);
      } else if (userDetails?.verified === "failed") {
        setKycFailed(true);
      } else {
        setIsIdVerified(false);
      }
    } catch (error) {
      handleToast("Network error", "error");
    }
  };
  useFocusEffect(
    useCallback(() => {
      getUserDetails();
    }, [])
  );
  return (
    <View style={styles.body}>
      <Div>
        {/* <AuthenticationHedear
          text="Account verification"
          navigation={navigation}
        /> */}
        <View
          style={{
            width: "90%",
            paddingVertical: 24,
            flexDirection: "row",
            justifyContent: "flex-end",
            alignSelf: "center",
          }}
        >
          <TouchableOpacity
            style={{
              paddingHorizontal: 12,
              paddingVertical: 7,
              borderRadius: 99,
              borderWidth: 1,
              borderColor: colors.stroke,
            }}
            onPress={() => {
              navigation.pop();
            }}
          >
            <P style={{ fontSize: 12 }}>Close</P>
          </TouchableOpacity>
        </View>
        <ScrollView contentContainerStyle={{ paddingBottom: 50 }}>
          <View style={styles.contentCard}>
            <View style={styles.section1Wrap}>
              <SvgXml xml={svg.CustomerID} />
              <H4 style={styles.amt}>Verify your identity</H4>
              <H4
                // @ts-ignore
                style={[
                  styles.amt,
                  {
                    fontSize: 14,
                    lineHeight: 18,
                    color: colors.dark500,
                    textAlign: "center",
                    fontFamily: fonts.poppinsRegular,
                    marginTop: 4,
                  },
                ]}
              >
                Easiest way to receive money abroad
              </H4>
              <View style={styles.section3Wrap}>
                <View style={styles.progressDesCont}>
                  <View style={{ alignItems: "center", marginRight: 12 }}>
                    <SvgXml
                      xml={
                        isPInfoCreated
                          ? svg.circleSuccess
                          : svg.circleInprogress
                      }
                    />
                    <View
                      style={[
                        styles.bar2,
                        {
                          backgroundColor: isPInfoCreated
                            ? colors.green
                            : colors.lowOpPrimary2,
                        },
                      ]}
                    ></View>
                  </View>
                  <View>
                    <P style={styles.progTextHead}>Personal information</P>
                    <P style={styles.progTextBody}>
                      Fill your contact information
                    </P>
                  </View>
                </View>

                <View style={styles.progressDesCont}>
                  <View style={{ alignItems: "center", marginRight: 12 }}>
                    <SvgXml
                      xml={
                        isUserNameCreated
                          ? svg.circleSuccess
                          : isPInfoCreated
                          ? svg.circleInprogress
                          : svg.circleNull
                      }
                    />
                    <View
                      style={[
                        styles.bar2,
                        {
                          backgroundColor:
                            isUserNameCreated === true
                              ? colors.green
                              : colors.lowOpPrimary2,
                        },
                      ]}
                    ></View>
                  </View>
                  <View>
                    <P style={styles.progTextHead}>Username</P>
                    <P style={styles.progTextBody}>
                      Create your money app username
                    </P>
                  </View>
                </View>
                <View style={styles.progressDesCont}>
                  <View style={{ alignItems: "center", marginRight: 12 }}>
                    <SvgXml
                      xml={
                        isTPinCreated
                          ? svg.circleSuccess
                          : isUserNameCreated
                          ? svg.circleInprogress
                          : svg.circleNull
                      }
                    />
                    <View
                      style={[
                        styles.bar2,
                        {
                          backgroundColor:
                            isTPinCreated === true
                              ? colors.green
                              : colors.lowOpPrimary2,
                        },
                      ]}
                    ></View>
                  </View>
                  <View>
                    <P style={styles.progTextHead}>Transaction PIN</P>
                    <P style={styles.progTextBody}>
                      Create your transaction PIN
                    </P>
                  </View>
                </View>

                <View style={styles.progressDesCont}>
                  <View style={{ alignItems: "center", marginRight: 12 }}>
                    <SvgXml
                    width={20}
                    height={20}
                      xml={
                        isIdVerified
                          ? svg.circleSuccess
                          : isKycPedding
                          ? svg.peddingTrack
                          : isKycFailed ? svg.fail : isTPinCreated
                          ? svg.circleInprogress
                          : svg.circleNull
                      }
                    />
                  </View>
                  <View>
                    <P style={styles.progTextHead}>Identity verification</P>
                    <P style={styles.progTextBody}>
                      Submit your valid government identity{" "}
                      <P
                        style={{
                          fontSize: 12,
                          color: colors.gray,
                          fontFamily: fonts.poppinsRegular,
                        }}
                      >
                        (International passport or Bank verification number)
                      </P>
                    </P>
                  </View>
                </View>
              </View>
            </View>
          </View>
          {isKycPedding ? (
            <></>
          ) : (
            <View style={styles.btnCont}>
              <Button
                btnText={isIdVerified ? "Done" : "Contnue"}
                onPress={() => {
                  if (!isPInfoCreated) {
                    navigation.navigate("AccountVerification1");
                  } else if (!isUserNameCreated) {
                    navigation.navigate("AccountVerification2");
                  } else if (!isTPinCreated) {
                    navigation.navigate("AccountVerification3");
                  } else if (!isIdVerified) {
                    navigation.navigate("SumsubKYCScreen");
                  } else {
                    navigation.navigate("BottomTabNavigator");
                  }
                }}
              />
            </View>
          )}
        </ScrollView>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentCard: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    // marginTop: 24,
    marginTop: (2.7 * height) / 100,
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 16,
    paddingRight: 16,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    marginBottom: 4,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  copyBtn: {
    paddingTop: 4,
    paddingBottom: 4,
    padding: 13,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  addMoney: {
    fontSize: 12,
    lineHeight: 19.2,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  amt: {
    fontSize: 20,
    fontFamily: fonts.poppinsSemibold,
    marginTop: 16,
  },
  amtCur: {
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  timer: {
    paddingTop: 4,
    paddingBottom: 4,
    paddingLeft: 16,
    paddingRight: 16,
    borderRadius: 99,
    marginTop: 16,
  },
  statusText: {
    fontSize: 10,
    lineHeight: 16,
    fontFamily: fonts.poppinsRegular,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginTop: (2.7 * height) / 100,
  },
  section3Wrap: {
    width: "100%",
    marginTop: (2.7 * height) / 100,
    borderColor: colors.stroke,
    paddingLeft: 16,
  },
  barCont: {
    // backgroundColor: "red",
    alignItems: "center",
  },
  bar1: {
    height: 46,
    width: 2,
    borderRadius: 2,
    backgroundColor: colors.green,
    marginTop: 4,
    marginBottom: 4,
  },
  bar2: {
    height: 36,
    width: 2,
    borderRadius: 2,
    marginTop: 4,
    marginBottom: 4,
  },
  progressDesCont: {
    flexDirection: "row",
  },
  progTextHead: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.dark500,
    fontFamily: fonts.poppinsMedium,
  },
  progTextBody: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  btnCont: {
    width: "75%",
    alignSelf: "center",
    marginTop: (5 * height) / 100,
    // marginTop: 42,
    // alignItems: 'center',
    // justifyContent: 'center',
  },
  dash: {
    width: "100%",
    height: 1,
    borderWidth: 0.5,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    marginTop: (2.7 * height) / 100,
  },
});
