import React, { useState, useEffect } from "react";
import { ScrollView, StyleSheet, TouchableOpacity, View } from "react-native";
import P from "../../components/P";
import Div from "../../components/Div";
import AuthenticationHeader from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import { fonts } from "../../config/Fonts";
import * as Clipboard from "expo-clipboard";
import { useToast } from "../../context/ToastContext";

export default function BillingAddressScreen({ navigation, route }) {
  const { handleToast } = useToast();
  const { details, details2 } = route?.params || "";
  const copyItem = async (accNum) => {
    const copiedText = await Clipboard.setStringAsync(accNum);
    if (copiedText === true) {
      handleToast("Copied to clipboard", "success");
    } else {
      handleToast("Error copying item", "error");
    }
  };

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHeader
          text="Billing information"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.billCont}>
            <SvgXml xml={svg.outlineBill} />
            <View style={{ marginTop: 16 }}>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <P style={styles.item}>Billing address</P>
                <TouchableOpacity
                  onPress={() => {
                    copyItem(
                      details?.details?.billing_address?.billing_address1
                    );
                  }}
                >
                  <SvgXml xml={svg.lightCopy} pointerEvents="none" />
                </TouchableOpacity>
              </View>
              <P style={styles.value}>
                {details?.details?.billing_address?.billing_address1}
              </P>
            </View>
            <View style={{ marginTop: 16 }}>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <P style={styles.item}>Billing city</P>
                <TouchableOpacity
                  onPress={() => {
                    copyItem(details?.details?.billing_address?.billing_city);
                  }}
                >
                  <SvgXml xml={svg.lightCopy} pointerEvents="none" />
                </TouchableOpacity>
              </View>
              <P style={styles.value}>
                {details?.details?.billing_address?.billing_city}
              </P>
            </View>
            <View style={{ marginTop: 16 }}>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <P style={styles.item}>state</P>
                <TouchableOpacity
                  onPress={() => {
                    copyItem(details?.details?.billing_address?.state);
                  }}
                >
                  <SvgXml xml={svg.lightCopy} pointerEvents="none" />
                </TouchableOpacity>
              </View>
              <P style={styles.value}>
                {details?.details?.billing_address?.state}
              </P>
            </View>
            <View style={{ marginTop: 16 }}>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <P style={styles.item}>State code</P>
                <TouchableOpacity
                  onPress={() => {
                    copyItem(details?.details?.billing_address?.state_code);
                  }}
                >
                  <SvgXml xml={svg.lightCopy} pointerEvents="none" />
                </TouchableOpacity>
              </View>
              <P style={styles.value}>
                {details?.details?.billing_address?.state_code}
              </P>
            </View>

            <View style={{ marginTop: 16 }}>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <P style={styles.item}>Billing country</P>
                <TouchableOpacity
                  onPress={() => {
                    copyItem(
                      details?.details?.billing_address?.billing_country
                    );
                  }}
                >
                  <SvgXml xml={svg.lightCopy} pointerEvents="none" />
                </TouchableOpacity>
              </View>
              <P style={styles.value}>
                {details?.details?.billing_address?.billing_country}
              </P>
            </View>
            <View style={{ marginTop: 16 }}>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <P style={styles.item}>Country code</P>
                <TouchableOpacity
                  onPress={() => {
                    copyItem(details?.details?.billing_address?.country_code);
                  }}
                >
                  <SvgXml xml={svg.lightCopy} pointerEvents="none" />
                </TouchableOpacity>
              </View>
              <P style={styles.value}>
                {details?.details?.billing_address?.country_code}
              </P>
            </View>
            <View style={{ marginTop: 16 }}>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <P style={styles.item}>Billing zip code</P>
                <TouchableOpacity
                  onPress={() => {
                    copyItem(
                      details?.details?.billing_address?.billing_zip_code
                    );
                  }}
                >
                  <SvgXml xml={svg.lightCopy} pointerEvents="none" />
                </TouchableOpacity>
              </View>
              <P style={styles.value}>
                {details?.details?.billing_address?.billing_zip_code}
              </P>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  billCont: {
    width: "90%",
    padding: 26,
    borderRadius: 12,
    backgroundColor: colors.white,
    alignSelf: "center",
  },
  value: {
    marginTop: 8,
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    paddingRight: 50,
  },
  item: {
    fontSize: 14,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
});
