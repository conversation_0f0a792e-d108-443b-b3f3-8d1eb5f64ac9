import React, { useState, useCallback } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  TextInput,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import CustomSwitch from "../../components/CustomSwitch";
import { useFocusEffect } from "@react-navigation/native";

const { height } = Dimensions.get("window");

export default function PinManagementScreen({ navigation, route }) {
  const [secureTextEntry, setScureTextEntry] = useState(true);
  const [pinValue, setPinValue] = useState("1234");

  useFocusEffect(
    useCallback(() => {
      // Set the secureTextEntry based on the textEntry prop from route params
      if (route.params?.textEntry !== undefined) {
        setScureTextEntry(route.params.textEntry);
      }
    }, [route.params?.textEntry])
  );

  const handleToggle = (state) => {
    if (state) {
      navigation.navigate("FreezeCardPin", {
        from: "Face ID",
        headerText: "Enter card PIN",
      });
    }
  };

  const togglePv = () => {
    if (secureTextEntry) {
      navigation.navigate("FreezeCardPin", {
        from: "View PIN",
        headerText: "Enter transaction PIN",
        textEntry: false, // Pass the textEntry parameter here
      });
    } else {
      setScureTextEntry(true);
    }
  };

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="PIN management" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentCard}>
            <View style={styles.section1Wrap}>
              <View style={{ width: "100%", marginBottom: 32 }}>
                <P style={{ fontSize: 12, fontFamily: fonts.poppinsRegular }}>
                  View PIN
                </P>
                <View style={styles.cutomInput}>
                  <TextInput
                    style={styles.input}
                    secureTextEntry={secureTextEntry}
                    value={pinValue}
                    editable={false}
                  />
                  <TouchableOpacity
                    onPress={togglePv}
                    style={{
                      width: 24,
                      height: 24,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <SvgXml
                      xml={secureTextEntry ? svg.eyeClose : svg.eyeOpen}
                    />
                  </TouchableOpacity>
                </View>
              </View>
              <TouchableOpacity
                onPress={() => navigation.navigate("ChangePin1")}
              >
                <View
                  style={{
                    flexDirection: "row",
                    width: "100%",
                    alignItems: "center",
                    justifyContent: "space-between",
                    marginBottom: 32,
                  }}
                >
                  <P style={{ fontSize: 12 }}>Change PIN</P>
                  <SvgXml xml={svg.arroveRight} />
                </View>
              </TouchableOpacity>
              <View
                style={{
                  flexDirection: "row",
                  width: "100%",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <P style={{ fontSize: 12 }}>Face ID</P>
                <CustomSwitch onToggle={handleToggle} />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentCard: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    marginTop: (2.7 * height) / 100,
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 16,
    paddingRight: 16,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  cutomInput: {
    width: "100%",
    height: 44,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: colors.stroke,
    marginTop: 6,
    flexDirection: "row",
    alignItems: "center",
  },
  input: {
    width: "90%",
    height: 44,
    paddingLeft: 16,
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.poppinsRegular,
  },
});
