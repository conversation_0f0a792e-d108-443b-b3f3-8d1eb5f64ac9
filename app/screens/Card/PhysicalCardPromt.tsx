import React, { useState } from "react";
import { Dimensions, StyleSheet, View, Image } from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import NoteComponent from "../../components/NoteComponent";
import Button from "../../components/Button";
import Link from "../../components/Link";

const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function PhysicalCardPromt({ navigation }) {
  const [stImg, setStImg] = useState(require("../../assets/clock.png"));
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Physicals card" navigation={navigation} />
        <View style={styles.itemCont}>
          {/* <Image
            source={require("../../assets/nullCard.png")}
            style={{
              width: (100 / baseWidth) * width,
              height: (60 / baseWidth) * width,
            }}
          /> */}
          <Image source={stImg} style={{ width: 64, height: 64 }} />
          <P
            style={{
              fontSize: 16,
              fontFamily: fonts.poppinsMedium,
              marginTop: (24 / baseHeight) * height,
            }}
          >
            Physical card is coming soon
          </P>
          <P
            style={{
              marginTop: (8 / baseHeight) * height,
              fontSize: 12,
              color: colors.gray,
              fontFamily: fonts.poppinsRegular,
              paddingLeft: 16,
              paddingRight: 16,
              textAlign: "center",
            }}
          >
            We’re excited to announce that our physical SFx Card will be
            launching soon!
          </P>
          <P
            style={{
              marginTop: (32 / baseHeight) * height,
              marginBottom: (16 / baseHeight) * height,
              fontSize: 12,
              color: colors.gray,
              fontFamily: fonts.poppinsRegular,
              paddingLeft: 16,
              paddingRight: 16,
              textAlign: "center",
            }}
          >
            By clicking “notify me” you agree to be notify by SFx when the
            physicals card is ready
          </P>
        </View>
        <View style={styles.bottomItem}>
          <View
            style={{
              width: (264 / baseWidth) * width,
              marginBottom: (16 / baseHeight) * height,
            }}
          >
            <Button btnText="Notify me" onPress={() => navigation.pop()} />
          </View>
          <Link
            style={{ fontSize: 12 }}
            onPress={() => navigation.navigate("Home")}
          >
            Go back home
          </Link>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
  itemCont: {
    width: "90%",
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "center",
    marginTop: (48 / baseHeight) * height,
  },
  bottomItem: {
    width: "90%",
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "center",
    // marginTop: (32 / baseHeight) * height,
  },
});
