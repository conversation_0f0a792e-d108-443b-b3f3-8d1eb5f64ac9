import React, { useEffect, useRef, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Text,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import Button from "../../components/Button";
import Keyboard from "../../components/Keyboard";

const baseHeight = 802;
const { width, height } = Dimensions.get("window");

export default function CreateCardPin({ navigation, route }) {
  const [fields, setFields] = useState(["", "", "", ""]);
  const [errors, setErrors] = useState([false, false, false, false]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [showDots, setShowDots] = useState([false, false, false, false]);
  const refs = [useRef(), useRef(), useRef(), useRef()];
  const { fee, type, color } = route?.params || "";
  const { name } = route?.params || {};

  const handleKeyPress = (key) => {
    // Clear errors when typing starts
    // setErrors([false, false, false, false]);

    if (key === "←") {
      if (activeIndex > 0 || fields[activeIndex] !== "") {
        handleChangeText(activeIndex, "");
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[activeIndex] = false;
          return updatedDots;
        });
        if (activeIndex > 0) {
          setActiveIndex(activeIndex - 1);
        }
      }
    } else if (key === "Enter") {
    } else {
      handleChangeText(activeIndex, key);
      if (activeIndex < 3) {
        setActiveIndex(activeIndex + 1);
      }
    }
  };

  const handleChangeText = (index, text) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;

      setErrors((prevErrors) => {
        const updatedErrors = [...prevErrors];
        updatedErrors[index] = text === "";
        return updatedErrors;
      });

      if (text !== "") {
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[index] = false;
          return updatedDots;
        });

        setTimeout(() => {
          setShowDots((prevShowDots) => {
            const updatedDots = [...prevShowDots];
            updatedDots[index] = true;
            return updatedDots;
          });
        }, 500);
      }

      return updatedFields;
    });
  };

  useEffect(() => {
    if (fields.every((field) => field !== "")) {
      // All fields have been filled, call verifyPin
      // handleSubmit();
    }
  }, [fields]);

  const handleSubmit = () => {
    const hasEmptyFields = fields.some((field) => field === "");
    if (hasEmptyFields) {
      setErrors(fields.map((field) => field === ""));
    } else {
      navigation.navigate("ConfirmCardPin", {
        pins: fields.join(""),
        fee: fee,
        type: type,
        color: color,
        name: name,
        pin: fields.join(""),
      });
    }
  };

  return (
    <>
      <View style={styles.body}>
        <Div>
          <ScrollView>
            <AuthenticationHedear text="Create PIN" navigation={navigation} />
            <View style={styles.contentBody}>
              <View style={styles.inputCardWrap}>
                <Text style={styles.title}>Enter PIN</Text>
                <View style={styles.con}>
                  {refs.map((ref, index) => (
                    <View
                      key={index}
                      style={[
                        styles.pinInput,
                        {
                          marginRight: index === refs.length - 1 ? 0 : 16,
                          borderColor: errors[index]
                            ? colors.red
                            : activeIndex === index
                            ? colors.primary
                            : "#E6E5E5",
                        },
                      ]}
                    >
                      <View style={styles.pinView}>
                        {showDots[index] ? (
                          <View style={styles.dot} />
                        ) : (
                          <Text style={styles.pinText}>{fields[index]}</Text>
                        )}
                      </View>
                    </View>
                  ))}
                </View>
                {errors.some((error) => error) && (
                  <Text style={styles.errorText}>
                    Please fill in all fields.
                  </Text>
                )}
              </View>
              <View style={styles.bottom}>
                <View style={{ width: "90%", alignSelf: "center" }}>
                  <Keyboard onKeyPress={handleKeyPress} />
                </View>
                <View
                  style={{
                    width: "80%",
                    alignSelf: "center",
                    marginTop: (32 / baseHeight) * height,
                  }}
                >
                  <Button btnText="Enter PIN" onPress={handleSubmit} />
                </View>
              </View>
            </View>
          </ScrollView>
        </Div>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: "rgba(247, 244, 255, 1)",
  },
  contentBody: {
    width,
    height: (92 * height) / 100,
    backgroundColor: "rgba(247, 244, 255, 1)",
    paddingTop: 16,
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
    paddingTop: 24,
    backgroundColor: "#fff",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    width: 48,
    height: 48,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 18,
  },
  pinView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  pinText: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
  },
  dot: {
    width: 16,
    height: 16,
    backgroundColor: "#000",
    borderRadius: 12,
  },
  con: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: "75%",
    paddingBottom: 24,
  },
  bottom: {
    width,
    marginTop: (100 / baseHeight) * height,
  },
  errorText: {
    color: colors.red,
    fontSize: 12,
    marginTop: -20,
    fontFamily: fonts.poppinsRegular,
    marginBottom: 24,
  },
  title: {
    color: colors.gray,
    textAlign: "center",
    marginBottom: 16,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
  },
});
