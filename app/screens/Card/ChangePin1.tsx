import React, { useRef, useState } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  Dimensions,
  TextInput,
} from "react-native";
import Div from "../../components/Div";
import { colors } from "../../config/colors";
import P from "../../components/P";
import Button from "../../components/Button";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { fonts } from "../../config/Fonts";
import { ValidatePin } from "../../RequestHandlers/User";
import { UpdateCardPin } from "../../RequestHandlers/Card";
import { useToast } from "../../context/ToastContext";
import { encryptPIN } from "../../Utils/encrypt";
const { width, height } = Dimensions.get("window");
const screenHeight = height;

export default function ExistingRp({ navigation, route }) {
  const { cardID } = route.params;
  const [loading, setLoading] = useState(false);
  const [fields, setFields] = useState(["", "", "", ""]);
  const [newFields, setNewFields] = useState(["", "", "", ""]);
  const [confirmFields, setConfirmFields] = useState(["", "", "", ""]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [activeGroup, setActiveGroup] = useState(null);
  const [activeIndex, setActiveIndex] = useState(null);
  const { handleToast } = useToast();

  const refs = fields.map(() => useRef(null));
  const newRefs = newFields.map(() => useRef(null));
  const confirmRefs = confirmFields.map(() => useRef(null));

  const focusNextField = (refsArray, index) => {
    const nextRef = refsArray[index + 1];
    if (nextRef) nextRef.current.focus();
  };

  const handleKeyPress = (index, event, fields, setFields, refsArray) => {
    const { nativeEvent } = event;
    if (nativeEvent.key === "Backspace" && fields[index] === "") {
      const prevIndex = index - 1;
      if (prevIndex >= 0) {
        setFields((prevFields) => {
          const updatedFields = [...prevFields];
          updatedFields[prevIndex] = "";
          return updatedFields;
        });
        refsArray[prevIndex].current.focus();
      }
    }
  };

  const handleChangeText = (index, text, fields, setFields, refsArray) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;
      if (text !== "") focusNextField(refsArray, index);
      return updatedFields;
    });
  };

  const getInputBorderStyle = (group, index, isEmpty, notMatch) => {
    if (isEmpty) {
      return { borderColor: colors.red }; // Error border color for empty fields
    } else if (notMatch) {
      return { borderColor: colors.red }; // Error border color for mismatch
    }
    return {
      borderColor:
        activeGroup === group && activeIndex === index
          ? colors.primary
          : "#E6E5E5",
    };
  };

  const handleFocus = (group, index) => {
    setActiveGroup(group);
    setActiveIndex(index);
  };

  const changePin = async () => {
    try {
      const body = {
        cardId: cardID,
        cardPin: newFields.join(""),
      };
      const res = await UpdateCardPin(body);
      if (res.status === true) {
        navigation.navigate("CardScreen");
      }
      if (res?.error) {
        handleToast(res.message, "error");
      } else {
        handleToast(res.message, "success");
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const navigate = (activityId) => {
    navigation.navigate("VerifyActivityScreen", {
      activityId: activityId,
      activityType: "update-card-pin",
      ActivityFunction: changePin,
    });
  };
  const handleSubmit = async () => {
    setIsSubmitted(true);
    const newPin = newFields.join("");
    const confirmPin = confirmFields.join("");
    if (
      fields.every((field) => field !== "") &&
      newFields.every((field) => field !== "") &&
      confirmFields.every((field) => field !== "")
    ) {
      setLoading(true);
      if (newPin === confirmPin) {
        try {
          const response = await ValidatePin({
            pin: await encryptPIN(String(fields?.join(""))),
            activityType: "update-card-pin",
          });
          if (response.error) {
            handleToast("Incorrect transaction PIN", "error");
            setLoading(false);
          } else {
            navigate(response?.activityId);
          }
        } catch (error) {}
      } else {
      }
    } else {
    }
  };

  return (
    <View style={styles.body}>
      <Div style={{ height: screenHeight }}>
        <AuthenticationHedear text="Change card pin" navigation={navigation} />
        <ScrollView>
          <View style={{ backgroundColor: colors.secBackground }}>
            <View style={styles.components}>
              {/* Existing PIN Section */}
              <View style={styles.section}>
                <P style={styles.label}>Enter Transaction PIN</P>
                <View style={styles.inputContainer}>
                  {fields.map((value, index) => (
                    <TextInput
                      key={index}
                      style={[
                        styles.pinInput,
                        getInputBorderStyle(
                          "fields",
                          index,
                          isSubmitted && value === "",
                          false
                        ),
                        { marginRight: index === fields.length - 1 ? 0 : 16 },
                      ]}
                      placeholderTextColor="#000"
                      keyboardType="numeric"
                      ref={refs[index]}
                      onChangeText={(text) =>
                        handleChangeText(index, text, fields, setFields, refs)
                      }
                      onKeyPress={(event) =>
                        handleKeyPress(index, event, fields, setFields, refs)
                      }
                      onFocus={() => handleFocus("fields", index)}
                      value={value}
                      secureTextEntry
                    />
                  ))}
                </View>
              </View>

              {/* New PIN Section */}
              <View style={styles.section}>
                <P style={styles.label}>Enter new card PIN</P>
                <View style={styles.inputContainer}>
                  {newFields.map((value, index) => (
                    <TextInput
                      key={index}
                      style={[
                        styles.pinInput,
                        // @ts-ignore
                        getInputBorderStyle(
                          "newFields",
                          index,
                          isSubmitted && value === "",
                          isSubmitted && value === ""
                          // isSubmitted &&
                          //   confirmFields[index] !== newFields[index]
                        ),
                        {
                          marginRight: index === newFields.length - 1 ? 0 : 16,
                        },
                      ]}
                      placeholderTextColor="#000"
                      keyboardType="numeric"
                      ref={newRefs[index]}
                      onChangeText={(text) =>
                        handleChangeText(
                          index,
                          text,
                          newFields,
                          setNewFields,
                          newRefs
                        )
                      }
                      onKeyPress={(event) =>
                        handleKeyPress(
                          index,
                          event,
                          newFields,
                          setNewFields,
                          newRefs
                        )
                      }
                      onFocus={() => handleFocus("newFields", index)}
                      value={value}
                      secureTextEntry
                    />
                  ))}
                </View>
              </View>

              {/* Confirm New PIN Section */}
              <View style={styles.section}>
                <P style={styles.label}>Confirm new card PIN</P>
                <View style={styles.inputContainer}>
                  {confirmFields.map((value, index) => (
                    <TextInput
                      key={index}
                      style={[
                        styles.pinInput,
                        getInputBorderStyle(
                          "confirmFields",
                          index,
                          isSubmitted && value === "",
                          isSubmitted &&
                            confirmFields[index] !== newFields[index]
                        ),
                        {
                          marginRight:
                            index === confirmFields.length - 1 ? 0 : 16,
                        },
                      ]}
                      placeholderTextColor="#000"
                      keyboardType="numeric"
                      ref={confirmRefs[index]}
                      onChangeText={(text) =>
                        handleChangeText(
                          index,
                          text,
                          confirmFields,
                          setConfirmFields,
                          confirmRefs
                        )
                      }
                      onKeyPress={(event) =>
                        handleKeyPress(
                          index,
                          event,
                          confirmFields,
                          setConfirmFields,
                          confirmRefs
                        )
                      }
                      onFocus={() => handleFocus("confirmFields", index)}
                      value={value}
                      secureTextEntry
                    />
                  ))}
                </View>
              </View>
            </View>
            <View style={styles.buttonContainer}>
              <Button
                btnText="Enter PIN"
                onPress={handleSubmit}
                loading={loading}
              />
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  components: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: colors.white,
    marginTop: 16,
    alignItems: "center",
    borderRadius: 12,
    paddingTop: 24,
    paddingBottom: 24,
  },
  section: {
    marginBottom: 32,
  },
  label: {
    fontSize: 12,
    marginBottom: 16,
    textAlign: "center",
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  inputContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  pinInput: {
    width: 50,
    height: 50,
    textAlign: "center",
    fontSize: 20,
    borderWidth: 1,
    borderRadius: 8,
  },
  buttonContainer: {
    width: "70%",
    alignSelf: "center",
    marginTop: 32,
  },
});
