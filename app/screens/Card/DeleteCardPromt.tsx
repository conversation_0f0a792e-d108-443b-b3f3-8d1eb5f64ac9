import React, { useEffect, useState } from "react";
import { StyleSheet, View, Image, Dimensions, StatusBar } from "react-native";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Link from "../../components/Link";
import NoteComponent2 from "../../components/NoteComponent2";

const baseHeight = 800;
const { width, height } = Dimensions.get("window");
export default function DeleteCardPromt({ navigation, route }) {
  const [stImg, setStImg] = useState(require("../../assets/alertError.png"));
  const [stText, setStText] = useState("Sent money is pending");
  const { cardID } = route?.params || "";
  return (
    <View style={styles.body}>
      <View style={styles.itemBox}>
        <Image source={stImg} style={{ width: 64, height: 64 }} />
        <P style={styles.statusState}>Delete card</P>

        <P style={styles.stTx}>
          Deleting this card will permanently remove it from your account.
        </P>
        <View style={{ width: "80%", marginTop: (24 / baseHeight) * height }}>
          <NoteComponent2
            text={
              "Any pending transactions or subscriptions linked to this card will be affected"
            }
          />
        </View>
        {/* @ts-ignore */}
        <P style={[styles.stTx, { marginTop: (32 / baseHeight) * height }]}>
          By clicking “proceed” you agree for your SFx card deleted
        </P>
        <View style={{ width: "75%", marginTop: 32 }}>
          <Button
            btnText="Proceed"
            onPress={() => {
              navigation.navigate("DeleteCardPin", { cardID: cardID });
            }}
          />
          <Link
            style={{ textAlign: "center", marginTop: 16, fontSize: 12 }}
            onPress={() => navigation.navigate("CardScreen")}
          >
            Go back home
          </Link>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    width,
    height: (105 * height) / 100,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    bottom: 0,
    // top: 0,
    zIndex: 100,
  },
  itemBox: {
    width: "100%",
    alignItems: "center",
    // marginTop: (20*height)/100
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    width: "80%",
    fontSize: 12,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
});
