import React, { useCallback, useState } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
} from "react-native";
import { colors } from "../../config/colors";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import Content from "../../components/Content";
import Button from "../../components/Button";
import CustomSwitch from "../../components/CustomSwitch";
import VirtualCard from "../../components/VirtualCard";
import { GetcardById, GetcardByToken } from "../../RequestHandlers/Card";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../../components/ActivityIndicator";

const { width, height } = Dimensions.get("window");
const baseHeight = 812;
export default function CardManagementScreen({ navigation, route }) {
  const [hideBal, setHideBal] = useState(false);
  const [amount, setAmount] = useState("0.00");
  const [cardColor, setCardColor] = useState("brown");
  const [loader, setLoader] = useState(false);
  const { details } = route?.params || {};
  const [items, setItems] = useState<any>([]);
  const [mainD, setMainD] = useState<any>([]);
  const [exYear, setExYear] = useState("");
  //   const [freezeCardPin, setFreezeCardPin] = useState(false);
  const [isCardFreezed, setIsCardFreezed] = useState(false);
  const handleToggle = (newState) => {
    if (newState) {
      navigation.navigate("FreezeCardPin", {
        cardID: items?.card?.id,
        status: true,
        activityType: "freeze-card",
      });
      setIsCardFreezed(true);
    } else {
      navigation.navigate("FreezeCardPin", {
        cardID: items?.card?.id,
        status: false,
        activityType: "unfreeze-card",
      });
      setIsCardFreezed(false);
    }
  };
  const getCardById = async (id) => {
    try {
      const cardDetails = await GetcardById(id);
      if (cardDetails?.card) {
        setItems(cardDetails);
        setIsCardFreezed(cardDetails?.card?.status === "active" ? false : true);
      }
      if (details.details) {
        setLoader(false);
        setMainD(details.details);
        const yyyy = details.details.expiry_year;
        const lastyyyy = yyyy.split("");
        setExYear(`${lastyyyy[2]}${lastyyyy[3]}`);
      }
    } catch (error) {
    } finally {
      setLoader(false);
    }
  };
  // const getDetaileByTkn = async (token) => {
  //   try {
  //     const details = await GetcardByToken(token);
  //     if (details.data) {
  //       setLoader(false);
  //       setMainD(details.data);
  //       const yyyy = details.data.expiry_year;
  //       const lastyyyy = yyyy.split("");
  //       setExYear(`${lastyyyy[2]}${lastyyyy[3]}`);
  //     } else {
  //       setLoader(false);
  //     }
  //   ;
  //   } catch (error) {
  //     ;
  //   }finally{
  //     setLoader(false)
  //   }
  // };
  function formatCardNumber(cardNumber) {
    const cleaned = cardNumber?.replace(/\D+/g, "");
    const formatted = cleaned.match(/.{1,4}/g)?.join(" ") || "";
    return formatted;
  }
  useFocusEffect(
    useCallback(() => {
      setLoader(true);
      if (details) {
        getCardById(details?.card?.id);
      }
    }, [details])
  );
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Card management" navigation={navigation} />
        <ScrollView>
          <View style={styles.cc}>
            <VirtualCard
              isCardFreezed={items?.card?.status == "active" ? false : true}
              ccStyle={{ height: (188 / baseHeight) * height }}
              contStyle={{ marginTop: 0, marginBottom: 0 }}
              name={
                mainD?.card_name ? mainD?.card_name?.toUpperCase() : "....."
              }
              cardNumber={
                mainD?.card_number
                  ? formatCardNumber(mainD?.card_number)
                  : ".... .... .... ...."
              }
              CardText1={{ fontSize: 9 }}
              CardText2={{ fontSize: 15 }}
              cardColor={items?.card?.colour}
              cvv={mainD.cvv ? mainD.cvv : "..."}
              validDate={`${mainD ? mainD.expiry_month : ".."}/${
                mainD ? exYear : ".."
              }`}
            />
          </View>
          <View style={styles.actionCard}>
            <Content
              svg1={svg.cFreeze}
              header={isCardFreezed ? "Unfreeze card" : "Freeze card"}
              arrowRight={false}
              disabled={true}
              rightComponent={
                <CustomSwitch onToggle={handleToggle} isOn={isCardFreezed} />
              }
            />
            {/* <Content
              svg1={svg.securityLock}
              header="Card PIN"
              onPress={() =>
                navigation.navigate("PinManagementScreen", { textEntry: true })
              }
            /> */}
            <Content
              svg1={svg.securityLock}
              isCardFreezed={isCardFreezed}
              header="Change card PIN"
              onPress={() =>
                navigation.navigate("ChangePin1", { cardID: items?.card?.id })
              }
            />
            {/* <Content
              svg1={svg.bb}
              isCardFreezed={isCardFreezed}
              header="Manage online merchant"
              onPress={() => navigation.navigate("OnlineMarchent")}
            /> */}
            <Content
              svg1={svg.cardG}
              isCardFreezed={isCardFreezed}
              header="Change card color"
              onPress={() =>
                navigation.navigate("CardColor", { cardID: items?.card?.id })
              }
            />
            {/* <Content
              svg1={svg.alertError2}
              header="FAQs"
              //   onPress={() => navigation.navigate("MobileMoneyScreen2")}
            /> */}
            <Content
              svg1={svg.support}
              header="Support"
              bottomBorder={false}
              //   onPress={() => navigation.navigate("DeleteCardPromt")}
            />
          </View>
          <View
            style={{
              width: "90%",
              marginTop: (16 / baseHeight) * height,
              alignSelf: "center",
            }}
          >
            <Button
              onPress={() =>
                navigation.navigate("DeleteCardPromt", {
                  cardID: items?.card?.id,
                })
              }
              style={{
                backgroundColor: colors.white,
                borderRadius: 12,
                height: 48,
              }}
              btnTextStyle={{ color: colors.black }}
              btnText="Delete card"
            />
          </View>
          {/* transactions */}
        </ScrollView>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  card: {
    // backgroundColor: 'red',¿
    borderRadius: 12,
    padding: 16,
    width: "90%",
    alignSelf: "center",
    marginBottom: 12,
    // elevation: 4,
  },
  accountBalance: {
    alignItems: "flex-start",
    // backgroundColor:"red",
  },
  balanceText: {
    fontSize: 12,
    color: "rgba(22, 24, 23, 0.6)",
  },
  balanceAmount: {
    fontSize: 24,
    // fontWeight: "bold",
    fontFamily: "poppins-semibold",
    color: "rgba(22, 24, 23, 1)",
  },

  cc: {
    width: "90%",
    alignSelf: "center",
    alignItems: "center",
  },
  actionCard: {
    width: "90%",
    borderRadius: 12,
    alignSelf: "center",
    backgroundColor: colors.white,
    alignItems: "center",
    marginTop: (32 / baseHeight) * height,
  },
});
