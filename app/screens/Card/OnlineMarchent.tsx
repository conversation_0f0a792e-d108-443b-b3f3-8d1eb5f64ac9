import React, { useState } from "react";
import { Dimensions, ScrollView, StyleSheet, View, Image } from "react-native";
import { LanguageContext } from "../../context/LanguageContext";
import i18n from "../../../i18n";
import P from "../../components/P";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import NoteComponent2 from "../../components/NoteComponent2";
import { SvgXml } from "react-native-svg";
import { fonts } from "../../config/Fonts";
import { svg } from "../../config/Svg";
import CustomSwitch from "../../components/CustomSwitch";

const { width, height } = Dimensions.get("window");
export default function OnlineMarchent({ navigation }) {
  const [block, setBlock] = useState(false);
  const marchants = [
    {
      img: require("../../assets/spotify.png"),
      name: "Spotify",
      des: i18n.t("om.marchent1"),
      status: "active",
    },
    {
      img: require("../../assets/apple.png"),
      name: "Apple",
      des: i18n.t("om.marchent1"),
      status: "block",
    },
  ];

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text={i18n.t("om.mom")} />
        <View style={{ width: "90%", alignSelf: "center" }}>
          <NoteComponent2 text={i18n.t("om.note")} />
        </View>
        <ScrollView>
          <View>
            {marchants.length === 0 ? (
              <>
                <View style={styles.emptyCont}>
                  <SvgXml xml={svg.emptyNoti} />
                  <P
                    style={{
                      fontFamily: fonts.poppinsMedium,
                      lineHeight: 21,
                      marginTop: 16,
                      fontSize: 12,
                    }}
                  >
                    {i18n.t("om.empty1")}
                  </P>
                  <P
                    style={{
                      fontSize: 12,
                      fontFamily: fonts.poppinsRegular,
                      color: colors.gray2,
                    }}
                  >
                    {i18n.t("om.empty2")}
                  </P>
                </View>
              </>
            ) : (
              <>
                <View style={{ width: "90%", alignSelf: "center" }}>
                  {marchants.map((item, index) => (
                    <View key={index} style={styles.cardItem}>
                      <Image
                        source={item.img}
                        style={{ width: 24, height: 24, marginRight: 12 }}
                      />
                      <View style={{ width: "70%" }}>
                        <View
                          style={{ flexDirection: "row", alignItems: "center" }}
                        >
                          <P style={styles.name}>{item.name}</P>
                          {item.status === "block" && (
                            <P
                              style={{
                                marginLeft: 8,
                                padding: 8,
                                paddingTop: 6,
                                paddingBottom: 6,
                                backgroundColor: colors.lowOpFaild,
                                fontSize: 12,
                                borderRadius: 100,
                                color: colors.red,
                              }}
                            >
                              Block
                            </P>
                          )}
                        </View>

                        <P style={styles.des}>{item.des}</P>
                      </View>
                      <View style={{ position: "absolute", right: 16 }}>
                        <CustomSwitch />
                      </View>
                    </View>
                  ))}
                </View>
              </>
            )}
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  emptyCont: {
    width: "100%",
    height: (65 * height) / 100,
    alignItems: "center",
    justifyContent: "center",
  },
  cardItem: {
    width: "100%",
    padding: 16,
    borderRadius: 12,
    backgroundColor: colors.white,
    marginTop: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  name: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
  },
  des: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
});
