import React, { useEffect, useRef, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import SendMoneyStatus from "../../components/SeendMoneyStatus";
import Link from "../../components/Link";
import { formatDate } from "../../components/FormatDate";
import ViewShot from "react-native-view-shot";
import * as MediaLibrary from "expo-media-library";
import * as Sharing from "expo-sharing";
import { GetcardById } from "../../RequestHandlers/Card";
import { countries } from "../../components/counties";
import { GetRateByCountry, GetRateById } from "../../RequestHandlers/Wallet";
import { GetcardByToken } from "../../RequestHandlers/Card";
import { useToast } from "../../context/ToastContext";
const { width, height } = Dimensions.get("window");

export default function CardReciept({ navigation, route }) {
  const { handleToast } = useToast();
  const { transactionType } = route.params;
  const { data } = route?.params || {};
  const { data2 } = route?.params || {};
  const viewShotRef = useRef(null);
  const [curDetails, setCurDetails] = useState<any>([]);
  const [cardNum, setCardNum] = useState(".....");
  const [symbol, setSymbol] = useState("");
  const [code, setCode] = useState("");
  const captureAndDownload = async () => {
    // Request media library permission
    const { status } = await MediaLibrary.requestPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permission required",
        "You need to grant permission to save the receipt."
      );
      return;
    }
    if (!viewShotRef.current) {
      Alert.alert("Error", "Unable to capture receipt.");
      return;
    }
    try {
      const uri = await viewShotRef.current.capture();
      const asset = await MediaLibrary.createAssetAsync(uri);
      // Try saving to the 'Download' folder, fallback to 'Pictures'
      try {
        await MediaLibrary.createAlbumAsync("Download", asset, false);
      } catch {
        await MediaLibrary.createAlbumAsync("Pictures", asset, false);
      }

      Alert.alert("Success", "Details saved to your gallery.");
      handleToast("Details downloaded", "success");
    } catch (error) {
      Alert.alert("Error", "Failed to save receipt.");
    }
  };
  const captureAndShare = async () => {
    try {
      // @ts-ignore
      const uri = await viewShotRef.current.capture();
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri);
      } else {
        alert("Sharing is not available on this device");
      }
    } catch (error) {
      console.error("Error capturing and sharing view:", error);
    }
  };
  function formatCardNumber(cardNumber) {
    const cardStr = String(cardNumber);
    if (cardStr.length < 8) {
      return "......";
    }
    return `${cardStr.slice(0, 4)} **** **** ${cardStr.slice(-4)}`;
  }
  const getCardById = async (id) => {
    // setLoader(true);
    try {
      const cardDetails = await GetcardById(id);
      if (cardDetails) {
      }
      if (cardDetails?.details) {
        setCardNum(cardDetails?.details?.card_number);
      }
    } catch (error) {}
  };
  // const getCardByToken = async (token) => {
  //   try {
  //     const details = await GetcardByToken(token);
  //     if (details) {
  //       setCardNum(details?.data?.card_number);
  //     }
  //   } catch (error) {
  //     ;
  //   }
  // };
  const getYellowCardCode = (countryName) => {
    if (countryName === "Turkey" || countryName === "North Cyprus") {
      return "TRY"; // Assuming "TRY" is the Yellow Card Code for Turkey/North Cyprus
    }
    const country = countries.find((item) => item.country === countryName);
    return country ? country.currencyCode : "Country not found";
  };

  const getSymbol = (symbol) => {
    if (symbol === "TRY") {
      return "₺";
    }
    const curSymbol = countries.find((item) => item.currencyCode === symbol);
    return curSymbol ? curSymbol.symbol : "symbol not found";
  };
  //   const yc = getYellowCardCode(yellowCardCode);
  const getRateById = async (yc, type) => {
    const provider =
      yc === "NGN" ? "link" : yc === "TRY" ? "tcmb" : "yellow-card";
    try {
      const sfxRate = await GetRateByCountry(yc, provider);
      const rate = await GetRateById(yc);
      sfxRate.map((item, index) => {
        if (item.type === type) {
          setCurDetails(item);
        }
      });
    } catch (error) {}
  };
  useEffect(() => {
    if (data) {
      getCardById(data?.card?.id);
      const yellowCardCode = getYellowCardCode(data?.user?.homeCountry);
      const symbol = getSymbol(yellowCardCode);
      setSymbol(symbol);
      setCode(yellowCardCode);
      if (data.type === "fund-card") {
        getRateById(yellowCardCode, "buy");
      } else {
        getRateById(yellowCardCode, "sell");
      }
    }
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Transaction receipt"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <View>
                <ViewShot
                  ref={viewShotRef}
                  options={{ format: "jpg", quality: 0.9 }}
                >
                  <DetailCard
                    image={
                      <Image
                        source={require("../../assets/sfx2.png")}
                        style={{
                          width: 66.98,
                          height: 24,
                          objectFit: "contain",
                          marginBottom: 24,
                        }}
                      />
                    }
                    amount={
                      <>
                        <P
                          style={{
                            fontSize: 24,
                            lineHeight: 36,
                            marginRight: 2,
                          }}
                        >
                          ${data?.amount?.toLocaleString()}
                        </P>
                        <P style={{ marginTop: 5 }}>USD</P>
                      </>
                    }
                    convertedAmount={
                      <>
                        <P
                          style={{
                            fontSize: 16,
                            lineHeight: 24,
                            marginRight: 2,
                          }}
                        >
                          {symbol}
                          {(
                            data?.amount * curDetails?.amount
                          )?.toLocaleString() || 0.0}
                        </P>
                        <P
                          style={{
                            marginTop: 2,
                            fontSize: 12,
                            lineHeight: 18,
                          }}
                        >
                          {code}
                        </P>
                      </>
                    }
                    lineStyle={{ borderStyle: "dashed", marginTop: 24 }}
                    bottomComponent={
                      <View style={styles.desCont}>
                        <View
                          style={{
                            paddingBottom: 24,
                            borderBottomWidth: 1,
                            borderColor: colors.stroke,
                            borderStyle: "dashed",
                          }}
                        >
                          <View style={styles.items}>
                            <P style={styles.holder}>Recipient</P>
                            <View style={{ justifyContent: "flex-end" }}>
                              {/* @ts-ignore */}
                              <P style={[styles.value, { width: 200 }]}>
                                {data?.user?.firstName} {data?.user?.lastName}
                              </P>
                              <P
                                // @ts-ignore
                                style={[
                                  styles.value,
                                  {
                                    width: "100%",
                                    color: colors.gray,
                                    marginTop: 4,
                                    fontFamily: fonts.poppinsRegular,
                                  },
                                ]}
                              >
                                {formatCardNumber(cardNum)} |{" "}
                                {data?.card?.type === "virtual"
                                  ? "Virtual card"
                                  : "Physical card"}
                              </P>
                            </View>
                          </View>
                        </View>
                        <View style={{ paddingTop: 24 }}>
                          <View style={styles.items}>
                            <P style={styles.holder}>Reference number</P>
                            <View
                              style={{
                                flexDirection: "row",
                                width: 150,
                                justifyContent: "flex-end",
                              }}
                            >
                              <P
                                // @ts-ignore
                                style={[
                                  styles.value,
                                  { textAlign: "right", width: 120 },
                                ]}
                              >
                                {data?.ref}
                              </P>
                            </View>
                          </View>
                          <View style={styles.items}>
                            <P style={styles.holder}>Payment method</P>
                            <P style={styles.value}>
                              {" "}
                              {data?.card?.type === "virtual"
                                ? "Virtual card"
                                : "Physical card"}
                            </P>
                          </View>
                          <View style={[styles.items]}>
                            <P style={styles.holder}>Type</P>
                            <P style={styles.value}>
                              {data?.type === "fund-card"
                                ? "Top-up"
                                : data?.type === "withdraw-from-card"
                                ? "Send"
                                : "Spent"}
                            </P>
                          </View>
                          <View style={styles.items}>
                            <P style={styles.holder}>Timestamp</P>
                            <P style={styles.value}>
                              {formatDate(data?.updatedAt)}
                            </P>
                          </View>
                        </View>
                      </View>
                    }
                  />
                </ViewShot>
              </View>
              <View style={styles.buttonWrap}>
                <Button
                  btnText="Share receipt"
                  onPress={() => {
                    // navigation.navigate("BankTransferScreen");
                    // setShowSendStatus(true);
                    captureAndShare();
                  }}
                />
                <View
                  style={{
                    flexDirection: "row",
                    width: "100%",
                    alignItems: "center",
                    justifyContent: "center",
                    marginTop: 26,
                  }}
                >
                  <SvgXml xml={svg.download} style={{ marginRight: 4 }} />
                  <Link
                    style={{ fontSize: 12, lineHeight: 24 }}
                    onPress={() => {
                      captureAndDownload();
                    }}
                  >
                    Download receipt
                  </Link>
                </View>
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
    textAlign: "right",
    width: "60%",
    alignSelf: "flex-end",
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  indicatorCont: {
    padding: 19.5,
    paddingTop: 4,
    paddingBottom: 4,
    backgroundColor: colors.secBackground,
    marginTop: 8,
    borderRadius: 99,
    flexDirection: "row",
    alignItems: "center",
  },
  indicatorDot: {
    width: 8,
    height: 8,
    borderRadius: 99,
    marginRight: 4,
  },
});
