import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Linking,
  Image,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Input from "../../components/Input";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import * as Clipboard from "expo-clipboard";
import Link from "../../components/Link";
import NoteComponent2 from "../../components/NoteComponent2";
import BottomSheet from "../../components/BottomSheet";
import CountrySelect from "../../components/CountrySelect";
import CountryCodSelect from "../../components/CountryCodSelect";
import { countries } from "../../components/counties";

const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function PersonalInfo({ navigation }) {
  const [countryCode, setCountryCode] = useState("+90");
  const [countryCode2, setCountryCode2] = useState("Turkey");
  const [flag, setFlag] = useState(require("../../assets/turkey.png"));
  const [flag2, setFlag2] = useState(require("../../assets/turkey.png"));
  const [show, setShow] = useState(false);
  const [show2, setShow2] = useState(false);
  const [firstName, setFirstName] = useState("John");
  const [lastName, setLastName] = useState("Doe");
  const [address, setAddress] = useState(
    "14, Block A, Longson dormitory, eastern..."
  );
  // const [isAccVerified, setAccVerified] = useState(false);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Personal information"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentCard}>
            <View style={styles.section3Wrap}>
              <Input
                label="First name"
                placeholder="John"
                value={"John"}
                editable={false}
                inputStyle={{ width: "85%", color: colors.black }}
                contStyle={{ marginBottom: (16 / baseHeight) * height }}
                customInputStyle={styles.activeInput}
              />
              <Input
                label="Last name"
                placeholder="Doe"
                value={"Doe"}
                editable={false}
                inputStyle={{ width: "85%", color: colors.black }}
                contStyle={{ marginBottom: (16 / baseHeight) * height }}
                customInputStyle={styles.activeInput}
              />
              <Input
                label="Phone Number"
                placeholder="8144855058"
                inputStyle={{ width: "80%" }}
                contStyle={{ marginBottom: (16 / baseHeight) * height }}
                leftIcon={
                  <TouchableOpacity
                    style={[styles.pinInput]}
                    onPress={() => {
                      setShow(true);
                    }}
                  >
                    <Image
                      source={flag}
                      style={{
                        width: (24 / baseWidth) * width,
                        height: (24 / baseWidth) * width,
                        marginLeft: (16 / baseWidth) * width,
                        objectFit: "contain",
                      }}
                    />
                    <P style={styles.pinTextInput}>{countryCode}</P>
                    <SvgXml xml={svg.dropDown} />
                  </TouchableOpacity>
                }
              />

              <TouchableOpacity
                onPress={() => {
                  setShow2(true);
                }}
              >
                <Input
                  value={countryCode2}
                  label="Home Country"
                  placeholder="Turkey"
                  inputStyle={{ width: "80%", color: colors.black }}
                  contStyle={{ marginBottom: (16 / baseHeight) * height }}
                  leftIcon={
                    <Image
                      source={flag2}
                      style={{
                        width: (24 / baseWidth) * width,
                        height: (24 / baseWidth) * width,
                        marginLeft: (16 / baseWidth) * width,
                      }}
                    />
                    // <P style={styles.pinTextInput}>{flag2}</P>
                  }
                  editable={false}
                  rightIcon={
                    <View
                      style={{
                        //   backgroundColor: "red",
                        width: "10%",
                        height: "100%",
                        justifyContent: "center",
                        alignItems: "center",
                        // marginRight: ,
                      }}
                    >
                      <SvgXml xml={svg.dropDown} />
                    </View>
                  }
                />
              </TouchableOpacity>
              <Input
                label="Resident address"
                placeholder="Turkey"
                value={address}
                numberOfLines={1}
                inputStyle={{ width: "85%" }}
                contStyle={{ marginBottom: (16 / baseHeight) * height }}
              />
              <NoteComponent2
                text={
                  <P style={{ fontSize: 10, fontFamily: fonts.poppinsRegular }}>
                    By clicking continue you agree to SFx’s{" "}
                    <P
                      onPress={() => {}}
                      style={{
                        fontSize: 10,
                        textDecorationLine: "underline",
                        fontFamily: fonts.poppinsMedium,
                      }}
                    >
                      Privacy Policy
                    </P>{" "}
                    &{" "}
                    <P
                      style={{ fontSize: 10, textDecorationLine: "underline" }}
                    >
                      Terms of Service.
                    </P>
                  </P>
                }
              />
            </View>
          </View>
          <View style={styles.btnCont}>
            <Button
              btnText={"Continue"}
              onPress={() => navigation.navigate("CardTypeScreen")}
            />
          </View>
        </ScrollView>
      </Div>
      <BottomSheet
        isVisible={show2}
        backspaceText={"Home country"}
        onClose={() => setShow2(false)}
        showBackArrow={false}
        components={
          <CountrySelect
            onPress={(h) => {
              setShow2(false);
              setCountryCode2(countries[h].country);
              setFlag2(countries[h].flag);
            }}
          />
        }
        modalContentStyle={{ height: "55%" }}
        extraModalStyle={{ height: "53%" }}
      />

      <BottomSheet
        isVisible={show}
        backspaceText={"Phone number"}
        onClose={() => setShow(false)}
        showBackArrow={false}
        components={
          <CountryCodSelect
            onPress={(h) => {
              setShow(false);
              setCountryCode(countries[h].countryCode);
              setFlag(countries[h].flag);
            }}
          />
        }
        modalContentStyle={{ height: "55%" }}
        extraModalStyle={{ height: "53%" }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentCard: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    // marginTop: 24,
    marginTop: (2.7 * height) / 100,
    // paddingTop: (24 / baseHeight) * height,
    paddingBottom: (24 / baseHeight) * height,
    paddingLeft: (16 / baseWidth) * width,
    paddingRight: (16 / baseWidth) * width,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  holder: {
    fontSize: 12,
    lineHeight: (18 / baseHeight) * height,
    color: colors.gray,
    marginBottom: (4 / baseHeight) * height,
  },
  value: {
    fontSize: 12,
    lineHeight: (18 / baseHeight) * height,
    color: colors.black,
  },
  copyBtn: {
    paddingTop: (4 / baseHeight) * height,
    paddingBottom: (4 / baseHeight) * height,
    padding: (13 / baseWidth) * width,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: (32 / baseHeight) * height,
    marginBottom: (16 / baseHeight) * height,
  },
  amt: {
    fontSize: 16,
    // fontSize: 32,
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  amtCur: {
    lineHeight: (24 / baseHeight) * height,
    fontFamily: fonts.poppinsMedium,
  },
  statusText: {
    fontSize: 10,
    lineHeight: 16,
    fontFamily: fonts.poppinsRegular,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginTop: (24 / baseHeight) * height,
    // paddingTop: (2.7 * height) / 100,
    borderTopWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  section3Wrap: {
    width: "100%",
    marginTop: (24 / baseHeight) * height,
    borderColor: colors.stroke,
    // paddingLeft: 16,
  },
  progressDesCont: {
    flexDirection: "row",
  },
  progTextHead: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  progTextBody: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: (32 / baseHeight) * height,
    marginBottom: (64 / baseHeight) * height,
    // marginTop: 42,
    // alignItems: 'center',
    // justifyContent: 'center',
  },
  pinInput: {
    width: "40%",
    height: "100%",
    alignItems: "center",
    flexDirection: "row",
    borderRightColor: "#E6E5E5",
    borderRightWidth: 1,
  },
  pinTextInput: {
    fontSize: 14,
    textAlign: "center",
    color: "#161817",
    fontFamily: fonts.poppinsMedium,
    marginLeft: 16,
    marginRight: 8,
  },
  activeInput: {
    backgroundColor: colors.secBackground,
    borderWidth: 0,
  },
});
