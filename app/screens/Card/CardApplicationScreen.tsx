import React, { useEffect, useState } from "react";
import {
  Dimensions,
  ScrollView,
  StyleSheet,
  View,
  Image,
  ImageBackground,
  TouchableOpacity,
  Platform,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import P from "../../components/P";
import { svg } from "../../config/Svg";
import { SvgXml } from "react-native-svg";
import Button from "../../components/Button";
import { GetUserDetails } from "../../RequestHandlers/User";
import Loader from "../../components/ActivityIndicator";
import { CheckId } from "../../RequestHandlers/User";
import ExtraId from "../../components/ExtraId";
import ExtraId2 from "../../components/ExtraId2";
import RNFS from "react-native-fs";
import Selfie from "./Selfie";

const baseHeight = 802;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function CardApplicationScreen({ navigation }) {
  const [selectedCard, setSelectedCard] = useState(null);
  const [isAccVerified, setIsAccVerified] = useState(false);
  const [disabled, setDisabled] = useState(false);
  const [itProps, setItprops] = useState<any>([]);
  const [loader, setLoader] = useState(false);
  const [showExtraID, setExtraId] = useState(false);
  const [showBvnNinAdd, setShowBvnNinAdd] = useState(false);
  const [homeCountry, setHomeCountry] = useState("");
  const [isSelfieAdded, setIsSelfieAdded] = useState(false);
  const [showSelfie, setShowSelfie] = useState(false);
  const [isCardCreated, setIsCardCreated] = useState(false);
  const formatNumberWithDecimal = (value, decimalPlaces = 2) => {
    if (!isNaN(value)) {
      return Number(value)
        .toFixed(decimalPlaces)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    return "0.00";
  };

  const cards = [
    {
      img: require("../../assets/tinyCard.png"),
      type: "Virtual card",
      des: "Start making online purchases and managing your finances from anywhere.",
      cardFee: 5,
      type1: "virtual",
    },
    // {
    //   img: require("../../assets/nullCard.png"),
    //   type: "Physical card",
    //   des: "We’re excited to announce that our physical SFx Card will be launching soon!",
    //   type1: "physical",
    //   //   cardFee: 10.0,
    // },
  ];

  const getUserDetails = async () => {
    setLoader(true);
    try {
      const details = await GetUserDetails();
      if (details.cards.length === 0) {
        setIsCardCreated(false);
      } else {
        setIsCardCreated(true);
      }
      if (details) {
        if (details.verified === "true") {
          setIsAccVerified(true);
        } else {
          setIsAccVerified(false);
        }
        setHomeCountry(details.homeCountry);
      }
    } catch (error) {
      ;
    } finally {
      setLoader(false);
    }
  };
  const checkId = async () => {
    try {
      const res = await CheckId();
      if (res.bvn) {
        setExtraId(false);
      } else {
        setExtraId(true);
      }
      if (res.selfie) {
        setIsSelfieAdded(true);
      } else {
        setIsSelfieAdded(false);
      }
    } catch (error) {
      ;
    }
  };

  useEffect(() => {
    checkId();
    getUserDetails();
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Card" navigation={navigation} />
        <ScrollView>
          <View style={styles.textWrap}>
            <H4
              style={{ textAlign: "center", fontFamily: fonts.poppinsSemibold }}
            >
              Card application
            </H4>
            <P style={styles.textP}>
              Get an sfx card and shop at over 44M Mastercard enabled online
              merchants world wide
            </P>
          </View>
          <View style={styles.cards}>
            {cards.map((item, index) => {
              return (
                <TouchableOpacity
                  key={index}
                  onPress={() => {
                    if (item.des.includes("launching soon")) {
                      navigation.navigate("PhysicalCardPromt");
                    } else {
                      if (index === selectedCard) {
                        setSelectedCard(null);
                      } else {
                        setSelectedCard(index);
                        setItprops(item);
                      }
                    }
                  }}
                >
                  <View
                    style={{
                      padding: (16 / baseWidth) * width,
                      backgroundColor: colors.white,
                      marginBottom: (16 / baseHeight) * height,
                      borderRadius: 16,
                      borderColor: colors.primary,
                      borderWidth: index === selectedCard ? 1 : 0,
                    }}
                  >
                    {index === selectedCard && (
                      <ImageBackground
                        style={{
                          width: 48,
                          height: 48,
                          position: "absolute",
                          right: -1,
                          top: -1,
                          alignItems: "flex-end",
                          justifyContent: "center",
                        }}
                        borderTopRightRadius={16}
                        source={require("../../assets/triangle.png")}
                      >
                        <SvgXml
                          xml={svg.mark}
                          style={{ marginTop: -20, marginRight: 10 }}
                        />
                      </ImageBackground>
                    )}

                    <View style={{ width: "100%" }}>
                      {!item.des.includes("launching soon") && (
                        <View
                          style={{
                            position: "absolute",
                            minWidth: 66,
                            height: 36,
                            right: (8 / baseWidth) * width,
                            top: (8 / baseHeight) * height,
                            alignItems: "flex-end",
                          }}
                        >
                          <P
                            style={{
                              fontSize: (10 / baseWidth) * width,
                              color: colors.gray,
                              fontFamily: fonts.poppinsRegular,
                            }}
                          >
                            Card fee
                          </P>
                          <P
                            style={{
                              fontSize: (12 / baseWidth) * width,
                              color: colors.green,
                            }}
                          >{`$${formatNumberWithDecimal(item.cardFee)} USD`}</P>
                        </View>
                      )}
                      <Image source={item.img} style={styles.tinyImg} />
                      <P
                        style={{
                          lineHeight: (21 / baseHeight) * height,
                        }}
                      >
                        {item.type}
                      </P>
                      <P
                        style={{
                          marginTop: (4 / baseHeight) * height,
                          fontSize: (12 / baseWidth) * width,
                          color: colors.gray,
                          fontFamily: fonts.poppinsRegular,
                        }}
                      >
                        {item.des}
                      </P>
                      <View
                        style={{
                          flexDirection: "row",
                          marginTop: (8 / baseHeight) * height,
                        }}
                      >
                        <Image
                          source={require("../../assets/masterCard.png")}
                          style={styles.cardBrand}
                        />
                      </View>
                    </View>
                  </View>
                </TouchableOpacity>
              );
            })}
            <View style={styles.btnCont}>
              <Button
                btnText="Apply card"
                disabled={selectedCard === null}
                onPress={() => {
                  if (isAccVerified) {
                    if (homeCountry === "Nigeria") {
                      // Nigerian users
                      if (!isSelfieAdded && showExtraID) {
                        // No selfie and no BVN
                        setShowBvnNinAdd(true);
                      } else if (showExtraID) {
                        // No BVN
                        setShowBvnNinAdd(true);
                      } else if (!isSelfieAdded) {
                        // No selfie
                        // setShowBvnNinAdd(true);
                        setShowSelfie(true);
                      } else {
                        if (isCardCreated) {
                          navigation.navigate("CardTypeScreen", {
                            fee: itProps?.cardFee,
                            type: itProps?.type1,
                          });
                        } else {
                          navigation.navigate("CardAddNameScreen", {
                            fee: itProps?.cardFee,
                            type: itProps?.type1,
                          });
                        }
                      }
                    } else {
                      if (!isSelfieAdded) {
                        setShowSelfie(true);
                      } else {
                        if (isCardCreated) {
                          navigation.navigate("CardTypeScreen", {
                            fee: itProps?.cardFee,
                            type: itProps?.type1,
                          });
                        } else {
                          navigation.navigate("CardAddNameScreen", {
                            fee: itProps?.cardFee,
                            type: itProps?.type1,
                          });
                        }
                      }
                    }
                  } else {
                    navigation.navigate("AccountVerificationPromt");
                  }
                }}
              />
            </View>
          </View>
        </ScrollView>
      </Div>
      {showSelfie && (
        <Selfie
          close={() => {
            setShowSelfie(false);
          }}
          extraFunction={() => {
            setShowSelfie(false);
            navigation.navigate("CardTypeScreen", {
              fee: itProps?.cardFee,
              type: itProps?.type1,
            });
          }}
        />
      )}
      {showBvnNinAdd && (
        <ExtraId2
          extraFunction={() => {
            setShowBvnNinAdd(false);
            if (!isSelfieAdded) {
              setShowSelfie(true);
            } else {
              navigation.navigate("CardTypeScreen", {
                fee: itProps?.cardFee,
                type: itProps?.type1,
              });
            }
          }}
          close={() => {
            setShowBvnNinAdd(false);
          }}
        />
      )}
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  content: {
    width: "90%",
    alignSelf: "center",
  },
  btnCont: {
    width: (264 / baseWidth) * width,
    alignSelf: "center",
    marginTop: (24 / baseHeight) * height,
    marginBottom: (48 / baseHeight) * height,
  },
  textWrap: {
    width: "90%",
    alignSelf: "center",
    marginTop: (16 / baseHeight) * height,
  },
  textP: {
    fontSize: (12 / baseWidth) * width,
    color: colors.gray,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    marginTop: (8 / baseHeight) * height,
  },
  cards: {
    width: "90%",
    alignSelf: "center",
    marginTop: (24 / baseHeight) * height,
  },
  tinyImg: {
    width: 53.1,
    height: 32,
    objectFit: "contain",
    marginBottom: (8 / baseHeight) * height,
  },
  cardBrand: {
    width: 34,
    height: 24,
    marginRight: (8 / baseWidth) * width,
  },
  selfieCont: {
    paddingTop: Platform.OS === "ios" ? 50 : 30,
    width: "100%",
    zIndex: 100,
    position: "absolute",
    height,
    backgroundColor: colors.secBackground,
  },
});
