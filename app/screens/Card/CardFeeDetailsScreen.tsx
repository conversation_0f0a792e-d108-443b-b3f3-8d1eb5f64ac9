import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import SendMoneyStatus from "../../components/SeendMoneyStatus";
import Content2 from "../../components/Content2";
import NoteComponent from "../../components/NoteComponent";
import CustomSwitch from "../../components/CustomSwitch";
import { GetUserDetails } from "../../RequestHandlers/User";
import { setLocale } from "yup";
import Loader from "../../components/ActivityIndicator";
import { GetUserWallet } from "../../RequestHandlers/Wallet";

const { width, height } = Dimensions.get("window");

export default function CardFeeDetailsScreen({ navigation, route }) {
  //   const { country } = route.params;
  const [selectedAcc, setSelectedAcc] = useState(null);
  const [details, setDetails] = useState<any>([]);
  const [loader, setLoader] = useState(false);
  const { fee, pin, type, color } = route?.params || "";
  const { name } = route?.params || {};
  const [walletId, setWalletId] = useState("");
  const [accounts, setAccounts] = useState([
    // {
    //   id: "",
    //   balance: 3500,
    //   currency: "USDT",
    //   exchangeRate: "1 USDT ~ 1 USD",
    //   type: "Tether",
    // },
    {
      id: "",
      balance: 0,
      currency: "USDC",
      exchangeRate: "1 USDC ~ 1 USD",
      type: "USDC",
    },
  ]);

  const getUserDetails = async () => {
    setLoader(true);
    try {
      const details = await GetUserDetails();
      if (details.email) {
        setDetails(details);
        setLoader(false);
      } else {
        setLoader(false);
      }
    } catch (error) {}
  };

  const getUserWallet = async () => {
    try {
      const wallet = await GetUserWallet();
      if (wallet?.wallets) {
        setAccounts([
          // {
          //   id: wallet?.wallets[0]?.id,
          //   balance: wallet?.wallets[0]?.balance?.toFixed(2),
          //   currency: wallet?.wallets[0]?.asset,
          //   exchangeRate: "1 USDT ~ 1 USD",
          //   type: "Tether",
          // },
          {
            id: wallet?.wallets[0]?.id,
            balance: wallet?.wallets[0]?.balance,
            currency: wallet?.wallets[0]?.asset,
            exchangeRate: "1 USDC ~ 1 USD",
            type: "USDC",
          },
        ]);
        setWalletId(wallet?.wallets[0]?.id);
      }
    } catch (error) {}
  };
  function capitalizeFirstLetter(string) {
    if (!string) return "";
    return string.charAt(0).toUpperCase() + string.slice(1);
  }
  useEffect(() => {
    getUserDetails();
    getUserWallet();
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Card fee details" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <DetailCard
                headText={"Money you’re paying"}
                amount={
                  <>
                    <P style={{ fontSize: 32, lineHeight: 48, marginRight: 2 }}>
                      ${fee}
                    </P>
                    <P style={{ marginTop: 5 }}>USD</P>
                  </>
                }
                bottomComponent={
                  <View style={styles.desCont}>
                    <View style={styles.items}>
                      <P style={styles.holder}>First name</P>
                      <P style={styles.value}>{details?.firstName}</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Last name</P>
                      <P style={styles.value}>{details?.lastName}</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Phone number</P>
                      <P style={styles.value}>{details?.phoneNumber}</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Home country</P>
                      <P style={styles.value}>{details?.homeCountry}</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Card color</P>
                      <P style={styles.value}>{capitalizeFirstLetter(color)}</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Resident address</P>
                      <P style={styles.value}>{details?.residentAddress}</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Creation fee</P>
                      <P style={styles.value}>
                        4{" "}
                        <P
                          // @ts-ignore
                          style={[
                            styles.value,
                            { fontFamily: fonts.poppinsRegular },
                          ]}
                        >
                          USD
                        </P>
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Card Top up</P>
                      <P style={styles.value}>
                        1{" "}
                        <P
                          // @ts-ignore
                          style={[
                            styles.value,
                            { fontFamily: fonts.poppinsRegular },
                          ]}
                        >
                          USD
                        </P>
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Payment method</P>
                      <P style={styles.value}>Card</P>
                    </View>
                    <View style={[styles.items]}>
                      <P style={styles.holder}>Type</P>
                      <P style={styles.value}>Card fee</P>
                    </View>
                    {/* <View style={[styles.items, { marginBottom: 0 }]}>
                      <P style={styles.holder}>Earn SFx point</P>
                      <P style={styles.value}>$2</P>
                    </View> */}
                    {/* <View style={styles.line}></View>

                    <P
                      style={{
                        color: "#A5A1A1",
                        fontSize: 12,
                        textAlign: "center",
                        marginBottom: 6,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      Select payment method
                    </P> */}
                    {/* <View
                      style={{
                        width: "97%",
                        flexDirection: "row",
                        justifyContent: "space-between",
                        marginBottom: 16,
                      }}
                    >
                      <P style={{ color: "#A5A1A1", fontSize: 12 }}>
                        SFx point
                      </P>
                      <View style={{ flexDirection: "row" }}>
                        <P style={{ marginRight: 8 }}>$50</P>
                        <CustomSwitch />
                      </View>
                    </View> */}
                    {/* {accounts.map((item, index) => (
                      <Content2
                        key={index}
                        svgg={
                          item.currency === "USDT" ? svg.tather : svg.usdCoin
                        }
                        onclick={index === selectedAcc}
                        ClickedMe={() => {
                          if (selectedAcc === index) {
                            setSelectedAcc(null);
                          } else if (item.balance > 0) {
                            setSelectedAcc(index);
                            setWalletId(item?.id);
                          }
                        }}
                        header={
                          <>
                            <P
                              style={{
                                fontSize: 12,
                                lineHeight: 18,
                                fontFamily: fonts.poppinsMedium,
                              }}
                            >
                              {item.balance}
                            </P>{" "}
                            <P
                              style={{
                                fontSize: 12,
                                lineHeight: 18,
                                fontFamily: fonts.poppinsRegular,
                                color: colors.gray,
                              }}
                            >
                              {item.currency}
                            </P>
                          </>
                        }
                        body={item.exchangeRate}
                        containerStyle={{
                          justifyContent: "flex-start",
                          paddingLeft: 16,
                        }}
                        itemWrapper={{ marginLeft: 8 }}
                        headerStyle={{ marginBottom: 4 }}
                        textStyle={{
                          fontFamily: fonts.poppinsRegular,
                          fontSize: 12,
                        }}
                        rightComponent={
                          item.balance === 0 && (
                            <Button
                              btnText="Add money"
                              btnTextStyle={{ color: colors.primary }}
                              type="alt"
                              style={{ width: "80%", height: "50%" }}
                              onPress={() => {
                                navigation.navigate("AddMoneyScreen");
                              }}
                            />
                          )
                        }
                      />
                    ))} */}
                  </View>
                }
              />
              <View style={styles.buttonWrap}>
                <Button
                  btnText="Confirm"
                  onPress={() => {
                    navigation.navigate("CardPayPinScreen", {
                      fee: fee,
                      pin: pin,
                      color: color,
                      type: type,
                      walletId: walletId,
                      name: name,
                    });
                    // setShowSendStatus(true);
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    // height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    marginBottom: 40,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
    width: "50%",
    textAlign: "right",
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
    marginBottom: 40,
  },
  line: {
    width: "100%",
    height: 1,
    borderWidth: 1,
    borderStyle: "dashed",
    borderColor: colors.stroke,
    marginTop: (3.5 * height) / 100,
    marginBottom: (3.5 * height) / 100,
  },
});
