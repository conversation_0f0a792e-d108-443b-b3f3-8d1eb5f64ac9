import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import SendMoneyStatus from "../../components/SeendMoneyStatus";
import Content2 from "../../components/Content2";
import NoteComponent from "../../components/NoteComponent";
import NoteComponent2 from "../../components/NoteComponent2";
import { GetUserWallet } from "../../RequestHandlers/Wallet";
import { convertToTitleCase } from "../../components/ConvertToTitlecase";
import Loader from "../../components/ActivityIndicator";
import { useToast } from "../../context/ToastContext";

const { width, height } = Dimensions.get("window");

export default function CardTopUpDetails({ navigation, route }) {
  const { details, ngnRate, code, symbol, amount } = route?.params;
  //   const { country } = route.params;
  const [showSendStatus, setShowSendStatus] = useState(false);
  const [selectedAcc, setSelectedAcc] = useState(null);
  const [loader, setLoader] = useState(false);
  const [wallet, setWallet] = useState(null);
  const { handleToast } = useToast();
  const [accounts, setAccounts] = useState([
    // {
    //   id: 1,
    //   balance: 3500,
    //   currency: "USDT",
    //   exchangeRate: "1 USDT ~ 1 USD",
    //   type: "Tether",
    // },
    {
      id: 1,
      balance: 0,
      currency: "USDT",
      exchangeRate: "1 USDC ~ 1 USD",
      type: "USDC",
    },
  ]);
  function formatCardNumber(cardNumber) {
    const cardStr = String(cardNumber);
    if (cardStr.length < 8) {
      return "Invalid card number";
    }
    return `${cardStr.slice(0, 4)} **** **** ${cardStr.slice(-4)}`;
  }
  const getUserWallets = async () => {
    setLoader(true);
    try {
      const wallet = await GetUserWallet();
      if (wallet.wallets) {
        setLoader(false);
        setAccounts([
          // {
          //   id: wallet?.wallets[0]?.id,
          //   balance: wallet?.wallets[0]?.balance?.toFixed(2),
          //   currency: wallet?.wallets[0]?.asset,
          //   exchangeRate: "1 USDT ~ 1 USD",
          //   type: "Tether",
          // },
          {
            id: wallet?.wallets[0]?.id,
            balance: wallet?.wallets[0]?.balance?.toFixed(2),
            currency: wallet?.wallets[0]?.asset,
            exchangeRate: "1 USDc ~ 1 USD",
            type: "USDC",
          },
        ]);
        setWallet(wallet?.wallets[0]);
      } else {
        setLoader(false);
      }
    } catch (error) {}
  };
  useEffect(() => {
    getUserWallets();
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Transaction details"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <DetailCard
                headText={"Money you’re paying"}
                amount={
                  <>
                    <P style={{ fontSize: 32, lineHeight: 48, marginRight: 2 }}>
                      ${(amount + 1)?.toFixed(2)?.toLocaleString()}
                    </P>
                    <P style={{ marginTop: 5 }}>USD</P>
                  </>
                }
                convertedAmount={
                  <>
                    <P style={{ fontSize: 16, lineHeight: 24, marginRight: 2 }}>
                      {symbol}
                      {(amount * ngnRate)?.toLocaleString()}
                    </P>
                    <P style={{ marginTop: 2, fontSize: 12, lineHeight: 18 }}>
                      {code}
                    </P>
                  </>
                }
                bottomComponent={
                  <View style={styles.desCont}>
                    <View style={styles.items}>
                      <P style={styles.holder}>Card holder</P>
                      <P style={styles.value}>{details?.details?.card_name}</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Card number</P>
                      <P style={styles.value}>
                        {" "}
                        {formatCardNumber(details?.details?.card_number)}
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Card type</P>
                      <P style={styles.value}>
                        {" "}
                        {convertToTitleCase(details?.card?.type)}
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Fee</P>
                      <P style={styles.value}>
                        1{" "}
                        <P
                          // @ts-ignore
                          style={[
                            styles.value,
                            { fontFamily: fonts.poppinsRegular },
                          ]}
                        >
                          USD
                        </P>
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Exchange rate</P>
                      <P style={styles.value}>
                        1 USD ~ {ngnRate} {code}
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Payment method</P>
                      <P style={styles.value}>Virtual card</P>
                    </View>
                    <View style={[styles.items, { marginBottom: 0 }]}>
                      <P style={styles.holder}>Type</P>
                      <P style={styles.value}>Top-up</P>
                    </View>
                    {/* <View style={styles.line}></View>

                    <P
                      style={{
                        color: "#A5A1A1",
                        fontSize: 12,
                        textAlign: "center",
                        marginBottom: 6,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      Send money to
                    </P> */}
                    {/* <View
                      style={{
                        width: "100%",
                        flexDirection: "row",
                        justifyContent: "space-between",
                        marginBottom: 16,
                      }}
                    >
                      <P style={{ color: "#A5A1A1", fontSize: 12 }}>
                        SFx point
                      </P>
                      <View style={{ flexDirection: "row" }}>
                        <P style={{ marginRight: 8 }}>$50</P>
                        <CustomSwitch />
                      </View>
                    </View> */}
                    {/* {selectedAcc !== null && (
                      <View style={{ marginBottom: 16 }}>
                        <NoteComponent2
                          text={`Your card can only be top-up from your  ${
                            wallet?.type === "USDC" ? "USD coin" : wallet?.type
                          } (${wallet?.currency}) account`}
                        />
                      </View>
                    )} */}
                    {/* {accounts.map((item, index) => (
                      <Content2
                        svgg={
                          item.currency === "USDT" ? svg.tather : svg.usdCoin
                        }
                        onclick={index === selectedAcc}
                        ClickedMe={() => {
                          if (selectedAcc === index) {
                            setSelectedAcc(null);
                          } else if (item.balance > 0) {
                            setSelectedAcc(index);
                            setWallet(item);
                          }
                        }}
                        header={
                          <>
                            <P
                              style={{
                                fontSize: 12,
                                lineHeight: 18,
                                fontFamily: fonts.poppinsMedium,
                              }}
                            >
                              {item.balance}
                            </P>{" "}
                            <P
                              style={{
                                fontSize: 12,
                                lineHeight: 18,
                                fontFamily: fonts.poppinsRegular,
                                color: colors.gray,
                              }}
                            >
                              {item.currency}
                            </P>
                          </>
                        }
                        body={item.exchangeRate}
                        containerStyle={{
                          width: "100%",
                          justifyContent: "flex-start",
                          paddingLeft: 16,
                          backgroundColor: "transparent",
                        }}
                        itemWrapper={{ marginLeft: 8 }}
                        headerStyle={{ marginBottom: 4 }}
                        textStyle={{
                          fontFamily: fonts.poppinsRegular,
                          fontSize: 12,
                        }}
                        rightComponent={
                          item.balance === 0 && (
                            <Button
                              btnText="Add money"
                              btnTextStyle={{ color: colors.primary }}
                              type="alt"
                              style={{ width: "80%", height: "50%" }}
                              onPress={() => {
                                navigation.navigate("AddMoneyScreen");
                              }}
                            />
                          )
                        }
                      />
                    ))} */}
                  </View>
                }
              />
              <View style={styles.buttonWrap}>
                <Button
                  btnText="Confirm"
                  onPress={() => {
                    if (selectedAcc === null) {
                      handleToast("Select a wallet", "error");
                    } else {
                      navigation.navigate("TransactionPinScreen2", {
                        details: details,
                        walletId: wallet?.id,
                        amount: amount,
                      });
                    }
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    // height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
    width: "50%",
    textAlign: "right",
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
    marginBottom: 40,
  },
  line: {
    width: "100%",
    // height: 1,
    borderBottomWidth: 1,
    borderStyle: "dashed",
    borderColor: colors.stroke,
    marginTop: (3.5 * height) / 100,
    marginBottom: (3.5 * height) / 100,
  },
});
