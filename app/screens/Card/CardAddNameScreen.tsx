import React from "react";
import { Platform, StyleSheet, View, Keyboard, Linking } from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHeader from "../../components/AuthenticationHedear";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Dash from "../../components/Dash";
import { Formik } from "formik";
import * as yup from "yup";
import Input from "../../components/Input";
import { Text } from "react-native";
import Button from "../../components/Button";

export function CardAddNameScreen({ navigation, route }) {
  const { fee } = route?.params || 0;
  const { type } = route?.params || "";

  const nameScheme = yup.object().shape({
    first_name: yup.string().required("First name is required"),
    last_name: yup.string().required("Last name is required"),
  });
  return (
    <View style={{ flex: 1, backgroundColor: colors.secBackground }}>
      <Div>
        <Formik
          initialValues={{
            first_name: "",
            last_name: "",
          }}
          validationSchema={nameScheme}
          onSubmit={async (values, actions) => {
            Keyboard.dismiss();
            const trimmedValues = {
              first_name: values.first_name.trim(),
              last_name: values.last_name.trim(),
            };
            navigation.navigate("CardTypeScreen", {
              fee: fee,
              type: type,
              name: trimmedValues,
            });
          }}
        >
          {(formikProps) => (
            <>
              <AuthenticationHeader
                text="Card registration"
                navigation={navigation}
              />
              <View style={styles.form}>
                <P style={styles.hText}>Secure card registration</P>
                <P style={styles.pText}>
                  Provide the name associated with your bank verification
                  number(BVN) below
                </P>
                {Platform.OS === "ios" ? (
                  <View style={styles.mt}>
                    <Dash />
                  </View>
                ) : (
                  <View style={styles.dash}></View>
                )}
                <View style={{ marginBottom: 16 }}>
                  <Input
                    label={"First name"}
                    placeholder="Doe"
                    onChangeText={formikProps.handleChange("first_name")}
                    value={formikProps.values.first_name}
                    onBlur={formikProps.handleBlur("first_name")}
                    autoCapitalize="none"
                    error={
                      formikProps.errors.first_name &&
                      formikProps.touched.first_name
                    }
                  />
                  {formikProps.errors.first_name &&
                    formikProps.touched.first_name && (
                      <P style={styles.errorText}>
                        {formikProps.errors.first_name}
                      </P>
                    )}
                </View>
                <View>
                  <Input
                    label={"Last name"}
                    placeholder="John"
                    onChangeText={formikProps.handleChange("last_name")}
                    value={formikProps.values.last_name}
                    onBlur={formikProps.handleBlur("last_name")}
                    autoCapitalize="none"
                    error={
                      formikProps.errors.last_name &&
                      formikProps.touched.last_name
                    }
                  />
                  {formikProps.errors.last_name &&
                    formikProps.touched.last_name && (
                      <P style={styles.errorText}>
                        {formikProps.errors.last_name}
                      </P>
                    )}
                </View>
              </View>

              <View
                style={{
                  width: "90%",
                  alignSelf: "center",
                  paddingLeft: 20,
                  paddingRight: 20,
                }}
              >
                <P
                  style={{
                    fontSize: 12,
                    lineHeight: 19.2,
                    color: "#A5A1A1",
                    marginBottom: 32,
                    marginTop: 16,
                    textAlign: "center",
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  By clicking next you agree to SFx’s{" "}
                  <Text
                    style={{
                      textDecorationColor: "#A5A1A1",
                      textDecorationLine: "underline",
                      color: colors.primary,
                      fontFamily: fonts.poppinsMedium,
                    }}
                    onPress={() => {
                      Linking.openURL(
                        "https://sfx-1.gitbook.io/legal-and-policy/privacy-policy-for-sfx"
                      );
                    }}
                  >
                    Privacy Policy
                  </Text>{" "}
                  &{" "}
                  <Text
                    style={{
                      textDecorationColor: "#A5A1A1",
                      textDecorationLine: "underline",
                      color: colors.primary,
                      fontFamily: fonts.poppinsMedium,
                    }}
                    onPress={() => {
                      Linking.openURL(
                        "https://sfx-1.gitbook.io/legal-and-policy/terms-and-conditions"
                      );
                    }}
                  >
                    Terms of Service.
                  </Text>
                </P>
                <View style={{ width: "85%", alignSelf: "center" }}>
                  <Button
                    btnText="Continue"
                    onPress={formikProps.handleSubmit}
                  />
                </View>
              </View>
            </>
          )}
        </Formik>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  form: {
    width: "90%",
    backgroundColor: colors.white,
    alignSelf: "center",
    padding: 24,
    marginTop: 16,
    borderRadius: 12,
  },
  hText: {
    textAlign: "center",
    fontSize: 16,
    lineHeight: 24,
    fontFamily: fonts.poppinsMedium,
    marginBottom: 8,
  },
  pText: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
    textAlign: "center",
  },
  dash: {
    marginTop: 16,
    marginBottom: 16,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  mt: {
    marginTop: 16,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
});
