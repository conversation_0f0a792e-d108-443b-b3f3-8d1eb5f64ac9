import React, { useCallback, useEffect, useState } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  StatusBar,
  Modal,
  BackHandler,
} from "react-native";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Link from "../../components/Link";
import { useNavigation, useFocusEffect } from "@react-navigation/native";
import {
  GetRateByCountry,
  GetTransationById,
} from "../../RequestHandlers/Wallet";
import Loader from "../../components/ActivityIndicator";
import { countries } from "../../components/counties";
import { GetRateById } from "../../RequestHandlers/Wallet";
interface PProps {
  okayPress?: any;
  viewDetailPress?: any;
  tranStat?: "failed" | "success" | "pending";
  visible?: true | false;
  requestClose?: any;
  from?: string;
}
const { width, height } = Dimensions.get("window");
export default function CardTopStatus({ navigation, route }) {
  const [stImg, setStImg] = useState(require("../../assets/alert-circle.png"));
  const [stText, setStText] = useState("Top-up is pending");
  const [tranStat, setTranState] = useState("pending");
  const { response } = route?.params || {};
  const { destination } = route?.params || "";
  const [tranDetails, setTranDetails] = useState<any>([]);
  const [yellowCardData, setYellowCardData] = useState<any>([]);
  const [loader, setLoader] = useState(false);
  const [localRate, setLocaleRate] = useState(0);
  const [curDetails, setCurDetails] = useState("");
  const [symbol, setSymbol] = useState("");
  const [code, setCode] = useState("");
  // const [tranStat, setTranStat] = useState("pending");
  const getYellowCardCode = (countryName) => {
    if (countryName === "Turkey" || countryName === "North Cyprus") {
      return "TRY";
    }
    const country = countries.find((item) => item.country === countryName);
    return country ? country.currencyCode : "Country not found";
  };

  const getSymbol = (currencyCode) => {
    if (currencyCode === "TRY") {
      return "₺";
    }
    const curSymbol = countries.find(
      (item) => item.currencyCode === currencyCode
    );
    return curSymbol ? curSymbol.symbol : "Symbol not found";
  };
  const getRateById = async (currencyCode) => {
    setLoader(true);
    const provider = currencyCode === "NGN" ? "link" : currencyCode === "TRY" ? "tcmb" : "yellow-card";
    try {
      const rate = await GetRateById(currencyCode);
      const sfxRate = await GetRateByCountry(currencyCode, provider);
      sfxRate.map((item, index) => {
        if (item?.type === "buy") {
          setLocaleRate(item.amount);
        }
      });
    
    } catch (error) {
      ;
    }
  };
  const getTransaction = async () => {
    try {
      const id = response?.id;
      const transaction = await GetTransationById(id);
      if (transaction) {
        setLoader(false);
        const yellowCardCode = getYellowCardCode(
          transaction?.transaction?.user?.homeCountry
        );
        const symbol = getSymbol(yellowCardCode);
        getRateById(yellowCardCode); // Pass yellowCardCode to getRateById
        setSymbol(symbol);
        setCode(yellowCardCode);
      }
      setTranDetails(transaction?.transaction);
      transaction?.transaction?.status === "completed"
        ? setTranState("success")
        : transaction?.transaction?.status === "failed"
        ? setTranState("failed")
        : setTranState("pending");
    } catch (error) {
      setLoader(false);
      ;
    }
  };

  useEffect(() => {
    if (tranStat === "failed") {
      setStImg(require("../../assets/cancel-circle.png"));
      setStText("Top-up money failed");
    } else if (tranStat === "success") {
      setStImg(require("../../assets/success.png"));
      setStText("Top-up successfully added");
    } else {
      setStImg(require("../../assets/alert-circle.png"));
    }
  }, [tranStat]);

  useEffect(() => {
    setLoader(true);
    getTransaction();
  }, []);
  useEffect(() => {
    const interval = setInterval(() => {
      getTransaction();
    }, 3000);

    return () => clearInterval(interval);
  }, []);
  useFocusEffect(useCallback(() => {
    const onBackPress = () => {
      navigation.navigate("CardScreen")
      return true;
    };
    // Disable iOS swipe back gesture
    navigation.setOptions({
      gestureEnabled: false
    });

    // Handle Android back button
    BackHandler.addEventListener("hardwareBackPress", onBackPress);

    return () => {
      BackHandler.removeEventListener("hardwareBackPress", onBackPress);
    };
  }, [navigation]))
  return (
    <View style={styles.body}>
      <View style={styles.itemBox}>
        <Image source={stImg} style={{ width: 64, height: 64 }} />
        <P style={styles.statusState}>{stText}</P>
        {tranStat === "failed" ? (
          <P style={styles.stTx}>
            Top-up sent failed due to technical issue,{"\n"}please try again
            later!
          </P>
        ) : tranStat === "success" ? (
          <P style={styles.stTx}>
            You have successfully top-up ${tranDetails?.amount} USD ~ {"\n"}
            {symbol} {(tranDetails?.amount * localRate).toLocaleString()} {code}{" "}
            to virtual card
          </P>
        ) : (
          <P style={styles.stTx}>
            Top-up is processing, please check{"\n"}money status later!
          </P>
        )}
        <View style={{ width: "75%", marginTop: 32 }}>
          <Button
            btnText="Okay!"
            onPress={() => {
              if (destination === "home") {
                navigation.navigate("Home");
              } else {
                navigation.navigate("CardScreen");
              }
            }}
          />
          <Link
            style={{ textAlign: "center", marginTop: 16, fontSize: 12 }}
            onPress={() => {
              const transactionType =
                tranDetails?.type === "internal-tranfer"
                  ? "sfx money app"
                  : tranDetails?.paymentGayway === "momo"
                  ? "mobile money"
                  : tranDetails?.paymentGayway === "bank"
                  ? "bank transfer"
                  : tranDetails?.type === "TRANSFER"
                  ? "p2p"
                  : "unknown"; // Fallback if none of the conditions are met
              const id =
                response?.id === undefined
                  ? response?.transaction?.id
                  : response?.id;
              navigation.navigate("CardTransactionDetails", {
                id: id,
                transactionType: transactionType,
              });
            }}
          >
            View details
          </Link>
        </View>
      </View>
      {/* {loader && <Loader />} */}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    width,
    height: (105 * height) / 100,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    bottom: 0,
    // top: 0,
    zIndex: 100,
  },
  itemBox: {
    width: "100%",
    alignItems: "center",
    // marginTop: (20*height)/100
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    fontSize: 12,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
});
