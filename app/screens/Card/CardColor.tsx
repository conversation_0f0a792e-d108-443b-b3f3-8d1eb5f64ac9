import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Linking,
  Image,
  ImageBackground,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import * as Clipboard from "expo-clipboard";
import Link from "../../components/Link";
import NoteComponent2 from "../../components/NoteComponent2";
import VirtualCard from "../../components/VirtualCard";
import { ChangeCardColor } from "../../RequestHandlers/Card";
import { GetcardByToken } from "../../RequestHandlers/Card";
import { GetcardById } from "../../RequestHandlers/Card";
import Loader from "../../components/ActivityIndicator";
import { useToast } from "../../context/ToastContext";

const baseHeight = 800;
const { width, height } = Dimensions.get("window");
export default function CardColor({ navigation, route }) {
  const [activeColor, setActiveColor] = useState("#8B52FF");
  const Colors = ["#8B52FF", "#914C2C", "#BD0000", "#22C26E"];
  const [cardColor, setCardColor] = useState("purple");
  const { cardID } = route?.params || "";
  const [loading, setLoading] = useState(false);
  const [loader, setLoader] = useState(false);
  const [exYear, setExYear] = useState("");
  const [cardDetails, setCardDetails] = useState<any>([]);
  const [details, setDetails] = useState<any>([]);
     const {handleToast} = useToast()

  const changeCardColor = async () => {
    setLoading(true);
    try {
      const body = {
        cardId: cardID,
        color: cardColor,
      };
      const res = await ChangeCardColor(body);
      if (res.status === true) {
        setLoading(false);
        handleToast(res.message, "success");
      } else {
        setLoading(false);
        handleToast(res.message, "error");
      }
    } catch (error) {
      ;
    }
  };
  const getCardById = async (id) => {
    // setLoader(true);
    try {
      const cardDetails = await GetcardById(id);
      if (cardDetails) {
        setCardColor(cardDetails?.card?.colour);
        if (cardDetails?.card?.colour === "purple") {
          setActiveColor("#8B52FF");
        } else if (cardDetails?.card?.colour === "brown") {
          setActiveColor("#914C2C");
        } else if (cardDetails?.card?.colour === "red") {
          setActiveColor("#BD0000");
        } else {
          setActiveColor("#22C26E");
        }
      }
      if (cardDetails.details) {
        setLoader(false);
        setDetails(cardDetails.details);
        const yyyy = cardDetails.details.expiry_year;
        const lastyyyy = yyyy.split("");
        setExYear(`${lastyyyy[2]}${lastyyyy[3]}`);
      }
      if (cardDetails?.card) {
        setCardDetails(cardDetails);
      }
    } catch (error) {
      ;
    }
  };
  // const getCardByToken = async (token) => {
  //   try {
  //     const cardDetails = await GetcardByToken(token);
  //    
  //   } catch (error) {
  //     ;
  //   }
  // };
  function formatCardNumber(cardNumber) {
    const cleaned = cardNumber.replace(/\D+/g, "");
    const formatted = cleaned.match(/.{1,4}/g)?.join(" ") || "";

    return formatted;
  }
  useEffect(() => {
    setLoader(true);
    if (cardID) {
      getCardById(cardID);
    }
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Change card color"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentCard}>
            <View style={styles.section1Wrap}>
              <H4 style={styles.amt}>Your card, your style</H4>

              <H4
                // @ts-ignore
                style={[
                  styles.amt,
                  {
                    fontSize: 12,
                    lineHeight: (19.2 / baseHeight) * height,
                    color: colors.gray,
                    textAlign: "center",
                    fontFamily: fonts.poppinsRegular,
                  },
                ]}
              >
                Express your individuality with a card color that matches your
                unique lifestyle
              </H4>
              <View style={styles.section2Wrap}></View>
              <VirtualCard
                cardColor={cardColor}
                name={details?.card_name?.toUpperCase()}
                cardNumber={
                  details?.card_number
                    ? formatCardNumber(details?.card_number)
                    : ".... .... .... ...."
                }
                cvv={details.cvv ? details.cvv : "..."}
                validDate={`${details ? details.expiry_month : ".."}/${
                  details ? exYear : ".."
                }`}
              />

              <View style={styles.section3Wrap}>
                <H4
                  // @ts-ignore
                  style={[
                    {
                      fontSize: 12,
                      lineHeight: (19.2 / baseHeight) * height,
                      color: colors.gray,
                      textAlign: "center",
                      fontFamily: fonts.poppinsRegular,
                    },
                  ]}
                >
                  Choose the SFx card color that reflects your unique lifestyle.
                </H4>
                <View
                  style={{
                    flexDirection: "row",
                    marginTop: (24 / baseHeight) * height,
                    alignItems: "center",
                    width: "100%",
                    justifyContent: "center",
                  }}
                >
                  {Colors.map((item, index) => (
                    <TouchableOpacity
                      onPress={() => {
                        setActiveColor(item);
                        item === "#8B52FF"
                          ? setCardColor("purple")
                          : item === "#914C2C"
                          ? setCardColor("brown")
                          : item === "#BD0000"
                          ? setCardColor("red")
                          : item === "#22C26E"
                          ? setCardColor("green")
                          : setCardColor("purple");
                      }}
                      key={index}
                      style={{
                        width: 24,
                        height: 24,
                        // backgroundColor: item,
                        borderWidth: activeColor === item ? 1 : 0,
                        borderColor: item,
                        borderRadius: 100,
                        marginRight: 16,
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <View
                        style={{
                          width: 16,
                          height: 16,
                          backgroundColor: item,
                          borderRadius: 100,
                        }}
                      ></View>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </View>
          </View>
          <View style={styles.btnCont}>
            <Button
              btnText={"Change color"}
              onPress={() => {
                // ChangeCardColor()
                changeCardColor();
              }}
              loading={loading}
            />
          </View>
        </ScrollView>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentCard: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    // marginTop: 24,
    marginTop: (2.7 * height) / 100,
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 16,
    paddingRight: 16,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    marginBottom: 4,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  copyBtn: {
    paddingTop: 4,
    paddingBottom: 4,
    padding: 13,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  addMoney: {
    fontSize: 12,
    lineHeight: 19.2,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  amt: {
    fontSize: 16,
    // fontSize: 32,
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  amtCur: {
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  timer: {
    paddingTop: 4,
    paddingBottom: 4,
    paddingLeft: 16,
    paddingRight: 16,
    borderRadius: 99,
    marginTop: 16,
  },
  statusText: {
    fontSize: 10,
    lineHeight: 16,
    fontFamily: fonts.poppinsRegular,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginTop: (2.7 * height) / 100,
    // paddingTop: (2.7 * height) / 100,
    borderTopWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  section3Wrap: {
    width: "100%",
    marginTop: (2.7 * height) / 100,
    borderColor: colors.stroke,
    // paddingLeft: 16,
    // backgroundColor: 'red',
    alignItems: "center",
  },
  barCont: {
    // backgroundColor: "red",
    alignItems: "center",
  },
  bar1: {
    height: 46,
    width: 2,
    borderRadius: 2,
    backgroundColor: colors.green,
    marginTop: 4,
    marginBottom: 4,
  },
  bar2: {
    height: 28,
    width: 2,
    borderRadius: 2,
    marginTop: 4,
    marginBottom: 4,
  },
  progressDesCont: {
    flexDirection: "row",
  },
  progTextHead: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  progTextBody: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: (5 * height) / 100,
    // marginTop: 42,
    // alignItems: 'center',
    // justifyContent: 'center',
  },
});
