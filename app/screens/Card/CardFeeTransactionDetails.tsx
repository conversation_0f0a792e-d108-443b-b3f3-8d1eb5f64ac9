import React, { useContext, useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
  Linking,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import SendMoneyStatus from "../../components/SeendMoneyStatus";
import Link from "../../components/Link";
import { GetTransationById } from "../../RequestHandlers/Wallet";
import * as Clipboard from "expo-clipboard";
import { formatDate } from "../../components/FormatDate";
import { countries } from "../../components/counties";
import { GetRateById } from "../../RequestHandlers/Wallet";
import Loader from "../../components/ActivityIndicator";
import { convertToTitleCase } from "../../components/ConvertToTitlecase";
import { GetcardById, GetcardByToken } from "../../RequestHandlers/Card";
import { useToast } from "../../context/ToastContext";
import { CredentailsContext } from "../../RequestHandlers/CredentailsContext";

const { width, height } = Dimensions.get("window");
export default function CardFeeTransactionDetails({ navigation, route }) {
  const [showSendStatus, setShowSendStatus] = useState(false);
  const [tranStat, setTranStat] = useState("successful");
  const { transactionType, id } = route.params || "";
  const [resData, setResData] = useState<any>([]);
  const [loader, setLoader] = useState(false);
  const [yellowCardCode, setyellowCardCode] = useState("");
  const [curDetails, setCurDetails] = useState<any>([]);
  const [yData, setYData] = useState<any>([]);
  const [symbol, setSymbol] = useState("");
  const [code, setCode] = useState("");
  const [cardNum, setCardNum] = useState("....");
  const {storedCredentails} = useContext(CredentailsContext);
  const { handleToast } = useToast();
  const copyAccNum = async (accNum) => {
    const copiedText = await Clipboard.setStringAsync(accNum);
    if (copiedText === true) {
      handleToast("Code copied to clipboard", "success");
    } else {
      handleToast("Error copying code", "error");
    }
  };
  const getCardById = async (id) => {
    try {
      const cardDetails = await GetcardById(id);
      if (cardDetails) {
      }
    } catch (error) {}
  };
  const transaction = async () => {
    try {
      const res = await GetTransationById(id);
      if (res) {
        setLoader(false);
        setResData(res.transaction);
        getCardById(res?.transaction?.card?.id);
      }
      if (res.yellowCardData) {
        setYData(res.yellowCardData);
      }
      setyellowCardCode(res?.transaction?.user?.homeCountry);
      const yellowCardCode = getYellowCardCode(
        res?.transaction?.user?.homeCountry
      );
      const symbol = getSymbol(yellowCardCode);
      setSymbol(symbol);
      setCode(yellowCardCode);
      getRateById(yellowCardCode);
      if (res.transaction.status === "completed") {
        setTranStat("Successful");
      } else if (res.transaction.status === "failed") {
        setTranStat("Failed");
      } else {
        setTranStat("Pending");
      }
    } catch (error) {}
  };
  const getYellowCardCode = (countryName) => {
    if (countryName === "Turkey" || countryName === "North Cyprus") {
      return "TRY"; // Assuming "TRY" is the Yellow Card Code for Turkey/North Cyprus
    }
    const country = countries.find((item) => item.country === countryName);
    return country ? country.currencyCode : "Country not found";
  };
  const getSymbol = (symbol) => {
    if (symbol === "TRY") {
      return "₺";
    }
    const curSymbol = countries.find((item) => item.currencyCode === symbol);
    return curSymbol ? curSymbol.symbol : "symbol not found";
  };
  //   const yc = getYellowCardCode(yellowCardCode);
  const getRateById = async (yc) => {
    try {
      const rate = await GetRateById(yc);
      if (rate) {
        setLoader(false);
      }
      setCurDetails(rate[0]);
    } catch (error) {}
  };
  useEffect(() => {
    setLoader(true);
    transaction();
    const interval = setInterval(() => {
      transaction();
    }, 30000);
    return () => clearInterval(interval);
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Transaction details"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <DetailCard
                amount={
                  <>
                    <P
                      style={{
                        fontSize: 24,
                        lineHeight: 36,
                        marginRight: 2,
                      }}
                    >
                      ${resData?.amount?.toLocaleString()}
                    </P>
                    <P style={{ marginTop: 5 }}>USD</P>
                  </>
                }
                // convertedAmount={
                //   <>
                //     <P
                //       style={{
                //         fontSize: 16,
                //         lineHeight: 24,
                //         marginRight: 2,
                //       }}
                //     >
                //       {symbol}
                //       {(resData?.amount * curDetails?.buy).toLocaleString()}
                //     </P>
                //     <P style={{ marginTop: 2, fontSize: 12, lineHeight: 18 }}>
                //       {code}
                //     </P>
                //   </>
                // }
                timer={
                  <View style={styles.indicatorCont}>
                    <View
                      style={[
                        styles.indicatorDot,
                        {
                          backgroundColor:
                            tranStat?.toLowerCase() == "pending"
                              ? colors.yellow
                              : tranStat?.toLowerCase() === "failed"
                              ? colors.red
                              : colors.green,
                        },
                      ]}
                    ></View>
                    <P style={{ fontSize: 10, lineHeight: 16 }}>{tranStat}</P>
                  </View>
                }
                lineStyle={{ borderStyle: "dashed", marginTop: 24 }}
                bottomComponent={
                  <View style={styles.desCont}>
                    <View
                      style={{
                        paddingBottom: 24,
                        borderBottomWidth: 1,
                        borderColor: colors.stroke,
                        borderStyle: "dashed",
                      }}
                    >
                      {/* <View style={styles.items}>
                            <P style={styles.holder}>Account number</P>
                            <P style={styles.value}>**********</P>
                          </View> */}
                      <View style={styles.items}>
                        <P style={styles.holder}>First name</P>
                        <P style={styles.value}>{resData?.user?.firstName}</P>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Last name</P>
                        <P style={styles.value}>{resData?.user?.lastName}</P>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Phone numberr</P>
                        <P style={styles.value}>
                          {/* @ts-ignore */}
                          {resData?.user?.phoneNumber}
                        </P>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Home country</P>
                        <P style={styles.value}>{resData?.user?.homeCountry}</P>
                      </View>
                      {/* <View style={styles.items}>
                        <P style={styles.holder}>Card color</P>
                        <P style={styles.value}>{resData?.card?.type}</P>
                      </View> */}
                      <View style={styles.items}>
                        <P style={styles.holder}>Resident address</P>
                        <P
                          // @ts-ignore
                          style={[
                            styles.value,
                            { width: "55%", textAlign: "right" },
                          ]}
                        >
                          {resData?.user?.residentAddress}
                        </P>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Reference number</P>
                        <View
                          style={{
                            flexDirection: "row",
                            width: 150,
                            justifyContent: "flex-end",
                            alignItems: "center",
                          }}
                        >
                          <TouchableOpacity
                            onPress={() => {
                              // @ts-ignore
                              copyAccNum(resData?.ref);
                            }}
                          >
                            <SvgXml
                              xml={svg.lightCopy}
                              style={{ marginRight: 10 }}
                            />
                          </TouchableOpacity>
                          <P
                            // @ts-ignore
                            style={[
                              styles.value,
                              { textAlign: "right", width: 120 },
                            ]}
                          >
                            {/* @ts-ignore */}
                            {resData?.ref}
                          </P>
                        </View>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Timestamp</P>
                        <P style={styles.value}>
                          {/* @ts-ignore */}
                          {formatDate(resData?.updatedAt)}
                        </P>
                      </View>
                    </View>
                    <View style={{ paddingTop: 24 }}>
                      <View style={styles.items}>
                        <P style={styles.holder}>Creation fee</P>
                        <P style={styles.value}>
                          4{" "}
                          <P
                            // @ts-ignore
                            style={[
                              styles.value,
                              { fontFamily: fonts.poppinsRegular },
                            ]}
                          >
                            USD
                          </P>
                        </P>
                      </View>
                      <View style={styles.items}>
                        <P style={styles.holder}>Card top-up</P>
                        <P style={styles.value}>
                          1{" "}
                          <P
                            // @ts-ignore
                            style={[
                              styles.value,
                              { fontFamily: fonts.poppinsRegular },
                            ]}
                          >
                            USD
                          </P>
                        </P>
                      </View>
                      {/* <View style={styles.items}>
                        <P style={styles.holder}>Exchange rate</P>
                        <P style={styles.value}>
                          1 {resData?.cardWallet?.asset} ~ 1 USD
                        </P>
                      </View> */}
                      <View style={styles.items}>
                        <P style={styles.holder}>Payment method</P>
                        <P style={styles.value}>Card</P>
                      </View>
                      <View style={[styles.items]}>
                        <P style={styles.holder}>Type</P>
                        <P style={styles.value}>Card fee</P>
                      </View>
                      <View style={[styles.items, { marginBottom: 0 }]}>
                        <P style={styles.holder}>Account</P>
                        {/* @ts-ignore */}
                        <P style={styles.value}>{resData?.cardWallet?.asset}</P>
                      </View>
                    </View>
                  </View>
                }
              />
            </View>
            <View style={styles.buttonWrap}>
              {tranStat?.toLowerCase() === "successful" && (
                <Button
                  btnText="View receipt"
                  onPress={() => {
                    navigation.navigate("CardFeeReciept", {
                      transactionType: transactionType,
                      data: resData,
                    });
                  }}
                />
              )}
              <View
                style={{
                  alignItems: "center",
                  flexDirection: "row",
                  justifyContent: "center",
                  marginTop: 32,
                }}
              >
                <SvgXml xml={svg.chat} style={{ marginRight: 4 }} />
                <Link
                  style={{ fontSize: 12 }}
                  onPress={() => {
                    const username = storedCredentails?.user?.username || "Not provided";
                    const email = storedCredentails?.user?.email || "Not provided";
                    const message = `Hi, Support, I have an issue that requires resolving.\nMy Username is ${username} and My email is ${email}`;
                    const encodedMessage = encodeURIComponent(message);
                    Linking.openURL(
                      `https://wa.me/905338563416?text=${encodedMessage}`
                    );
                  }}
                >
                  Report transaction
                </Link>
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
    // backgroundColor: "red",
  },
  indicatorCont: {
    padding: 19.5,
    paddingTop: 4,
    paddingBottom: 4,
    backgroundColor: colors.secBackground,
    marginTop: 8,
    borderRadius: 99,
    flexDirection: "row",
    alignItems: "center",
  },
  indicatorDot: {
    width: 8,
    height: 8,
    borderRadius: 99,
    marginRight: 4,
  },
});
