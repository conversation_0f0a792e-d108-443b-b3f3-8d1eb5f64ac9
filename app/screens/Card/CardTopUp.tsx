import React, { useState, useEffect } from "react";
import { View, StyleSheet, Dimensions, ScrollView, Image } from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import InputCard from "../../components/InputCard";
import Keyboard from "../../components/Keyboard";
import Button from "../../components/Button";
import { colors } from "../../config/colors";
import { GetUserDetails } from "../../RequestHandlers/User";
import { countries } from "../../components/counties";
import { GetRateByCountry, GetRateById } from "../../RequestHandlers/Wallet";
import Loader from "../../components/ActivityIndicator";
import { GetUserWallet } from "../../RequestHandlers/Wallet";
import { useToast } from "../../context/ToastContext";

const baseHeight = 812;
const { width, height } = Dimensions.get("window");
export default function CardTopUp({ navigation, route }) {
  const [inputValue, setInputValue] = useState("0");
  const { handleToast } = useToast();
  const { details } = route?.params || {};
  const [error, setError] = useState(false);
  const [isUsdInput, setIsUsdInput] = useState(true);
  const [loader, setLoader] = useState(false);
  const [curDetails, setCurDetails] = useState<any>([]);
  const [yellowCardCode, setyellowCardCode] = useState("");
  const [ngnRate, setNgnRate] = useState(0);
  const [symbol, setSymbol] = useState("");
  const [code, setCode] = useState("");
  const [amount, setAmount] = useState(0);
  // Example exchange rate

  const formatNumber = (value) => {
    value = value.toString();
    return value.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const formatNumberWithDecimal = (value, decimalPlaces = 2) => {
    if (!isNaN(value)) {
      return Number(value)
        .toFixed(decimalPlaces)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    return "0.00";
  };

  const handleKeyPress = (key) => {
    setError(false); // Reset error state on key press

    if (key === "←") {
      setInputValue((prev) => (prev.length > 1 ? prev.slice(0, -1) : "0"));
    } else if (key === "Enter") {
      // Handle enter key press
    } else {
      setInputValue((prev) => {
        let newValue = prev === "0" && key !== "." ? key : prev + key;
        newValue = newValue.replace(/[^0-9.]/g, ""); // Remove any non-numeric characters
        return newValue;
      });
    }
  };

  const toggleCurrency = () => {
    setIsUsdInput(!isUsdInput);
    setInputValue("0"); // Reset input value when toggling
  };
  const convertedValue = isUsdInput
    ? formatNumberWithDecimal(Number(inputValue) * ngnRate)
    : formatNumberWithDecimal(Number(inputValue) / ngnRate);

  const userDetails = async () => {
    try {
      const details = await GetUserDetails();
      if (details) {
        const yellowCardCode = getYellowCardCode(details?.homeCountry);
        const symbol = getSymbol(yellowCardCode);
        getRateById(yellowCardCode); // Pass yellowCardCode to getRateById
        setSymbol(symbol);
        setCode(yellowCardCode);
      }
    } catch (error) {}
  };
  const getYellowCardCode = (countryName) => {
    if (countryName === "Turkey" || countryName === "North Cyprus") {
      return "TRY";
    }
    const country = countries.find((item) => item.country === countryName);
    return country ? country.currencyCode : "Country not found";
  };

  const getSymbol = (currencyCode) => {
    if (currencyCode === "TRY") {
      return "₺";
    }
    const curSymbol = countries.find(
      (item) => item.currencyCode === currencyCode
    );
    return curSymbol ? curSymbol.symbol : "Symbol not found";
  };
  const getRateById = async (currencyCode) => {
    setLoader(true);
    const provider =
      currencyCode === "NGN"
        ? "link"
        : currencyCode === "TRY"
        ? "tcmb"
        : "yellow-card";
    try {
      const rate = await GetRateById(currencyCode);
      const sfxRate = await GetRateByCountry(currencyCode, provider);
      sfxRate.map((item, index) => {
        if (item.type === "buy") {
          setNgnRate(item.amount);
        }
      });
      if (rate) {
        setLoader(false);
        setCurDetails(rate[0]);
      }
    } catch (error) {}
  };
  const getWallet = async () => {
    try {
      const bal = await GetUserWallet();
      setAmount(bal.totalInUsd);
    } catch (error) {}
  };

  const setAddAmount = () => {
    const realAmount = Number(inputValue.replaceAll(",", ""));
    const formattedInputValue = isUsdInput ? realAmount : realAmount / ngnRate;
    if (realAmount > amount) {
      handleToast("Insufficient balance", "error");
      setError(true);
    } else {
      setError(false);
      navigation.navigate("CardTopUpDetails", {
        details: details,
        ngnRate: ngnRate,
        code: code,
        symbol: symbol,
        amount: formattedInputValue,
      });
    }
  };

  useEffect(() => {
    setLoader(true);
    userDetails();
    getWallet();
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Amount" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.inputCardWrap}>
              <InputCard
                headerText="How much do you want to top-up to card"
                onTogglePress={toggleCurrency}
                amountValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 32,
                        lineHeight: 48,
                        marginRight: 4,
                      }}
                    >
                      {isUsdInput
                        ? `$${formatNumber(inputValue)}`
                        : `${symbol}${formatNumber(inputValue)}`}
                      <P style={{ lineHeight: 48 }}>
                        {isUsdInput ? "USD" : code}
                      </P>
                    </P>
                  </>
                }
                convertedValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 16,
                        lineHeight: 24,
                        marginRight: 4,
                      }}
                    >
                      {isUsdInput
                        ? `${symbol}${formatNumberWithDecimal(
                            Number(inputValue) * ngnRate
                          )}`
                        : `$${formatNumberWithDecimal(
                            Number(inputValue) / ngnRate
                          )}`}
                      <P style={{ lineHeight: 24, fontSize: 12 }}>
                        {isUsdInput ? code : "USD"}
                      </P>
                    </P>
                  </>
                }
                text1={`Exchange rate: 1 USD ~ ${ngnRate} ${code}`}
                extraComponent1={
                  <P
                    style={{
                      fontSize: 12,
                      color: colors.gray,
                      marginBottom: -10,
                    }}
                  >
                    Fee: {Number(inputValue) === 0 ? 0 : 1} USD
                  </P>
                }
                text2={`Available balance: $${
                  amount?.toFixed(2)?.toLocaleString() || 0
                }`}
                error={error}
              />
            </View>
            <View style={styles.bottom}>
              <View style={{ width: "90%", alignSelf: "center" }}>
                <Keyboard onKeyPress={handleKeyPress} />
              </View>
              <View
                style={{ width: "75%", alignSelf: "center", marginTop: 16 }}
              >
                <Button
                  btnText="Next"
                  onPress={() => {
                    if (inputValue === "0") {
                      setError(true);
                      handleToast("Minimum deposit is 1 USD", "error");
                    } else {
                      setAddAmount();
                    }
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    height: (92 * height) / 100,
    backgroundColor: "rgba(247, 244, 255, 1)",
    paddingTop: 16,
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
  },
  bottom: {
    width,
    top: (76 / baseHeight) * height,
  },
});
