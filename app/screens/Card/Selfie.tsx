import React, { useEffect, useState } from "react";
import { StyleSheet, View, Platform, Dimensions } from "react-native";
import { SmileIDSmartSelfieCaptureView } from "@smile_identity/react-native";
import RNFS from "react-native-fs";
import { Image } from "react-native-compressor";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import { AddAdditionalId } from "../../RequestHandlers/User";
import Loader from "../../components/ActivityIndicator";
import { manipulateAsync, FlipType, SaveFormat } from "expo-image-manipulator";
import { useToast } from "../../context/ToastContext";

const { width, height } = Dimensions.get("window");

interface PProps {
  close?: any;
  extraFunction?: any;
}
export default function Selfie({ close, extraFunction }: PProps) {
  const [base64Image, setBase64Image] = useState<
    { image: string; image_type_id: number | null }[]
  >([]);
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();
  const convertToBase64 = async (filePath: string, id: number) => {
    try {
      const manipResult = await manipulateAsync(filePath, [], {
        compress: 0.5,
        format: SaveFormat.WEBP,
        base64: true,
      });
      const result = await Image.compress(filePath, {
        compressionMethod: "manual",
        maxWidth: 500,
        quality: 0.1,
      });
      const base64 = await RNFS.readFile(result, "base64");
      const base64String = `${manipResult.base64}`;
      if (base64String) {
      }
      const newImageObject = {
        image: String(base64String),
        image_type_id: id,
      };
      setBase64Image((prev) => [...prev, newImageObject]);
    } catch (error) {
      console.error("Error converting file to Base64:", error);
    }
  };

  const sumbitImg = async () => {
    try {
      const res = await AddAdditionalId({
        images: base64Image,
      });
      if (res.error) {
        handleToast(res.message, "error");
      } else {
        extraFunction();
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (base64Image.length >= 8) {
      sumbitImg();
    }
  }, [base64Image.length >= 8]);
  return (
    <>
      <View style={styles.selfieCont}>
        <AuthenticationHedear text="Selfie" cancel={true} modalClose={close} />
        <View
          style={{
            width: "90%",
            alignSelf: "center",
            borderRadius: 12,
            height: "85%",
            paddingTop: 30,
            paddingBottom: 30,
            backgroundColor: "white",
            overflow: "hidden",
          }}
        >
          <SmileIDSmartSelfieCaptureView
            style={{ width: "100%", height: "100%" }}
            allowAgentMode={false}
            showInstructions={true}
            showAttribution={true}
            showConfirmation={true}
            onResult={(event) => {
              try {
                const result = JSON.parse(event.nativeEvent.result);
                console.log("Parsed result:", result);
                setLoading(true);
                if (result.selfieFile) {
                  convertToBase64(result.selfieFile, 2);
                } else {
                  console.error("No selfie file found in the result.");
                }
                if (
                  result.livenessFiles &&
                  Array.isArray(result.livenessFiles)
                ) {
                  result.livenessFiles.forEach((item) => {
                    if (item) {
                      convertToBase64(item, 6);
                    } else {
                      console.error("Invalid liveness file path:", item);
                    }
                  });
                } else {
                  console.error("No valid liveness files found.");
                }
              } catch (error) {
                console.error("Error parsing result:", error);
              }
            }}
          />
        </View>
      </View>
      {loading && <Loader />}
    </>
  );
}

const styles = StyleSheet.create({
  selfieCont: {
    paddingTop: Platform.OS === "ios" ? 50 : 30,
    width: "100%",
    zIndex: 100,
    position: "absolute",
    height,
    backgroundColor: colors.secBackground,
  },
});
