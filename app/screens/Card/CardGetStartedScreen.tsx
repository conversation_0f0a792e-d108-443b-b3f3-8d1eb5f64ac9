import React, { useEffect, useState } from "react";
import { Dimensions, ScrollView, StyleSheet, View, Image } from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import P from "../../components/P";
import { svg } from "../../config/Svg";
import { SvgXml } from "react-native-svg";
import Button from "../../components/Button";
import { GetUserDetails } from "../../RequestHandlers/User";
import VirtualCard from "../../components/VirtualCard";
import Loader from "../../components/ActivityIndicator";

const baseHeight = 802;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function CardGetStartedScreen({ navigation }) {
  const [name, setName] = useState("");
  const [cardColor, setCardColor] = useState("purple");
  const [loader, setLoader] = useState(false);
  const details = [
    {
      text1: "Instant top-ups",
      text2:
        "Easily add funds to your card in seconds, making you ready for your next purchase.",
      icon: svg.ccadd,
    },
    {
      text1: "Earn SFx Points",
      text2: "Enjoy rewards every time you use your SFx Card on bill payments",
      icon: svg.giftGray,
    },
    {
      text1: "Global Access",
      text2:
        "Over $5,000 usd card spending limit for your shopping, bills, residence permit, many more",
      icon: svg.globGray,
    },
    {
      text1: "Enhanced Security",
      text2:
        "Freeze or unfreeze your card instantly, set spending limits, and manage your PIN",
      icon: svg.shield,
    },
    {
      text1: "Fee",
      text2: `1$ is charged on every transaction you make.\n1$ is also charged as monthly maintenance fee`,
      icon: svg.tags,
    },
  ];

  const userDetails = async () => {
    setLoader(true);
    try {
      const details = await GetUserDetails();
      if (details) {
        setName(`${details?.firstName} ${details?.lastName}`);
        setLoader(false);
      }
    } catch (error) {
      ;
    }
  };

  useEffect(() => {
    userDetails();
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Card" navigation={navigation} />
        <ScrollView contentContainerStyle={{ paddingBottom: 150 }}>
          <View
            style={{
              alignItems: "center",
              justifyContent: "center",
              marginTop: 16,
            }}
          >
            {/* <Image
              source={require("../../assets/Creditcard.png")}
              style={{
                width: "75%",
                height: (20 * height) / 100,
                objectFit: "cover",
                borderRadius: 12,
                marginBottom: 24,
              }}
            /> */}
            <View
              style={{
                width: "80%",
                // alignSelf: "center",
                alignItems: "center",
                // backgroundColor: "red",
              }}
            >
              <VirtualCard
                contStyle={{ marginTop: 0 }}
                cardColor={cardColor}
                name={name?.toUpperCase()}
                cardNumber={"1234 1234 1234 1234"}
              />
            </View>
          </View>
          <View style={styles.content}>
            <H4
              style={{ textAlign: "center", fontFamily: fonts.poppinsSemibold }}
            >
              Empower Your Finances with the SFx Card
            </H4>
            <P
              style={{
                fontSize: 12,
                color: colors.gray,
                textAlign: "center",
                fontFamily: fonts.poppinsRegular,
                marginTop: 8,
              }}
            >
              Your Gateway to Seamless Transactions, Rewards, and Unmatched
              Convenience
            </P>
            {details.map((item, index) => {
              return (
                <View
                  key={index}
                  style={{
                    flexDirection: "row",
                    marginTop: 24,
                  }}
                >
                  <SvgXml xml={item.icon} />
                  <View
                    style={{
                      marginLeft: 8,
                      paddingRight: 20,
                    }}
                  >
                    <P
                      style={{
                        fontSize: 12,
                        fontFamily: fonts.poppinsMedium,
                        marginBottom: 4,
                      }}
                    >
                      {item.text1}
                    </P>
                    <P
                      style={{
                        fontSize:12,
                        color: colors.gray,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      {item.text2}
                    </P>
                  </View>
                </View>
              );
            })}
          </View>
          <View style={styles.btnCont}>
            <Button
              btnText="Get Started"
              onPress={() => {
                navigation.navigate("CardApplicationScreen");
              }}
            />
          </View>

        </ScrollView>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  content: {
    width: "90%",
    alignSelf: "center",
  },
  btnCont: {
    // backgroundColor: "red",
    width: (264 / baseWidth) * width,
    alignSelf: "center",
    marginTop: 20,
    marginBottom:20,
  },
  buttonWrap: {
    width,
    backgroundColor: colors.secBackground,
    position: "absolute",
    bottom: 0,
  },
});
