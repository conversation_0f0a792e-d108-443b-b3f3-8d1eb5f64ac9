import React, { useState } from "react";
import {
  Dimensions,
  ScrollView,
  StyleSheet,
  View,
  Image,
  ImageBackground,
  TouchableOpacity,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import P from "../../components/P";
import { svg } from "../../config/Svg";
import { SvgXml } from "react-native-svg";
import Button from "../../components/Button";
import Link from "../../components/Link";
import VirtualCard from "../../components/VirtualCard";

const baseHeight = 802;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function DeleteCardSelect({ navigation }) {
  const [selectedCard, setSelectedCard] = useState(null);
  const [isAccVerified, setIsAccVerified] = useState(true);
  const [disabled, setDisabled] = useState(false);
  const cards = [
    {
      name: "John Doe",
      cardNumber: "1234 1234 1234 1234",
      validDate: "06/24",
      cvv: 123,
      color: "brown",
    },
    {
      name: "John Doe",
      cardNumber: "1234 1234 1234 1234",
      validDate: "06/24",
      cvv: 123,
      color: "purple",
    },
  ];
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Delete card" navigation={navigation} />
        <ScrollView>
          <View style={styles.textWrap}>
            <P style={styles.textP}>Select the card you want to delete</P>
          </View>
          <View style={styles.cards}>
            {cards.map((item, index) => {
              return (
                <TouchableOpacity
                  key={index}
                  onPress={() => {
                    if (index === selectedCard) {
                      setSelectedCard(null);
                    } else {
                      setSelectedCard(index);
                    }
                  }}
                >
                  <View
                    style={{
                      alignItems: "center",
                      marginBottom: (16 / baseHeight) * height,
                    }}
                  >
                    {index === selectedCard && (
                      <ImageBackground
                        style={{
                          width: 48,
                          height: 48,
                          position: "absolute",
                          right: 8,
                          top: -1,
                          alignItems: "flex-end",
                          justifyContent: "center",
                          zIndex: 10,
                        }}
                        borderTopRightRadius={16}
                        source={require("../../assets/trianglew.png")}
                      ></ImageBackground>
                    )}

                    <VirtualCard
                      ccStyle={{ height: (188 / baseHeight) * height }}
                      contStyle={{ marginTop: 0, marginBottom: 0 }}
                      name="John Doe"
                      cardNumber={"1234 1234 1234 1234"}
                      CardText1={{ fontSize: 9 }}
                      CardText2={{ fontSize: 15 }}
                      cardColor={item.color}
                      cvv={item.cvv}
                      validDate={item.validDate}
                    />
                  </View>
                </TouchableOpacity>
              );
            })}
            <View style={styles.btnCont}>
              <Button
                btnText="Delete"
                disabled={selectedCard === null}
                onPress={() => {
                  navigation.navigate("FreezeCardPin", {
                    from: "Delete card",
                    headerText: "Enter card PIN",
                  });
                }}
              />
              <Link
                onPress={() => navigation.navigate("CardScreen")}
                style={{
                  textAlign: "center",
                  fontSize: (12 / baseWidth) * width,
                  marginTop: (24 / baseHeight) * height,
                }}
              >
                Cancel
              </Link>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  content: {
    width: "90%",
    alignSelf: "center",
  },
  btnCont: {
    width: (264 / baseWidth) * width,
    alignSelf: "center",
    marginTop: (120 / baseHeight) * height,
    marginBottom: (48 / baseHeight) * height,
  },
  textWrap: {
    width: "90%",
    alignSelf: "center",
    marginTop: (16 / baseHeight) * height,
  },
  textP: {
    fontSize: (14 / baseWidth) * width,
    color: colors.black,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    marginTop: (8 / baseHeight) * height,
  },
  cards: {
    width: "90%",
    alignSelf: "center",
    marginTop: (24 / baseHeight) * height,
  },
  tinyImg: {
    width: 53.1,
    height: 32,
    objectFit: "contain",
    marginBottom: (8 / baseHeight) * height,
  },
  cardBrand: {
    width: 34,
    height: 24,
    marginRight: (8 / baseWidth) * width,
  },
});
