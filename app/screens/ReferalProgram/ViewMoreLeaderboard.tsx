import React, { useState, useCallback, useContext } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  TouchableOpacity,
  Text,
  ScrollView,
  ActivityIndicator,
  Platform,
  Share,
} from "react-native";
import P from "../../components/P";
import Link from "../../components/Link";
import Button from "../../components/Button";
import { fonts } from "../../config/Fonts";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import {
  ClaimSfxPoint,
  GetReferals,
  GetSfxPoint,
} from "../../RequestHandlers/User";
import { useFocusEffect } from "@react-navigation/native";
import { formatDate2 } from "../../components/FormatDate";
import { formatDate } from "../../components/FormatDate";
import ReferralShare from "../../components/ReferralShare";
import { useToast } from "../../context/ToastContext";
import { LinearGradient } from "expo-linear-gradient";
import BottomSheet from "../../components/BottomSheet";
import * as Clipboard from "expo-clipboard";
import Loader from "../../components/ActivityIndicator";
import AuthenticationHedear2 from "../../components/AuthenticationHedear2";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import { GetReferralRank } from "../../RequestHandlers/Referral";
import { CredentailsContext } from "../../RequestHandlers/CredentailsContext";

// const groupByDate = (items) => {
//   return items.reduce((acc, item) => {
//     const dateOnly = new Date(item.createdAt).toISOString().split("T")[0];
//     acc[dateOnly] = acc[dateOnly] || [];
//     acc[dateOnly].push(item);
//     return acc;
//   }, {});
// };

const baseHeight = 812;
const { width, height } = Dimensions.get("window");
export default function ViewMoreLeaderboard({ navigation, route }) {
  const [amount, setAmount] = useState(0);
  const [amc, setAmc] = useState("");
  const [limit] = useState(50);
  const [referrals, setReferrals] = useState<any>([]);
  const [loading1, setLoading1] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [totalItem, setTotalItem] = useState(0);
  const [length, setLength] = useState(0);
  const [loading, setLoading] = useState(false);
  const [actionLoad, setActionLoad] = useState(false);
  const { handleToast } = useToast();
  const [ranking, setRanking] = useState<any>([]);
  const [rankLoading, setRankLoading] = useState(true);
  const [isReferralProgram, setIsReferralProgram] = useState(false);
  const [page, setPage] = useState(1);
  const [rankPage, setRankPage] = useState(1);
  const [hasMoreRankData, setHasMoreRankData] = useState(true);
  const [rankLoadingMore, setRankLoadingMore] = useState(false);
  const [activeTab, setActiveTab] = useState("this-month");
  const { storedCredentails } = useContext(CredentailsContext);
  // @ts-ignore
  const userId = storedCredentails?.user?.id;
  const tabs = [
    { option: "This month", value: "this-month" },
    { option: "All time", value: "all" },
  ];

  const referral = [
    {
      name: "John Doe",
      time: "5:55 am",
      date: "Today",
      sfxPoint: 20,
      img: require("../../assets/user1.png"),
    },
    {
      name: "John Doe",
      time: "5:55 am",
      date: "Today",
      sfxPoint: 20,
      img: require("../../assets/user2.png"),
    },
    {
      name: "John Doe",
      time: "5:55 am",
      date: "Today",
      sfxPoint: 20,
      img: require("../../assets/user3.png"),
    },
    {
      name: "John Doe",
      time: "5:55 am",
      date: "Yesterday",
      sfxPoint: 20,
      img: require("../../assets/user2.png"),
    },
    {
      name: "John Doe",
      time: "5:55 am",
      date: "Yesterday",
      sfxPoint: 20,
      img: require("../../assets/user3.png"),
    },
    {
      name: "John Doe",
      time: "5:55 am",
      date: "25th Aug 2024",
      sfxPoint: 20,
      img: require("../../assets/user2.png"),
    },
    {
      name: "John Doe",
      time: "5:55 am",
      date: "25th Aug 2024",
      sfxPoint: 20,
      img: require("../../assets/user3.png"),
    },
  ];

  const getSfxPoint = async () => {
    try {
      const res = await GetSfxPoint();
      if (res.totalpoints) {
        setAmount(res.totalpoints);
      }
    } catch (error) {}
  };
  const getRef = async (loadMore = false) => {
    setLoading1(true);
    try {
      const res = await withApiErrorToast(
        GetReferals(page, limit),
        handleToast
      );
      if (res.items) {
        setLoading1(false);
        setTotalItem(res.meta.totalItems);
        setLength(res.items.length);
        if (res.items.length === 0) {
          setHasMoreData(false);
        } else {
          // Use referrals data as returned by API (no unnecessary sorting)
          setReferrals((prev) =>
            loadMore ? [...prev, ...res.items] : res.items
          );
          if (loadMore) setPage((prev) => prev + 1);
        }
      }
    } finally {
      setLoading(false);
    }
  };
  const getReferralRanks = async (loadMore = false) => {
    if (loadMore) {
      setRankLoadingMore(true);
    } else {
      setRankLoading(true);
    }

    const currentPage = loadMore ? rankPage : 1;

    try {
      const res = await withApiErrorToast(
        GetReferralRank(currentPage, limit, activeTab),
        handleToast
      );
      if (res.error) {
        if (!loadMore) {
          setRanking([]);
        }
      } else {
        // Check if we have more data based on hasNextPage
        setHasMoreRankData(res.meta?.hasNextPage || false);

        if (loadMore) {
          // Append new ranking data
          setRanking((prevRanking) => [...prevRanking, ...res.items]);
          setRankPage(currentPage + 1);
        } else {
          // Replace ranking data
          setRanking(res.items);
          setRankPage(2); // Next page will be 2
        }
      }
    } catch (error) {
      console.error("Error fetching ranking:", error);
    } finally {
      if (loadMore) {
        setRankLoadingMore(false);
      } else {
        setRankLoading(false);
      }
    }
  };
  useFocusEffect(
    useCallback(() => {
      getReferralRanks();
    }, [activeTab])
  );
  useFocusEffect(
    useCallback(() => {
      // Reset ranking pagination
      setRankPage(1);
      setHasMoreRankData(true);
      setRanking([]);
      getRef();
      getSfxPoint();
    }, [])
  );

  const fetchMoreRanking = () => {
    if (!rankLoadingMore && hasMoreRankData) {
      getReferralRanks(true);
    }
  };

  function ensureHttps(url) {
    if (url?.startsWith("http://")) {
      return url?.replace("http://", "https://");
    }
    return url;
  }
  if (loading) {
    return <Loader />;
  }
  return (
    <>
      <LinearGradient
        colors={["#D0B8FF", "#F7F4FF"]}
        locations={[0.3, 0.3]}
        style={styles.gradient}
      >
        <Image
          source={require("../../assets/innerImg.png")}
          style={styles.bgImg}
        />
        <View style={styles.body}>
          <Div>
            <AuthenticationHedear2
              navigation={navigation}
              text="Leaderboard"
              iconBlack={true}
              iconComp={
                <View style={{ position: "absolute", right: 0 }}>
                  <Link
                    style={{
                      fontSize: 12,
                      fontFamily: fonts.poppinsMedium,
                      textDecorationLine: "underline",
                      color: colors.black,
                    }}
                    onPress={() => {
                      if (isReferralProgram) {
                        navigation.navigate("ContestRuleScreen");
                      } else {
                        navigation.navigate("NewReferralRules");
                      }
                    }}
                  >
                    Referral rules
                  </Link>
                </View>
              }
            />
            <ScrollView
              style={{ width: "90%", alignSelf: "center" }}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ paddingBottom: 100 }}
              onScroll={({ nativeEvent }) => {
                const { layoutMeasurement, contentOffset, contentSize } =
                  nativeEvent;
                const paddingToBottom = 20;
                if (
                  layoutMeasurement.height + contentOffset.y >=
                  contentSize.height - paddingToBottom
                ) {
                  fetchMoreRanking();
                }
              }}
              scrollEventThrottle={400}
            >
              <View style={styles.contestCard}>
                <View
                  style={{
                    width: "100%",
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <View style={{ width: "70%" }}>
                    <P
                      style={{
                        fontSize: 12,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      Invite your friends, earn points,
                    </P>
                    <P
                      style={{
                        fontSize: 16,
                        fontFamily: fonts.poppinsMedium,
                        lineHeight: 20,
                        marginTop: 4,
                      }}
                    >
                      climb the leaderboard!
                    </P>
                  </View>
                  <SvgXml xml={svg.ldImage} />
                </View>
                <View style={styles.dashedDivider} />
                <View style={styles.referralStatsContainer}>
                  <P style={{ fontSize: 12, fontFamily: fonts.poppinsRegular }}>
                    Unlock—plus a chance to rank among the top referrers this
                    month.
                  </P>
                </View>
              </View>
              <View style={styles.detailWrap2}>
                <View style={styles.deatilsHead}>
                  {tabs.map((item, index) => (
                    <TouchableOpacity
                      onPress={() => {
                        setActiveTab(item.value);
                      }}
                      style={{
                        width: "50%",
                        alignItems: "center",
                        justifyContent: "center",
                        paddingVertical: 11,
                        borderTopRightRadius: index === 0 ? 12 : 0,
                        borderTopLeftRadius: index === 1 ? 12 : 0,
                        backgroundColor:
                          activeTab === item.value
                            ? colors.white
                            : "transparent",
                      }}
                      key={index}
                    >
                      <P
                        style={{
                          color:
                            activeTab === item.value
                              ? colors.primary
                              : colors.black,
                          fontSize: 12,
                        }}
                      >
                        {item.option}
                      </P>
                    </TouchableOpacity>
                  ))}
                </View>

                {rankLoading ? (
                  <View
                    style={{
                      height: 150,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <ActivityIndicator color={colors.primary} size={"large"} />
                  </View>
                ) : ranking.length === 0 ? (
                  <View
                    style={{
                      width: "100%",
                      alignItems: "center",
                      paddingVertical: 50,
                    }}
                  >
                    <SvgXml
                      xml={svg.pplPrimary}
                      style={{ marginTop: 52, marginBottom: 16 }}
                    />
                    <P
                      style={{
                        fontFamily: fonts.poppinsMedium,
                        marginBottom: 4,
                      }}
                    >
                      No top referrals!
                    </P>
                    <P
                      style={{
                        color: "#A5A1A1",
                        fontFamily: fonts.poppinsRegular,
                        width: 190,
                        fontSize: 11,
                        lineHeight: 14,
                        textAlign: "center",
                      }}
                    >
                      No active top referals at the moment
                    </P>
                  </View>
                ) : (
                  <>
                    {ranking.map((item, index) => (
                      <View
                        key={`ranking-${item?.user?.id || index}`}
                        style={{
                          width: "100%",
                          paddingTop: 13,
                          paddingBottom: 13,
                          paddingLeft: 16,
                          paddingRight: 16,
                          borderBottomWidth:
                            index === ranking.length - 1 ? 0 : 1,
                          borderColor: colors.secBackground,
                          flexDirection: "row",
                          justifyContent: "space-between",
                        }}
                      >
                        <View
                          style={{
                            flexDirection: "row",
                            alignItems: "center",
                          }}
                        >
                          <P style={{ marginRight: 12 }}>{index + 1}</P>
                          <View>
                            <Image
                              source={
                                item?.user?.picture
                                  ? { uri: ensureHttps(item?.user?.picture) }
                                  : require("../../assets/user1.png")
                              }
                              style={{
                                width: 40,
                                height: 40,
                                borderRadius: 100,
                                objectFit: "cover",
                              }}
                            />
                            {item?.rank >= 1 && item?.rank <= 5 && (
                              <SvgXml
                                xml={
                                  item?.rank == 1
                                    ? svg.GoldBadge
                                    : item?.rank == 2
                                    ? svg.silver
                                    : item?.rank == 3
                                    ? svg.Bronze3
                                    : item?.rank == 4
                                    ? svg.Bronze4
                                    : svg.Bronze5
                                }
                                style={{
                                  position: "absolute",
                                  bottom: -3,
                                  right: -3,
                                }}
                                width={20}
                                height={20}
                              />
                            )}
                          </View>
                          <View style={{ marginLeft: 12 }}>
                            <View
                              style={{
                                flexDirection: "row",
                                alignItems: "center",
                                gap: 4,
                              }}
                            >
                              <P style={{ fontSize: 12 }}>
                                {item?.user?.firstName} {item?.user?.lastName}
                              </P>
                              {userId === item?.user?._id && (
                                <View
                                  style={{
                                    backgroundColor: colors.primarySubtle,
                                    paddingHorizontal: 12,
                                    borderRadius: 5,
                                  }}
                                >
                                  <P
                                    style={{
                                      color: colors.primary,
                                      fontSize: 10,
                                    }}
                                  >
                                    You
                                  </P>
                                </View>
                              )}
                            </View>
                            <View
                              style={{
                                flexDirection: "row",
                                alignItems: "center",
                                gap: 4,
                              }}
                            >
                              <SvgXml xml={svg.pplOutline} />
                              <P
                                style={{
                                  fontSize: 12,
                                  color: colors.dGray,
                                  fontFamily: fonts.poppinsRegular,
                                }}
                              >
                                {item?.referredCount?.toLocaleString()}{" "}
                                {item?.referredCount > 1
                                  ? "referrals"
                                  : "referral"}
                              </P>
                            </View>
                          </View>
                        </View>
                      </View>
                    ))}

                    {rankLoadingMore && (
                      <View style={{ padding: 20, alignItems: "center" }}>
                        <ActivityIndicator
                          size="small"
                          color={colors.primary}
                        />
                      </View>
                    )}
                  </>
                )}
              </View>
            </ScrollView>
          </Div>
        </View>
      </LinearGradient>
    </>
  );
}

const styles = StyleSheet.create({
  body: {
    width,
    height: "100%",
  },
  itemBox: {
    width: "90%",
    marginTop: 16,
    alignSelf: "center",
    flexDirection: "row",
    alignItems: "center",
    gap: 15,
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    width: "80%",
    fontSize: 12,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
  accountBalance: {
    alignItems: "flex-start",
    // backgroundColor:"red",
  },
  balanceText: {
    fontSize: 12,
    color: "rgba(22, 24, 23, 0.6)",
  },
  balanceAmount: {
    fontSize: 24,
    // fontWeight: "bold",
    fontFamily: "poppins-semibold",
    color: "rgba(22, 24, 23, 1)",
  },
  addMoneyButton: {
    // marginTop: 10,
    // paddingVertical: 8,
    paddingTop: 4,
    paddingRight: 16,
    paddingBottom: 4,
    paddingLeft: 16,
    backgroundColor: "rgba(140, 82, 255, 1)",
    borderRadius: 20,
  },
  addMoneyText: {
    color: "#fff",
    fontSize: 12,
  },
  actions: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  actionButton: {
    alignItems: "center",
  },
  actionButtonText: {
    marginTop: 8,
    fontSize: 12,
    color: "rgba(22, 24, 23, 1)",
  },
  item: {
    width: "100%",
    paddingTop: 12,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    marginTop: 8,
    flexDirection: "row",
    alignItems: "center",
    borderBottomColor: "#F0EFEF",
  },
  transactionAmount: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    // fontWeight: "bold",
  },
  transactionDate: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  emptyCont: {
    width: "100%",
    height: (50 * height) / 100,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 12,
    marginTop: 16,
    // justifyContent: "center",
  },
  datCat: {
    fontSize: 12,
  },
  bottomFloat: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    paddingVertical: 20,
    paddingHorizontal: 20,
    alignItems: "center",
  },
  shareComp: {
    width: "80%",
    height: 48,
  },
  gradient: {
    width,
    height,
    flex: 1,
  },
  bgImg: {
    width: 180,
    height: 180,
    position: "absolute",
    right: 0,
    top: 43,
    zIndex: 0,
    opacity: 0.2,
  },
  contestCard: {
    width: "100%",
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 20,
    marginTop: 16,
  },
  contestHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  contestTitle: {
    fontSize: 16,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
    marginTop: 4,
  },
  contestSubtitle: {
    fontSize: 12,
    color: colors.dark500,
    fontFamily: fonts.poppinsRegular,
  },
  contestBagImage: {
    width: 100,
    height: 100,
    borderRadius: 12,
    objectFit: "cover",
  },
  dashedDivider: {
    width: "100%",
    // height: 1,
    backgroundColor: colors.secBackground,
    marginVertical: 16,
    borderStyle: "dashed",
    borderWidth: Platform.OS === "ios" ? 1 : 1,
    borderColor: colors.stroke,
  },
  referralStatsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  statItem: {
    alignItems: "center",
  },
  statLabel: {
    fontSize: 12,
    color: colors.dark500,
    fontFamily: fonts.poppinsRegular,
  },
  statValue: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.poppinsMedium,
  },
  contentBody: {
    width: "90%",
    alignSelf: "center",
    marginTop: 8,
  },
  bottomSheetContent: {
    // paddingHorizontal: 20,
    // paddingBottom: 20,
  },
  howItWorksHeader: {
    flexDirection: "row",
    // justifyContent: "center",
    alignItems: "center",
    gap: 4,
    paddingVertical: 15,
    // marginBottom:  8,
  },
  howItWorksTitle: {
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    color: colors.black,
  },
  stepsContainer: {
    marginBottom: 30,
  },
  stepItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    // marginBottom: 15,
  },
  stepNumberContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#22C26E",
    justifyContent: "center",
    alignItems: "center",
  },
  stepNumber: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.white,
  },
  stepTextContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.dark500,
    marginBottom: 2,
  },
  stepDescription: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.black,
  },
  referralCodeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: colors.secBackground,
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
  },
  referralLabel: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.dGray,
  },
  referralValue: {
    fontSize: 16,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
    marginTop: 5,
  },
  copyButton: {
    padding: 10,
  },
  referralLinkContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingLeft: 15,
    paddingRight: 10,
    height: 70, // Fixed height for alignment
  },
  shareButton: {
    width: 60,
    height: "100%",
    backgroundColor: colors.primary,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  textSection: {
    width: "80%",
    height: "100%",
    backgroundColor: colors.secBackground,
    paddingLeft: 16,
    justifyContent: "center",
  },
  buttonSection: {
    width: "20%",
    height: "100%",
    backgroundColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
  },
  overLayBorder: {
    width: "100%",
    position: "absolute",
    height: 53,
    borderWidth: 1.2,
    borderRadius: 8,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    pointerEvents: "none",
  },
  line: {
    height: 28,
    width: 2,
    borderRadius: 2,
    backgroundColor: "#CDF4E3",
    marginTop: 4,
    marginBottom: 4,
  },
  trackWrap: {
    alignItems: "center",
    marginRight: 12,
  },
  shareComp1: {
    width: "100%",
    height: 53,
    flexDirection: "row",
    borderRadius: 8,
    overflow: "hidden",
  },
  detailWrap2: {
    // padding: 24,
    width: "100%",
    alignSelf: "center",
    minHeight: "80%",
    backgroundColor: "white",
    borderRadius: 12,
    alignItems: "center",
    marginTop: 16,
    overflow: "hidden",
  },
  deatilsHead: {
    width: "100%",
    // height: 42,
    borderBottomWidth: 1,
    backgroundColor: colors.primarySubtle,
    borderColor: colors.secBackground,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
});
