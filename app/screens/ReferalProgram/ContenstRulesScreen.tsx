import React, { useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  ScrollView,
  Image,
  Text,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { colors } from "../../config/colors";
import AuthenticationHedear2 from "../../components/AuthenticationHedear2";
import { TouchableOpacity } from "react-native-gesture-handler";
import NoteComponent2 from "../../components/NoteComponent2";

const { width, height } = Dimensions.get("window");

export default function ContestRulesScreen({ navigation }) {
  const rules = [
    {
      head: "Win up to $300 by simply referring & growing the SFx community!",
      body: "At SFx, we believe in rewarding those who grow with us. This referral contest is your chance to turn your network into real cash. Here’s everything you need to know:",
    },
    {
      head: "Contest rewards",
      body: `The top 3 Participants on the leaderboard by the end of the contest will win:
🥇 1st Place: $300
🥈 2nd Place: $200
🥉 3rd Place: $100 
All prizes will be credited directly to your SFx wallet`,
    },
    {
      head: "Eligibility to participate",
      body: `To enter the contest, users must:
Be a verified SFx user.
You must complete your Know Your Customer (KYC) process on the SFx app before participating.`,
    },
    {
      head: "How to participate",
      body: `Once verified:
 1. Log into the SFx app and copy your unique referral code or link.
 2. Share your referral code with friends, family, and your network.
 3. Ensure that the people you refer:
  • Download the SFx app
  • Sign up using your referral code
  • Complete their KYC verification`,
    },
    {
      head: "How referrals are counted",
      body: "A referral only counts when:",
    },
  ];
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear2 text="Rules" navigation={navigation} />
        <ScrollView contentContainerStyle={{ flexGrow: 1, paddingBottom: 200 }}>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <View style={{ width: "90%" }}>
                <P>Official rules & guidelines</P>
              </View>
              {rules.map((item, index) => (
                <View
                  style={{
                    width: "90%",
                    marginTop: 16,
                  }}
                  key={index}
                >
                  <P style={{ fontSize: 14, marginBottom: 4 }}>{item.head}</P>
                  <P style={styles.text}>{item.body}</P>
                </View>
              ))}
              <View style={{ flexDirection: "row", gap: 8, width: "90%" }}>
                <P style={styles.text}>•</P>
                <P style={styles.text}>
                  The person you referred successfully signs up using your
                  referral code, and Completes KYC verification using the
                  accepted ID methods.
                </P>
              </View>
              <View style={{ flexDirection: "row", gap: 8, width: "90%" }}>
                <P style={styles.text}>•</P>
                <P style={styles.text}>
                  Each verified user linked to your referral code on the contest
                  leaderboard.
                </P>
              </View>
            </View>
            <View
              style={{
                width: "90%",
                marginBottom: 24,
                alignSelf: "center",
              }}
            >
              <NoteComponent2
                //   text={`Any fraudulent activity, such as creating fake accounts or manipulating the referral system, will result in the revocation of earned points and potential suspension of accounts.`}
                contStyle={{ backgroundColor: colors.white }}
                component={
                  <View>
                    <View
                      style={{ flexDirection: "row", gap: 8, width: "95%" }}
                    >
                      <P style={styles.noteText}>•</P>
                      <P style={styles.noteText}>
                        Multiple signups from the same person or fraudulent
                        activity will result in disqualification.
                      </P>
                    </View>
                    <View
                      style={{ flexDirection: "row", gap: 8, width: "95%" }}
                    >
                      <P style={styles.noteText}>•</P>
                      <P style={styles.noteText}>
                        The leaderboard will be updated daily, would be
                        available on the app and shared on our social media
                        handles.
                      </P>
                    </View>
                    <View
                      style={{ flexDirection: "row", gap: 8, width: "95%" }}
                    >
                      <P style={styles.noteText}>•</P>
                      <P style={styles.noteText}>
                        Contest runs from 21st until 25th of June 2025. Winners
                        will be announced and rewarded immediately after the
                        contest ends.
                      </P>
                    </View>
                    <View
                      style={{
                        flexDirection: "row",
                        gap: 8,
                        width: "95%",
                        marginBottom: 8,
                      }}
                    >
                      <P style={styles.noteText}>•</P>
                      <P style={styles.noteText}>
                        SFx reserves the right to amend rules or disqualify
                        participants violating our guidelines.
                      </P>
                    </View>
                  </View>
                }
              />
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    marginTop: -16,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent:"center",
    alignItems: "center",
    paddingTop: 24,
    marginBottom: 24,
    paddingBottom: 24,
  },
  card: {
    height: 50,
    width: "85%",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  code: {
    backgroundColor: "#F7F4FF",
    width: 264,
    height: 66,
    borderWidth: 1,
    borderColor: "#E6E5E5",
    borderStyle: "dashed",
    borderRadius: 7,
    marginTop: 32,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    gap: 35,
    paddingRight: 19,
  },
  line: {
    width: "80%",
    height: 1,
    borderWidth: 1,
    borderColor: "#E6E5E5",
    borderStyle: "dashed",
    marginTop: 32,
    marginBottom: 32,
  },
  circle: {
    width: 24,
    height: 24,
    borderRadius: 1000,
    backgroundColor: "#F7F4FF",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  underText: { fontSize: 10, color: "#A5A1A1", textAlign: "center" },
  ott: { width: 86, height: 80, alignItems: "center" },
  detailWrap2: {
    // padding: 24,
    width: "100%",
    alignSelf: "center",
    height: 246,
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent: "center",
    alignItems: "center",
    marginTop: 60,
  },
  deatilsHead: {
    width: "100%",
    height: 42,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: "5%",
  },
  textCats: {
    width: "90%",
    flexDirection: "row",
    marginTop: 10,
    paddingRight: 10,
  },
  text: { fontSize: 12, fontFamily: fonts.poppinsRegular },
  noteText: {
    fontSize: 11,
    fontFamily: fonts.poppinsRegular,
  },
});
