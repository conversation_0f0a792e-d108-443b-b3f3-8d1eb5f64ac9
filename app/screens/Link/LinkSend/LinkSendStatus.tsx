import React, { useCallback, useEffect, useState } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  StatusBar,
  Modal,
  BackHandler,
} from "react-native";
import { colors } from "../../../config/colors";
import P from "../../../components/P";
import { fonts } from "../../../config/Fonts";
import Button from "../../../components/Button";
import Link from "../../../components/Link";
import { useNavigation, useFocusEffect, CommonActions } from "@react-navigation/native";
import { GetTransationById } from "../../../RequestHandlers/Wallet";
import Loader from "../../../components/ActivityIndicator";
import { countries } from "../../../components/counties";
import { GetRateById } from "../../../RequestHandlers/Wallet";
import { GetRateByCountry } from "../../../RequestHandlers/Wallet";
import { withApiErrorToast } from "../../../Utils/withApiErrorToast";
import { useToast } from "../../../context/ToastContext";
interface PProps {
  okayPress?: any;
  viewDetailPress?: any;
  tranStat?: "failed" | "success" | "pending";
  visible?: true | false;
  requestClose?: any;
  from?: string;
}
const { width, height } = Dimensions.get("window");
export default function LinkSendStatus({ navigation, route }) {
  const [stImg, setStImg] = useState(
    require("../../../assets/alert-circle.png")
  );
  const [stText, setStText] = useState("Sent money is pending");
  const [tranStat, setTranState] = useState("pending");
  const { response } = route?.params || {};
  const { destination } = route?.params || "";
  const [tranDetails, setTranDetails] = useState<any>([]);
  const [yellowCardData, setYellowCardData] = useState<any>([]);
  const [loader, setLoader] = useState(false);
  const [localRate, setLocaleRate] = useState(0);
  const [curDetails, setCurDetails] = useState("");
  const [symbol, setSymbol] = useState("");
  const [code, setCode] = useState("");
  const [linkData, setLinkData] = useState<any>([]);
  const { handleToast } = useToast();
  // const [tranStat, setTranStat] = useState("pending");

  const getYellowCardCode = (countryName) => {
    if (countryName === "Turkey" || countryName === "North Cyprus") {
      return "TRY";
    }
    const country = countries.find((item) => item.country === countryName);

    return country ? country.currencyCode : "Country not found";
  };

  const getSymbol = (currencyCode) => {
    if (currencyCode === "TRY") {
      return "₺";
    }
    const curSymbol = countries.find(
      (item) => item.currencyCode === currencyCode
    );
    // setSymbol(curSymbol)
    return curSymbol ? curSymbol.symbol : "Symbol not found";
  };
  //   const getRateById = async (currencyCode) => {
  //     setLoader(true);
  //     try {
  //       const rate = await GetRateById(currencyCode);
  //
  //       if (rate) {
  //         setLoader(false);
  //         setCurDetails(rate[0]);
  //         setLocaleRate(rate[0].buy);
  //       }
  //     } catch (error) {
  //       ;
  //     }
  //   };
  const getRateById = async () => {
    try {
      //   const rate = await GetRateById(currencyCode);
      const sfxRate = await GetRateByCountry("NGN", "link");
      sfxRate.map((item, index) => {
        if (item.type === "sell") {
          setLocaleRate(item.amount);
        }
      });
      // if (rate) {
      //   setNgnRate(rate[0].buy);
      // }
    } catch (error) { }
  };
  const getTransaction = async () => {
    try {
      const id = response?.transaction.id;
      const transaction = await withApiErrorToast(GetTransationById(id), handleToast);
      if (transaction) {
        setLoader(false);
      }
      if (transaction.linkData) {
        setLinkData(transaction.linkData[0]);
      }
      setTranDetails(transaction?.transaction);
      transaction?.transaction?.status === "completed"
        ? setTranState("success")
        : transaction?.transaction?.status === "failed"
          ? setTranState("failed")
          : setTranState("pending");
    } catch (error) {
      setLoader(false);
    }
  };

  useEffect(() => {
    if (tranStat === "failed") {
      setStImg(require("../../../assets/cancel-circle.png"));
      setStText("Sent money failed");
    } else if (tranStat === "success") {
      setStImg(require("../../../assets/success.png"));
      setStText("Money successfully sent");
    } else {
      setStImg(require("../../../assets/alert-circle.png"));
    }
  }, [tranStat]);

  useEffect(() => {
    setLoader(true);
    getTransaction();
    getRateById();
  }, []);
  useEffect(() => {
    const interval = setInterval(() => {
      getTransaction();
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  // Handle both Android hardware back button and iOS swipe-back gesture
  useFocusEffect(useCallback(() => {
    const onBackPress = () => {
      // Reset navigation stack to avoid loading issues
      // @ts-ignore
      // navigation.navigate("BottomTabNavigator");
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: "BottomTabNavigator" }],
        })
      );
      return true;
    };
    // Disable iOS swipe back gesture
    navigation.setOptions({
      gestureEnabled: false
    });
    // Handle Android back button
    BackHandler.addEventListener("hardwareBackPress", onBackPress);

    return () => {
      BackHandler.removeEventListener("hardwareBackPress", onBackPress);
    };
  }, [navigation]))
  return (
    <View style={styles.body}>
      <View style={styles.itemBox}>
        <Image source={stImg} style={{ width: 64, height: 64 }} />
        <P style={styles.statusState}>{stText}</P>
        {tranStat === "failed" ? (
          <P style={styles.stTx}>
            Money sent failed due to technical issue,{"\n"}please try again
            later!
          </P>
        ) : tranStat === "success" ? (
          <P style={styles.stTx}>
            You have successfully sent ${tranDetails?.amount?.toFixed(2)} USD ~{" "}
            {"\n"}₦{(tranDetails?.localAmount).toLocaleString()} NGN to{" "}
            {linkData?.transactions?.account_name}
          </P>
        ) : (
          <P style={styles.stTx}>
            Money is processing, please check{"\n"}money status later!
          </P>
        )}
        <View style={{ width: "75%", marginTop: 32 }}>
          <Button
            btnText="Okay!"
            onPress={() => {
              // navigation.navigate("BottomTabNavigator");
              navigation.dispatch(
                CommonActions.reset({
                  index: 0,
                  routes: [{ name: "BottomTabNavigator" }],
                })
              );
              // if (destination === "home") {
              // } else {
              //   navigation.navigate("CardScreen");
              // }
            }}
          />
          <Link
            style={{ textAlign: "center", marginTop: 16, fontSize: 12 }}
            onPress={() => {
              const id =
                response?.id === undefined
                  ? response?.transaction?.id
                  : response?.id;
              navigation.navigate("LinkSendTransactionDetails", {
                id: id,
              });
            }}
          >
            View details
          </Link>
        </View>
      </View>
      {/* {loader && <Loader />} */}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    width,
    height: (105 * height) / 100,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    bottom: 0,
    // top: 0,
    zIndex: 100,
  },
  itemBox: {
    width: "100%",
    alignItems: "center",
    // marginTop: (20*height)/100
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    fontSize: 12,
    lineHeight: 19.2,
    paddingLeft: 50,
    paddingRight: 50,
    // backgroundColor: 'red',
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
});
