import React, { useState, useEffect } from "react";
import { Dimensions, Image, ScrollView, StyleSheet, View } from "react-native";
import { colors } from "../../../config/colors";
import Div from "../../../components/Div";
import AuthenticationHedear from "../../../components/AuthenticationHedear";
import P from "../../../components/P";
import { fonts } from "../../../config/Fonts";

import Keyboard from "../../../components/Keyboard";
import Button from "../../../components/Button";
import InputCard from "../../../components/InputCard";

import { GetRateByCountry } from "../../../RequestHandlers/Wallet";
import { GetUserDetails } from "../../../RequestHandlers/User";
import Loader from "../../../components/ActivityIndicator";
import { GetUserWallet } from "../../../RequestHandlers/Wallet";
import { useToast } from "../../../context/ToastContext";
import {
  calculateWithdrawalFee,
  calculateWithdrawalFeeSync,
} from "../../../Utils/feeCalculations";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";
import {
  formatNumberWithCommas,
  formatToTwoDecimals,
} from "../../../Utils/numberFormat";
import { withApiErrorToast } from "../../../Utils/withApiErrorToast";

const { width, height } = Dimensions.get("window");
function LinkSendAmount({ navigation, route }) {
  const { data } = route.params || {};
  const [inputValue, setInputValue] = useState("0");
  const [error, setError] = useState(false);
  const [ngnRate, setNgnRate] = useState(0);
  const [teir, setTeir] = useState(0);
  const [loading, setLoading] = useState(false);
  const [bal, setBal] = useState(0);
  const [receiveAmount, setReceiveAmount] = useState(0);
  const [totalFee, setTotalFee] = useState(0);
  const [isUsdInput, setIsUsdInput] = useState(true); // Track which currency is being input
  const { handleToast } = useToast();
  const [balLoading, setBalLoading] = useState(false);

  // const fee =
  //   Number(inputValue) > 100
  //     ? 0.02 * Number(inputValue)
  //     : 0.025 * Number(inputValue);
  const formatNumber = (value: number | string): string => {
    value = value.toString();
    return value.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const toggleCurrency = () => {
    setIsUsdInput(!isUsdInput);
    setInputValue("0"); // Reset input value when toggling
  };

  const convertedValue = isUsdInput
    ? formatToTwoDecimals(Number(inputValue) * ngnRate)
    : formatToTwoDecimals(Number(inputValue) / ngnRate);
  const handleKeyPress = (key: string): void => {
    setError(false);
    if (key === "←") {
      setInputValue((prev) => (prev.length > 1 ? prev.slice(0, -1) : "0"));
    } else if (key === "Enter") {
      // Handle enter key press
    } else {
      setInputValue((prev) => {
        let newValue = prev === "0" && key !== "." ? key : prev + key;
        newValue = newValue.replace(/[^0-9.]/g, ""); // Remove any non-numeric characters
        return newValue;
      });
    }
  };

  const getUserTier = async () => {
    setLoading(true);
    try {
      const teir = await withApiErrorToast(GetUserDetails(), handleToast);
      setTeir(teir.tier.level);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const getRateById = async () => {
    try {
      //   const rate = await GetRateById(currencyCode);
      const sfxRate = await withApiErrorToast(
        GetRateByCountry("NGN", "link"),
        handleToast
      );
      sfxRate.map((item: { type: string; amount: number }) => {
        if (item.type === "sell") {
          setNgnRate(item.amount);
        }
      });
      // if (rate) {
      //   setNgnRate(rate[0].buy);
      // }
    } catch (error) {}
  };
  const getWallet = async () => {
    setBalLoading(true);
    try {
      const bal = await withApiErrorToast(GetUserWallet(), handleToast);
      setBal(bal.totalInUsd);
    } catch (error) {
    } finally {
      setBalLoading(false);
    }
  };

  useEffect(() => {
    getRateById();
    getUserTier();
    getWallet();
  }, []);

  // Calculate fee details whenever input value or rate changes
  useEffect(() => {
    if (inputValue !== "0" && ngnRate > 0) {
      try {
        // Calculate naira amount based on input currency
        const nairaAmount = isUsdInput
          ? Number(inputValue) * ngnRate
          : Number(inputValue);

        // Use the synchronous version for UI calculations for better performance
        const feeDetails = calculateWithdrawalFeeSync(nairaAmount);
        setReceiveAmount(feeDetails.receiveAmount);
        setTotalFee(feeDetails.totalFee);
      } catch (error) {
        // Silent error handling for UI calculations
      }
    } else {
      setReceiveAmount(0);
      setTotalFee(0);
    }
  }, [inputValue, ngnRate, isUsdInput]);

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Amount" navigation={navigation} />
        <ScrollView contentContainerStyle={{ flex: 1 }}>
          <View style={{ alignItems: "center", marginBottom: 10 }}>
            <Image
              source={{ uri: data?.bankImg }}
              style={{ width: 32, height: 32, marginBottom: 8 }}
            />
            {/* <SvgXml
              xml={svg.bank2}
              width={32}
              height={32}
              style={{ marginBottom: 8 }}
            /> */}
            <P style={{ fontSize: 12 }}> {data?.accountName}</P>
            <P
              style={{
                fontSize: 12,
                color: colors.gray,
                fontFamily: fonts.poppinsRegular,
              }}
            >
              {data?.accountNumber} | {data?.bankName}
            </P>
          </View>
          <View>
            <View style={{ width: "90%", alignSelf: "center" }}>
              <InputCard
                headerText="How much do you want to send"
                onTogglePress={toggleCurrency}
                toggleStyle={{ top: "40%" }}
                amountValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 32,
                        lineHeight: 48,
                        marginRight: 4,
                      }}
                    >
                      {isUsdInput
                        ? `$${formatNumber(inputValue)}`
                        : `₦${formatNumber(inputValue)}`}
                      <P style={{ lineHeight: 48 }}>
                        {isUsdInput ? "USD" : "NGN"}
                      </P>
                    </P>
                  </>
                }
                convertedValue={
                  <P
                    numberOfLines={1}
                    style={{
                      textAlign: "center",
                      fontSize: 18,
                      lineHeight: 24,
                      marginRight: 4,
                    }}
                  >
                    {isUsdInput
                      ? `₦${
                          inputValue === "0"
                            ? "0"
                            : formatToTwoDecimals(Number(receiveAmount))
                        }`
                      : `$${inputValue === "0" ? "0.00" : convertedValue}`}
                    <P style={{ lineHeight: 24, fontSize: 12 }}>
                      {isUsdInput ? "NGN" : "USD"}
                    </P>
                  </P>
                }
                extraComponent1={
                  <>
                    <View style={[styles.fee, { marginTop: 4 }]}>
                      <View style={{ width: 24, alignItems: "center" }}>
                        <SvgXml xml={svg.coin1} style={{ marginRight: 8 }} />
                      </View>
                      <P style={{ fontSize: 11, color: colors.gray }}>
                        Charges: {formatNumberWithCommas(totalFee / ngnRate)}{" "}
                        USD
                      </P>
                    </View>
                    <View style={styles.fee}>
                      <View style={{ width: 24, alignItems: "center" }}>
                        <SvgXml
                          xml={svg.watterFall}
                          style={{ marginRight: 8 }}
                        />
                      </View>
                      <P style={{ fontSize: 11, color: colors.gray }}>
                        Exchange rate: 1 USD ~ {ngnRate} NGN
                      </P>
                    </View>
                    {!isUsdInput && (
                      <View style={styles.fee}>
                        <P style={{ fontSize: 11, color: colors.gray }}>
                          Amount to be recieved: ₦
                          {formatToTwoDecimals(receiveAmount)}
                          NGN
                        </P>
                      </View>
                    )}
                  </>
                }
                text2={`Available balance: ${
                  bal && !balLoading ? `$${formatToTwoDecimals(bal)}` : ""
                }`}
                error={error}
                loading={balLoading}
              />
            </View>
            <View style={styles.bottom}>
              <View style={{ width: "90%", alignSelf: "center" }}>
                <Keyboard onKeyPress={handleKeyPress} />
                <Button
                  btnText="Next"
                  style={{ width: "80%", alignSelf: "center", marginTop: 20 }}
                  onPress={async () => {
                    const inputAsNumber = Number(inputValue);
                    const usdAmount = isUsdInput
                      ? inputAsNumber
                      : inputAsNumber / ngnRate;
                    const nairaAmount = isUsdInput
                      ? inputAsNumber * ngnRate
                      : inputAsNumber;
                    const feeDetails = await calculateWithdrawalFee(
                      nairaAmount
                    );
                    const feeInUSD = feeDetails.totalFee / ngnRate;
                    if (inputValue === "0") {
                      setError(true);
                    } else if (usdAmount < 20) {
                      setError(true);
                      handleToast(
                        "Minimum withdrawal amount is 20 USD",
                        "error"
                      );
                    } else if (teir === 1 && usdAmount + feeInUSD > 3000) {
                      setError(true);
                      handleToast("Send money limit exceeded", "error");
                    } else if (usdAmount > bal) {
                      setError(true);
                      handleToast("Insufficient balance", "error");
                    } else {
                      try {
                        // Calculate the naira amount and fees based on input currency
                        navigation.navigate("LinkSendConfirmDetails", {
                          amountData: {
                            inputValue: usdAmount.toString(),
                            rate: ngnRate,
                            fee: feeInUSD?.toFixed(2),
                            localAmount: feeDetails.receiveAmount,
                            totalFee: feeDetails.totalFee,
                            totalAmount: feeDetails.totalAmount,
                            isUsdInput: isUsdInput,
                          },
                          data: data,
                        });
                      } catch (error) {
                        setError(true);
                        handleToast("Error calculating fees", "error");
                      }
                    }
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
      {loading && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  bottom: {
    width,
    top: (8 * height) / 100,
  },
  fee: {
    flexDirection: "row",
    alignItems: "center",
    // width: "100%"
    padding: 4,
    paddingLeft: 0,
  },
});
export default LinkSendAmount;
