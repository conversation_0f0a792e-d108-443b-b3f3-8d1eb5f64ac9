import React, { useEffect, useRef, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
} from "react-native";
import { fonts } from "../../../config/Fonts";
import Div from "../../../components/Div";
import AuthenticationHedear from "../../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";
import P from "../../../components/P";
import MicroBtn from "../../../components/MicroBtn";
import { colors } from "../../../config/colors";
import DetailCard from "../../../components/DetailCard";
import Button from "../../../components/Button";
import Link from "../../../components/Link";
import { formatDate } from "../../../components/FormatDate";
import ViewShot from "react-native-view-shot";
import * as MediaLibrary from "expo-media-library";
import * as Sharing from "expo-sharing";
import { useToast } from "../../../context/ToastContext";
import { formatToTwoDecimals } from "../../../Utils/numberFormat";

const { width, height } = Dimensions.get("window");

export default function LinkSendReciept({ navigation, route }) {
  const [showSendStatus, setShowSendStatus] = useState(false);
  const { handleToast } = useToast();
  const { transactionType } = route.params;
  const { data } = route?.params || {};
  const { data2 } = route?.params || {};
  const viewShotRef = useRef(null);
  const captureAndDownload = async () => {
    // Request media library permission
    const { status } = await MediaLibrary.requestPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permission required",
        "You need to grant permission to save the receipt."
      );
      return;
    }
    if (!viewShotRef.current) {
      Alert.alert("Error", "Unable to capture receipt.");
      return;
    }
    try {
      const uri = await viewShotRef.current.capture();
      const asset = await MediaLibrary.createAssetAsync(uri);
      // Try saving to the 'Download' folder, fallback to 'Pictures'
      try {
        await MediaLibrary.createAlbumAsync("Download", asset, false);
      } catch {
        await MediaLibrary.createAlbumAsync("Pictures", asset, false);
      }

      Alert.alert("Success", "Details saved to your gallery.");
      handleToast("Details downloaded", "success");
    } catch (error) {
      Alert.alert("Error", "Failed to save receipt.");
    }
  };
  const captureAndShare = async () => {
    try {
      // @ts-ignore
      const uri = await viewShotRef.current.capture();
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri);
      } else {
        alert("Sharing is not available on this device");
      }
    } catch (error) {
      console.error("Error capturing and sharing view:", error);
    }
  };
  useEffect(() => {}, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Transaction receipt"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <View>
                <ViewShot
                  ref={viewShotRef}
                  options={{ format: "jpg", quality: 0.9 }}
                >
                  <View style={styles.recieptWrap}>
                    <DetailCard
                      type="reciept"
                      image={
                        <Image
                          source={require("../../../assets/sfx2.png")}
                          style={{
                            width: 66.98,
                            height: 24,
                            objectFit: "contain",
                            marginBottom: 24,
                          }}
                        />
                      }
                      amount={
                        <>
                          <P
                            style={{
                              fontSize: 24,
                              lineHeight: 36,
                              marginRight: 2,
                            }}
                          >
                            ${ formatToTwoDecimals(Number(data?.amount))}
                          </P>
                          <P style={{ marginTop: 5 }}>USD</P>
                        </>
                      }
                      convertedAmount={
                        <>
                          <P
                            style={{
                              fontSize: 16,
                              lineHeight: 24,
                              marginRight: 2,
                            }}
                          >
                            ₦
                            {formatToTwoDecimals(Number(data?.localAmount))}
                          </P>
                          <P
                            style={{
                              marginTop: 2,
                              fontSize: 12,
                              lineHeight: 18,
                            }}
                          >
                            NGN
                          </P>
                        </>
                      }
                      lineStyle={{ borderStyle: "dashed", marginTop: 24 }}
                      bottomComponent={
                        <View style={styles.desCont}>
                          <View
                            style={{
                              paddingBottom: 24,
                              borderBottomWidth: 1,
                              borderColor: colors.stroke,
                              borderStyle: "dashed",
                            }}
                          >
                            <View style={styles.items}>
                              <P style={styles.holder}>Sender</P>
                              <View
                                style={{
                                  justifyContent: "flex-end",
                                  width: "70%",
                                }}
                              >
                                <P style={styles.value}>
                                  {data?.user?.firstName} {data?.user?.lastName}
                                </P>
                                <P
                                  // @ts-ignore
                                  style={[
                                    styles.value,
                                    {
                                      width: "100%",
                                      color: colors.dark500,
                                      marginTop: 4,
                                      fontFamily: fonts.poppinsRegular,
                                    },
                                  ]}
                                >
                                  {data?.user?.phoneNumber} | SFx money app
                                </P>
                              </View>
                            </View>
                            <View style={styles.items}>
                              <P style={styles.holder}>Recipient</P>

                              <View
                                style={{
                                  justifyContent: "flex-end",
                                  width: "60%",
                                }}
                              >
                                <P style={styles.value}>
                                  {data2?.transactions?.account_name}
                                </P>
                                <P
                                  // @ts-ignore
                                  style={[
                                    styles.value,
                                    {
                                      width: "100%",
                                      color: colors.dark500,
                                      fontFamily: fonts.poppinsRegular,
                                    },
                                  ]}
                                >
                                  {data2?.transactions?.account_number} |{" "}
                                  {data2?.transactions?.bank_name}
                                </P>
                              </View>
                            </View>
                          </View>
                          <View style={{ paddingTop: 24 }}>
                            <View style={styles.items}>
                              <P style={styles.holder}>Reference number</P>
                              <View
                                style={{
                                  flexDirection: "row",
                                  width: 150,
                                  justifyContent: "flex-end",
                                }}
                              >
                                <P
                                  // @ts-ignore
                                  style={[
                                    styles.value,
                                    { textAlign: "right", width: 120 },
                                  ]}
                                >
                                  {data?.ref}
                                </P>
                              </View>
                            </View>
                            <View style={styles.items}>
                              <P style={styles.holder}>Payment method</P>
                              <P style={styles.value}>Send money</P>
                            </View>
                            <View style={[styles.items]}>
                              <P style={styles.holder}>Type</P>
                              <P style={styles.value}>Bank transfer</P>
                            </View>
                            <View style={styles.items}>
                              <P style={styles.holder}>Timestamp</P>
                              <P style={styles.value}>
                                {formatDate(data?.updatedAt)}
                              </P>
                            </View>
                          </View>
                        </View>
                      }
                    />
                  </View>
                </ViewShot>
              </View>

              <View style={styles.buttonWrap}>
                <Button
                  btnText="Share receipt"
                  onPress={() => {
                    // navigation.navigate("BankTransferScreen");
                    // setShowSendStatus(true);
                    captureAndShare();
                  }}
                />
                <View
                  style={{
                    flexDirection: "row",
                    width: "100%",
                    alignItems: "center",
                    justifyContent: "center",
                    marginTop: 26,
                  }}
                >
                  <SvgXml xml={svg.download} style={{ marginRight: 4 }} />
                  <Link
                    style={{ fontSize: 12, lineHeight: 24 }}
                    onPress={() => {
                      captureAndDownload();
                    }}
                  >
                    Download receipt
                  </Link>
                </View>
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.dark500,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
    textAlign: "right",
    width: "60%",
    // backgroundColor: "red",
    alignSelf: "flex-end",
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  indicatorCont: {
    padding: 19.5,
    paddingTop: 4,
    paddingBottom: 4,
    backgroundColor: colors.secBackground,
    marginTop: 8,
    borderRadius: 99,
    flexDirection: "row",
    alignItems: "center",
  },
  indicatorDot: {
    width: 8,
    height: 8,
    borderRadius: 99,
    marginRight: 4,
  },
  recieptWrap: {
    backgroundColor: colors.secBackground,
    paddingTop: 16,
    paddingBottom: 16,
  },
});
