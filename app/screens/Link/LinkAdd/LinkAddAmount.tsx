import React, { useState, useEffect } from "react";
import { Dimensions, ScrollView, StyleSheet, View } from "react-native";
import { colors } from "../../../config/colors";
import Div from "../../../components/Div";
import AuthenticationHedear from "../../../components/AuthenticationHedear";
import P from "../../../components/P";
import { fonts } from "../../../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";
import Keyboard from "../../../components/Keyboard";
import Button from "../../../components/Button";
import {
  GetLinkDepositRate,
  GetRateById,
} from "../../../RequestHandlers/Wallet";
import { GetRateByCountry } from "../../../RequestHandlers/Wallet";
import { GetUserDetails } from "../../../RequestHandlers/User";
import Loader from "../../../components/ActivityIndicator";
import { useToast } from "../../../context/ToastContext";
import { TouchableOpacity } from "react-native";
import { withApiErrorToast } from "../../../Utils/withApiErrorToast";
import { formatNumberWithCommas, formatToTwoDecimals } from "../../../Utils/numberFormat";

const { width, height } = Dimensions.get("window");
function LinkAddAmount({ navigation }) {
  const [inputValue, setInputValue] = useState("0");
  const [error, setError] = useState(false);
  const [ngnRate, setNgnRate] = useState(0);
  const [teir, setTeir] = useState(0);
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();
  const [isUsdtAmount, setIsUsdtAmount] = useState(true);

  const calculateFee = (localAmount) => {
    console.log(localAmount);
    if (localAmount < 15000) {
      return 0; // No fee for amounts below 15,000
    } else if (localAmount <= 99000) {
      return 0.015 * localAmount; // 1.5% fee for amounts between 15,000 and 99,000
    } else if (localAmount <= 5000000) {
      return 0.01 * localAmount + 1050; // 0.8% fee + 1,050 for amounts between 100,000 and 5,000,000
    } else {
      return 0; // Example: 0.5% fee + 2,000 for amounts above 5,000,000
    }
  };

  // Usage
  const localAmount = isUsdtAmount
    ? Number(inputValue) * ngnRate
    : Number(inputValue);
  const fee = calculateFee(localAmount);
  console.log(fee);
  const formatNumber = (value) => {
    value = value.toString();
    return value.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };
  const handleKeyPress = (key) => {
    setError(false);
    if (key === "←") {
      setInputValue((prev) => (prev.length > 1 ? prev.slice(0, -1) : "0"));
    } else if (key === "Enter") {
      // Handle enter key press
    } else {
      setInputValue((prev) => {
        let newValue = prev === "0" && key !== "." ? key : prev + key;
        newValue = newValue.replace(/[^0-9.]/g, ""); // Remove any non-numeric characters
        return newValue;
      });
    }
  };

  const getUserTier = async () => {
    setLoading(true);
    try {
      const teir = await withApiErrorToast(GetUserDetails(), handleToast);
      setTeir(teir.tier.level);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const getRateById = async () => {
    try {
      //   const rate = await GetRateById(currencyCode);
      const sfxRate = await GetRateByCountry("NGN", "link");
      sfxRate.map((item, index) => {
        if (item.type === "buy") {
          setNgnRate(item.amount);
        }
      });
      // if (rate) {
      //   setNgnRate(rate[0].buy);
      // }
    } catch (error) {}
  };

  const getLinkFee = async () => {
    try {
      const res = await GetLinkDepositRate();
    } catch (error) {}
  };

  useEffect(() => {
    getRateById();
    getUserTier();
    getLinkFee();
  }, []);

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Amount" navigation={navigation} />
        <ScrollView contentContainerStyle={{ flex: 1 }}>
          <View>
            <View style={styles.amHolder}>
              <P
                style={{
                  fontSize: 12,
                  color: colors.gray,
                  fontFamily: fonts.poppinsRegular,
                  marginBottom: 4,
                }}
              >
                How much do you want to Add
              </P>
              <View
                style={{
                  width: (70 * width) / 100,
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  //   padding: 10,
                }}
              >
                <P
                  numberOfLines={1}
                  style={{
                    textAlign: "center",
                    fontSize: 30,
                    lineHeight: 40,
                    marginRight: 4,
                  }}
                >
                  {`${isUsdtAmount ? "$" : " ₦"}${formatNumber(inputValue)}`}
                  <P style={{ lineHeight: 48 }}>
                    {isUsdtAmount ? "USD" : "NGN"}
                  </P>
                </P>
              </View>

              <View
                style={[
                  styles.line,
                  { backgroundColor: error ? colors.red : colors.stroke },
                ]}
              ></View>
              {!isUsdtAmount && (
                <P style={{ fontSize: 18, lineHeight: 24, marginTop: 10 }}>
                  ${formatToTwoDecimals(Number(inputValue) / ngnRate)}{" "}
                  <P style={{ fontSize: 12, fontFamily: fonts.poppinsRegular }}>
                    USD
                  </P>
                </P>
              )}

              <TouchableOpacity
                style={{ position: "absolute", top: "43%", right: "5%" }}
                onPress={() => {
                  setIsUsdtAmount(!isUsdtAmount);
                }}
              >
                <SvgXml xml={svg.upDown} />
              </TouchableOpacity>
              {isUsdtAmount && (
                <View style={styles.equiv}>
                  <P
                    style={{
                      fontSize: 12,
                      color: colors.gray,
                      marginBottom: 4,
                    }}
                  >
                    Amount you should send
                  </P>
                  <P style={{ fontSize: 14, lineHeight: 24 }}>
                    ₦
                    {isUsdtAmount
                      ? formatNumberWithCommas(
                          Number(inputValue) * ngnRate + Number(fee)
                        )
                      : formatToTwoDecimals(
                          Number(inputValue) + Number(fee)
                        )}{" "}
                    <P
                      style={{ fontSize: 12, fontFamily: fonts.poppinsRegular }}
                    >
                      NGN
                    </P>
                  </P>
                </View>
              )}

              <View
                style={{ minWidth: 200, marginTop: 6, alignItems: "center" }}
              >
                <View style={styles.fee}>
                  <View style={{ width: 24, alignItems: "center" }}>
                    <SvgXml xml={svg.coin1} style={{ marginRight: 8 }} />
                  </View>
                  <P style={{ fontSize: 11, color: colors.gray }}>
                    Charges: {formatNumberWithCommas(fee/ngnRate)} USD
                  </P>
                </View>
                <View style={styles.fee}>
                  <View style={{ width: 24, alignItems: "center" }}>
                    <SvgXml xml={svg.watterFall} style={{ marginRight: 8 }} />
                  </View>
                  <P style={{ fontSize: 11, color: colors.gray }}>
                    Exchange rate: 1 USD ~ {ngnRate} NGN
                  </P>
                </View>
              </View>
            </View>
            <View style={styles.bottom}>
              <View style={{ width: "90%", alignSelf: "center" }}>
                <Keyboard onKeyPress={handleKeyPress} />
                <Button
                  btnText="Next"
                  style={{ width: "80%", alignSelf: "center", marginTop: 32 }}
                  onPress={() => {
                    const depositeAmount = isUsdtAmount
                      ? inputValue
                      : Number(inputValue) / ngnRate;
                    console.log(depositeAmount);
                    if (Number(depositeAmount) < 15) {
                      setError(true);
                      handleToast("Minimum deposit amount is 15 USD", "error");
                    } else if (teir === 1 && Number(depositeAmount) > 3000) {
                      setError(true);
                      handleToast("Add money limit exceeded", "error");
                    } else {
                      navigation.navigate("LinkAddAccountSelect", {
                        data: {
                          inputValue: depositeAmount,
                          rate: ngnRate,
                          fee: fee,
                          eqAmount:
                            Number(depositeAmount) * ngnRate + Number(fee),
                        },
                      });
                    }
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
      {loading && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  amHolder: {
    width: "90%",
    paddingTop: 24,
    paddingBottom: 24,
    backgroundColor: colors.white,
    alignItems: "center",
    alignSelf: "center",
    borderRadius: 12,
  },
  line: {
    width: (68 * width) / 100,
    height: 1,
    alignSelf: "center",
  },
  hBar: {
    width: 1,
    height: 12,
    backgroundColor: colors.stroke,
  },
  fee: {
    flexDirection: "row",
    alignItems: "center",
    // width: "100%"
    padding: 4,
    paddingLeft: 0,
  },
  hBarCont: {
    width: 24,
    height: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  equiv: {
    width: "90%",
    // height: 67,
    borderRadius: 8,
    marginTop: 8,
    alignItems: "center",
    justifyContent: "center",
    // backgroundColor: colors.secBackground,
  },
  bottom: {
    width,
    top: (10 * height) / 100,
  },
});

export default LinkAddAmount;
