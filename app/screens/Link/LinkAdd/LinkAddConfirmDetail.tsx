import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { fonts } from "../../../config/Fonts";
import Div from "../../../components/Div";
import AuthenticationHedear from "../../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";
import P from "../../../components/P";
import MicroBtn from "../../../components/MicroBtn";
import { colors } from "../../../config/colors";
import DetailCard from "../../../components/DetailCard";
import Button from "../../../components/Button";
import { DepositBank, DepositeLink } from "../../../RequestHandlers/Wallet";
import { useToast } from "../../../context/ToastContext";
import { useGlobalModal } from "../../../context/GlobalModalContext";
import { withApiErrorToast } from "../../../Utils/withApiErrorToast";
import {
  formatNumberWithCommas,
  formatToTwoDecimals,
} from "../../../Utils/numberFormat";
const { width, height } = Dimensions.get("window");
export default function LinkAddConfirmDetail({ navigation, route }) {
  const { data, data2 } = route?.params || {};
  const [loading, setLoading] = useState(false);
  const formatNumber = (value) => {
    value = value?.toString();
    return value?.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };
  const { handleToast } = useToast();
  const { showAddMoneyFailedModal } = useGlobalModal();
  function capitalizeFirstLetter(sentence) {
    if (!sentence) return sentence;

    return sentence.charAt(0).toUpperCase() + sentence.slice(1);
  }

  const network = "Polygon";
  const deposit = async () => {
    setLoading(true);
    try {
      const body = {
        currency: "NGN",
        amount: String(data?.inputValue),
        vendor_number: data2?.selectedVendors?.vendorNumber,
        vendor_name: data2?.selectedVendors?.venderName,
        vendor_bank: data2?.selectedVendors?.vendorBank,
        stables: "USDC",
        network: network,
        bank_name: data2?.selectedBanks?.name,
        account_number: data2?.accNumber,
      };
      const deposite = await withApiErrorToast(DepositeLink(body), handleToast);
      if (deposite.data) {
        setLoading(false);
        navigation.navigate("LinkTransferScreen", { data: deposite });
      } else {
        setLoading(false);
        showAddMoneyFailedModal(deposite.message);
        // if (deposite.message.includes("Your KYC hasn't been verified")) {
        //   handleToast(deposite.message, "error");
        // } else {
        //   handleToast(deposite.message, "error");
        // }
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Confirm details" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <DetailCard
                headText={"Money you’re adding"}
                amount={
                  <>
                    <P style={{ fontSize: 32, lineHeight: 48, marginRight: 2 }}>
                      ${formatToTwoDecimals(Number(data?.inputValue))}
                    </P>
                    <P style={{ marginTop: 5 }}>USD</P>
                  </>
                }
                convertedAmount={
                  <>
                    <P style={{ fontSize: 16, lineHeight: 24, marginRight: 2 }}>
                      ₦{formatNumberWithCommas(Number(data?.eqAmount))}
                    </P>
                    <P style={{ marginTop: 2, fontSize: 12, lineHeight: 18 }}>
                      NGN
                    </P>
                  </>
                }
                bottomComponent={
                  <View style={styles.desCont}>
                    {/* <View style={styles.items}>
                      <P style={styles.holder}>Fee</P>
                      <P style={styles.value}>
                        0{" "}
                        <P
                          // @ts-ignore
                          style={[
                            styles.value,
                            { fontFamily: fonts.poppinsRegular },
                          ]}
                        >
                          USD
                        </P>
                      </P>
                    </View> */}
                    <View style={styles.items}>
                      <P style={styles.holder}>Exchange rate</P>
                      <P style={styles.value}>1 USD ~ {`${data?.rate} NGN`}</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Charges</P>
                      <P style={styles.value}>
                        {formatNumberWithCommas(data?.fee / data?.rate)} USD
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Payment method</P>
                      <P style={styles.value}>Bank transfer</P>
                    </View>
                    <View style={[styles.items]}>
                      <P style={styles.holder}>Processing time</P>
                      <P style={styles.value}>24 hours</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Vendor</P>
                      <P style={styles.value}>
                        {data2?.selectedVendors?.venderName}
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Account number</P>
                      <P style={styles.value}>
                        {data2?.selectedVendors?.vendorNumber}
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Bank</P>
                      <P style={styles.value}>
                        {data2?.selectedVendors?.vendorBank}
                      </P>
                    </View>
                  </View>
                }
              />
              <View style={styles.buttonWrap}>
                <Button
                  btnText="Confirm"
                  loading={loading}
                  onPress={() => {
                    // navigation.navigate("BankTransferScreen", {
                    //   country: country,
                    // });
                    deposit();
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.secBackground,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
});
