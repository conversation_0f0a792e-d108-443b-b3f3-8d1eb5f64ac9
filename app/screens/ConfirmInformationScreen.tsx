import React, { useRef, useState, useEffect } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  Dimensions,
  Text,
  TouchableOpacity,
  Image,
  Keyboard,
} from "react-native";
import Div from "../components/Div";
import { colors } from "../config/colors";
import P from "../components/P";
import { fonts } from "../config/Fonts";
import Button from "../components/Button";
import Link from "../components/Link";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import Input from "../components/Input";
import BottomComponent from "../components/BottomComponent";
import SignupHeader from "../components/SignupHeader";
import OnboardingCountrySelect from "../components/OnboardingCountrySelect";
import CountryCodeSelect from "../components/CountryCodeSelect";
import BottomSheet from "../components/BottomSheet";
import { Formik } from "formik";
import * as yup from "yup";

import DateOfBirthPicker from "../components/DatePicker";
import { useToast } from "../context/ToastContext";
import { validateDateOfBirthForFinancialServices } from "../Utils/ageValidation";
import AuthHeader from "../components/AuthHeader";
import H4 from "../components/H4";
import { CheckPhone } from "../RequestHandlers/User";

const screenHeight = Dimensions.get("window").height;
export default function ConfirmInformationScreen({ navigation, route }) {
  const { values1 } = route?.params;
  const [countryCode, setCountryCode] = useState("+90");
  const [flag, setFlag] = useState(require("../assets/turkey.png"));
  const [flag2, setFlag2] = useState(require("../assets/turkey.png"));
  const [show, setShow] = useState(false);
  const [show2, setShow2] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showdatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState("");
  const { handleToast } = useToast();

  const registerSchema2 = yup.object().shape({
    firstName: yup.string().required("First name is required"),
    middleName: yup.string(), // Optional field - no .required()
    lastName: yup.string().required("Last name is required"),
    phoneNumber: yup
      .string()
      .required("Phone number is required")
      .min(7, "Invalid mobile number")
      .matches(/^[0-9]+$/, "Phone number should not include letters"),
    dob: yup
      .string()
      .nullable()
      .required("Date of birth is required")
      .matches(
        /^(19|20)\d\d-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/,
        "Invalid date format (YYYY-MM-DD)"
      )
      .test("valid-date", "Invalid date of birth", function (value) {
        if (!value) return true;
        const date = new Date(value);
        return !isNaN(date.getTime());
      })
      .test("age-validation", "Invalid Date of Birth", function (value) {
        if (!value) return true;
        const ageValidation = validateDateOfBirthForFinancialServices(value);
        return ageValidation.isValid;
      }),
    homeCountry: yup.string().required("Home country is required"),
    residentAddress: yup.string().required("Residential address is required"),
  });

  return (
    <View style={styles.body}>
      <Div style={{ height: screenHeight }}>
        <Formik
          initialValues={{
            firstName: "",
            middleName: "",
            lastName: "",
            phoneNumber: "",
            dob: selectedDate || "",
            homeCountry: "",
            residentAddress: "",
          }}
          validationSchema={registerSchema2}
          onSubmit={async (values) => {
            Keyboard.dismiss();
            setLoading(true);
            try {
              const res = await CheckPhone(
                `${countryCode}${values.phoneNumber.trim().replace(/^0/, "")}`
              );
              if (res.error) {
                handleToast(res.message, "error");
              } else {
                navigation.navigate("CreatenameScreen", {
                  values1: values1,
                  values2: values,
                  countryCode: countryCode,
                });
              }
            } catch (error) {
              handleToast("Failed to verify phone number", "error");
            } finally {
              setLoading(false);
            }
          }}
        >
          {(formikProps) => (
            <>
              <ScrollView
                style={styles.container}
                automaticallyAdjustKeyboardInsets={true}
                contentContainerStyle={{ paddingBottom: 100 }}
                showsHorizontalScrollIndicator={false}
              >
                <AuthHeader style={{ width: "95%" }} navigation={navigation} />
                <View
                  style={{
                    width: "90%",
                    justifyContent: "center",
                    alignSelf: "center",
                    marginTop: 8,
                    alignItems: "center",
                  }}
                >
                  <H4 style={styles.text1}>Personal Information</H4>
                  <P style={styles.text2}>
                    Confirm & enter your personal{"\n"}details on your ID
                  </P>
                </View>
                <View style={styles.components}>
                  <Input
                    label="First name"
                    placeholder="John"
                    inputStyle={{ width: "85%" }}
                    onChangeText={formikProps.handleChange("firstName")}
                    value={formikProps.values.firstName}
                    onBlur={formikProps.handleBlur("firstName")}
                    autoCapitalize="none"
                    error={
                      formikProps.errors.firstName &&
                      formikProps.touched.firstName
                    }
                  />
                  {formikProps.errors.firstName &&
                    formikProps.touched.firstName && (
                      <P style={styles.errorText}>
                        {formikProps.errors.firstName}
                      </P>
                    )}
                  <Input
                    label="Middle name (optional)"
                    placeholder="Michael"
                    inputStyle={{ width: "85%" }}
                    contStyle={{ marginTop: 16, color: colors.black }}
                    onChangeText={formikProps.handleChange("middleName")}
                    value={formikProps.values.middleName}
                    onBlur={formikProps.handleBlur("middleName")}
                    autoCapitalize="none"
                    error={
                      formikProps.errors.middleName &&
                      formikProps.touched.middleName
                    }
                  />
                  {formikProps.errors.middleName &&
                    formikProps.touched.middleName && (
                      <P style={styles.errorText}>
                        {formikProps.errors.middleName}
                      </P>
                    )}
                  <Input
                    label="Last name"
                    placeholder="Doe"
                    inputStyle={{ width: "85%" }}
                    contStyle={{ marginTop: 16, color: colors.black }}
                    onChangeText={formikProps.handleChange("lastName")}
                    value={formikProps.values.lastName}
                    onBlur={formikProps.handleBlur("lastName")}
                    autoCapitalize="none"
                    error={
                      formikProps.errors.lastName &&
                      formikProps.touched.lastName
                    }
                  />
                  {formikProps.errors.lastName &&
                    formikProps.touched.lastName && (
                      <P style={styles.errorText}>
                        {formikProps.errors.lastName}
                      </P>
                    )}
                  <Input
                    label="Phone Number"
                    placeholder="8144855058"
                    inputStyle={{ width: "85%" }}
                    contStyle={{ marginTop: 16, color: colors.black }}
                    onChangeText={(text) => {
                      // Remove all white spaces and non-numeric characters
                      const cleanedText = text.replace(/\s/g, '').replace(/[^0-9]/g, '');
                      formikProps.setFieldValue("phoneNumber", cleanedText);
                    }}
                    value={formikProps.values.phoneNumber}
                    keyboardType={"numeric"}
                    onBlur={formikProps.handleBlur("phoneNumber")}
                    autoCapitalize="none"
                    error={
                      formikProps.errors.phoneNumber &&
                      formikProps.touched.phoneNumber
                    }
                    leftIcon={
                      <TouchableOpacity
                        style={[styles.pinInput]}
                        onPress={() => {
                          setShow(true);
                        }}
                      >
                        <Image
                          source={flag}
                          style={{
                            width: 24,
                            height: 24,
                            marginLeft: 16,
                            borderRadius: 100,
                            objectFit:
                              countryCode === "+234" ? "fill" : "cover",
                          }}
                        />
                        <P style={styles.pinTextInput}>{countryCode}</P>
                        <SvgXml xml={svg.dropDown} />
                      </TouchableOpacity>
                    }
                  />
                  {formikProps.errors.phoneNumber &&
                    formikProps.touched.phoneNumber && (
                      <P style={styles.errorText}>
                        {formikProps.errors.phoneNumber}
                      </P>
                    )}
                  <TouchableOpacity
                    onPress={() => {
                      setShowDatePicker(true);
                    }}
                  >
                    <Input
                      label="Date of birth"
                      placeholder="YYYY-MM-DD"
                      inputStyle={{ width: "85%" }}
                      editable={false}
                      contStyle={{ marginTop: 16 }}
                      onChangeText={formikProps.handleChange("dob")}
                      value={formikProps.values.dob}
                      onBlur={formikProps.handleBlur("dob")}
                      autoCapitalize="none"
                      error={formikProps.errors.dob && formikProps.touched.dob}
                      rightIcon={
                        <View
                          style={{
                            width: "15%",
                            height: "100%",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <SvgXml xml={svg.dropDown} />
                        </View>
                      }
                    />
                    {formikProps.errors.dob && formikProps.touched.dob && (
                      <P style={styles.errorText}>{formikProps.errors.dob}</P>
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => {
                      setShow2(true);
                    }}
                  >
                    <Input
                      // value={countryCode2}
                      label="Home Country"
                      placeholder="Turkey"
                      inputStyle={{ width: "85%" }}
                      contStyle={{ marginTop: 16 }}
                      onChangeText={formikProps.handleChange("homeCountry")}
                      value={formikProps.values.homeCountry}
                      onBlur={formikProps.handleBlur("homeCountry")}
                      autoCapitalize="none"
                      error={
                        formikProps.errors.homeCountry &&
                        formikProps.touched.homeCountry
                      }
                      leftIcon={
                        <Image
                          source={flag2}
                          style={{
                            width: 24,
                            height: 24,
                            marginLeft: 16,
                            borderRadius: 100,
                            objectFit:
                              formikProps.values.homeCountry === "Nigeria"
                                ? "fill"
                                : "cover",
                          }}
                        />
                        // <P style={styles.pinTextInput}>{flag2}</P>
                      }
                      editable={false}
                      rightIcon={
                        <View
                          style={{
                            //   backgroundColor: "red",
                            width: "15%",
                            height: "100%",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <SvgXml xml={svg.dropDown} />
                        </View>
                      }
                    />
                    {formikProps.errors.homeCountry &&
                      formikProps.touched.homeCountry && (
                        <P style={styles.errorText}>
                          {formikProps.errors.homeCountry}
                        </P>
                      )}
                  </TouchableOpacity>
                  <Input
                    label="Resident address"
                    placeholder="Enter your residential address"
                    inputStyle={{ width: "85%" }}
                    contStyle={{ marginTop: 16 }}
                    onChangeText={formikProps.handleChange("residentAddress")}
                    value={formikProps.values.residentAddress}
                    onBlur={formikProps.handleBlur("residentAddress")}
                    autoCapitalize="none"
                    error={
                      formikProps.errors.residentAddress &&
                      formikProps.touched.residentAddress
                    }
                  />
                  {formikProps.errors.residentAddress &&
                    formikProps.touched.residentAddress && (
                      <P style={styles.errorText}>
                        {formikProps.errors.residentAddress}
                      </P>
                    )}
                  <View style={{ marginTop: 32 }}>
                    <Button
                      btnText="Next"
                      onPress={formikProps.handleSubmit}
                      loading={loading}
                      // onPress={() => navigation.navigate("CreatenameScreen")}
                    />
                  </View>

                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "center",
                      marginTop: 32,
                    }}
                  >
                    <P
                      style={{
                        fontSize: 12,
                        lineHeight: 22.4,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      Do you have an account?{" "}
                    </P>
                    <Link
                      style={{
                        fontSize: 12,
                        lineHeight: 21,
                        textDecorationLine: "underline",
                      }}
                      onPress={() => navigation.navigate("NewLoginScreen")}
                    >
                      Login
                    </Link>
                  </View>
                </View>
                <BottomSheet
                  isVisible={show2}
                  backspaceText={"Home country"}
                  onClose={() => setShow2(false)}
                  showBackArrow={false}
                  components={
                    <OnboardingCountrySelect
                      onPress={(country) => {
                        const previousCountry = formikProps.values.homeCountry;
                        // Set flag using the country code
                        setFlag2({
                          uri: `https://flagcdn.com/w2560/${country.code.toLowerCase()}.png`,
                        });
                        // Only clear resident address if the country actually changed
                        if (
                          previousCountry !== country.name &&
                          previousCountry !== ""
                        ) {
                          formikProps.setFieldValue("residentAddress", "");
                        }

                        // Set home country value
                        formikProps.setFieldValue("homeCountry", country.name);

                        // Close the modal
                        setShow2(false);
                      }}
                    />
                  }
                  modalContentStyle={{ height: "80%" }}
                  extraModalStyle={{ height: "77%" }}
                  componentHolderStyle={{ flex: 1 }}
                />

                <BottomSheet
                  isVisible={showdatePicker}
                  backspaceText="Date of birth"
                  onClose={() => setShowDatePicker(false)}
                  showBackArrow={false}
                  components={
                    <DateOfBirthPicker
                      selectedDate={selectedDate}
                      onDateChange={(date) => {
                        setSelectedDate(date);
                        formikProps.setFieldValue("dob", date);
                      }}
                      closeModal={() => {
                        setShowDatePicker(false);
                      }}
                    />
                  }
                  modalContentStyle={{ height: "72%" }}
                  extraModalStyle={{ height: "70%" }}
                />

                <BottomSheet
                  isVisible={show}
                  backspaceText={"Phone number"}
                  onClose={() => setShow(false)}
                  showBackArrow={false}
                  components={
                    <CountryCodeSelect
                      onPress={(country) => {
                        setShow(false);
                        setCountryCode(country.tel_code);
                        setFlag({
                          uri: `https://flagcdn.com/w2560/${country.code.toLowerCase()}.png`,
                        });
                      }}
                    />
                  }
                  modalContentStyle={{ height: "80%" }}
                  extraModalStyle={{ height: "77%" }}
                  componentHolderStyle={{ flex: 1 }}
                />
              </ScrollView>
              <BottomComponent absolute={false} navigation={navigation} />
            </>
          )}
        </Formik>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
    height: screenHeight,
  },

  text1: {
    fontSize: 20,
    fontFamily: fonts.poppinsBold,
    lineHeight: 30,
  },
  text2: {
    fontSize: 14,
    lineHeight: 22.4,
    fontFamily: fonts.poppinsRegular,
    textAlign: "center",
  },
  components: {
    width: "90%",
    marginTop: 24,
    alignSelf: "center",
    marginBottom: 68,
    // backgroundColor:"red"
  },
  pinInput: {
    width: "35%",
    height: "100%",
    alignItems: "center",
    flexDirection: "row",
    borderRightColor: "#E6E5E5",
    borderRightWidth: 1,
  },
  pinTextInput: {
    fontSize: 12,
    textAlign: "center",
    color: "#161817",
    fontFamily: fonts.poppinsRegular,
    marginLeft: 16,
    marginRight: 8,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
});
