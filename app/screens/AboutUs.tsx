import React from "react";
import { StyleSheet, View, TouchableOpacity, Linking } from "react-native";
import { colors } from "../config/colors";
import Div from "../components/Div";
import AuthenticationHedear from "../components/AuthenticationHedear";
import P from "../components/P";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";

export default function AboutUs({ navigation }) {
  const abtLinks = [
    { name: "About us", uri: "https://www.sfxchange.co/en/privacy" },
    { name: "FAQ", uri: "https://www.sfxchange.co/en/Test_Flight/FAQs" },
    { name: "Privacy policy", uri: "https://sfx-1.gitbook.io/legal-and-policy/privacy-policy-for-sfx" },
    { name: "Terms & conditions", uri: "https://sfx-1.gitbook.io/legal-and-policy/terms-and-conditions" },
  ];
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="About us" navigation={navigation} />
        <View style={styles.container}>
          {abtLinks.map((item, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => {
                Linking.openURL(item.uri);
              }}
              style={{
                width: "100%",
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
                paddingTop: 16,
                paddingBottom: 16,
              }}
            >
              <P style={{ fontSize: 12 }}>{item.name}</P>
              <SvgXml xml={svg.arrowUpRight} />
            </TouchableOpacity>
          ))}
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  container: {
    width: "90%",
    paddingTop: 16,
    paddingBottom: 16,
    paddingLeft: 24,
    paddingRight: 24,
    borderRadius: 12,
    backgroundColor: colors.white,
    alignSelf: "center",
  },
});
