import React from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../config/Fonts";
import Div from "../components/Div";
import AuthenticationHedear from "../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "../components/P";
import MicroBtn from "../components/MicroBtn";
import { colors } from "../config/colors";
import DetailCard from "../components/DetailCard";
import Button from "../components/Button";
import Content from "../components/Content";

const { width, height } = Dimensions.get("window");

export default function AddMoneyScreen({ navigation }) {
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Add money" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <Content
                svg1={svg.smalllogo}
                header="SFx wallet"
                body="Instantly add money with your SFx username"
                onPress={() =>
                  navigation.navigate("AddMoneyAccountScreen")
                }
              />
              <Content
                svg1={svg.bankGreen}
                header="Local bank account"
                body="Add money from local bank accounts in 14 African countries. Arrives in 5–15 minutes."
                onPress={() => navigation.navigate("AddMoneyBankAccountScreen")}
              />
              <Content
                svg1={svg.mobile}
                header="Mobile money"
                body="Add money from a mobile money wallet in 14 African countries. Arrives in 5–15 minutes."
                onPress={() =>
                  navigation.navigate("AddMoneyMobileMoneyAccountSelect")
                }
              />
              <Content
                svg1={svg.p2p}
                header="Wallet address"
                body="Add money using your blockchain address. Instant arrival."
                bottomBorder={false}
                onPress={() => navigation.navigate("AddMoneyP2pScreen")}
              />
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    minHeight: 304,
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent:"center",
    alignItems: "center",
  },

  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
});
