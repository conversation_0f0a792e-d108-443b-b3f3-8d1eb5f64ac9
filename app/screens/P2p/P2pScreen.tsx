import React, { useState, useRef, useEffect } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { svg } from "../../config/Svg";
import { SvgXml } from "react-native-svg";
import NoteComponent from "../../components/NoteComponent";
import Input from "../../components/Input";
import BottomSheet from "../../components/BottomSheet";
import CountrySelect from "../../components/CountrySelect";
import Button from "../../components/Button";
import NoteComponent2 from "../../components/NoteComponent2";
import BarCodeScanner from "../../components/BarCodeScanner";
import { GetUserWallet, GetWalletById } from "../../RequestHandlers/Wallet";
import Loader from "../../components/ActivityIndicator";
import { useToast } from "../../context/ToastContext";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";

const WAValidator = require("multicoin-address-validator");

const baseHeight = 802;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function P2pScreen({ navigation, route }) {
  const [paymentType, setPaymentType] = useState(null);
  const [flag, setFlag] = useState(require("../../assets/turkey.png"));
  const [country, setCountry] = useState("");
  const [showCountries, setShowCountries] = useState(false);
  const [activeNetwork, setActiveNetwork] = useState(null);
  const [displayArrivalTime, setDisplayArrivalTime] = useState(null);
  const [showQrCode, setShowQrCode] = useState(false);
  const [wallets, setWallets] = useState<any>([]);
  const [waldetails, setWalDetails] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [depositAddress, setDepositAddress] = useState("");
  const [error, setError] = useState(false);
  const [chain, setChain] = useState("");
  const [allNetwork, setAllNetwork] = useState([]);
  const { data } = route?.params || "";
  const { asset } = route?.params || "";
  const [detectedNetwork, setDetectedNetwork] = useState("");
  const [netError, setNetError] = useState(false);
  const [isProd, setIsProd] = useState(true);
  const [matchedNetworks, setMatchedNetworks] = useState<any>([]);
  const { handleToast } = useToast();
  const [activeSendType, setActiveSendType] = useState<any>(null);

  // State to control available chains - easily modify this array to enable/disable chains
  // Available options: ["POLYGON", "AVALANCHE", "ARBITRUM", "BASE"]
  // SOLANA is always "coming soon" regardless of this setting
  const [enabledChains, setEnabledChains] = useState<string[]>([
    "POLYGON",
    "AVALANCHE",
    "ARBITRUM",
    "BASE",
  ]);

  const net = isProd ? "testnest" : "mainnet";
  const chainNameMapping = {
    CHAIN_POLYGON: "POLYGON",
    CHAIN_AVALANCHE: "AVALANCHE",
    CHAIN_ARBITRUM: "ARBITRUM",
    CHAIN_BASE: "BASE",
    CHAIN_SOLANA: "SOLANA",
  };

  // Base network configurations
  const allNetworks = [
    {
      name: "POLYGON",
      altName: "Polygon",
      arrival: "Money arrival in 2 minutes",
      chain: "matic",
      isRecommended: true,
      isComingSoon: false,
      icon: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/info/logo.png",
    },
    {
      name: "AVALANCHE",
      altName: "Avalanche - C Chain",
      arrival: "Money arrival in 13 minutes",
      chain: "avax",
      isRecommended: false,
      isComingSoon: false,
      icon: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/avalanchec/info/logo.png",
    },
    {
      name: "ARBITRUM",
      altName: "Arbitrum One",
      arrival: "Money arrival in 13 minutes",
      chain: "eth",
      isRecommended: false,
      isComingSoon: false,
      icon: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/arbitrum/info/logo.png",
    },
    {
      name: "BASE",
      altName: "Base",
      arrival: "Money arrival in 13 minutes",
      chain: "eth",
      isRecommended: false,
      isComingSoon: false,
      icon: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/base/info/logo.png",
    },
    {
      name: "SOLANA",
      altName: "Solana",
      arrival: "Money arrival in 13 minutes",
      chain: "sol",
      isRecommended: false,
      isComingSoon: true,
      icon: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/solana/info/logo.png",
    },
  ];

  // Filter networks based on enabled chains
  const dummyNetworks = allNetworks.map((network) => ({
    ...network,
    isComingSoon: network.name === "SOLANA" ? true : false, // Only SOLANA is "coming soon"
    isUnavailable:
      network.name !== "SOLANA" && !enabledChains.includes(network.name), // Others are unavailable if not enabled
  }));
  const getWallet = async () => {
    setLoading(true);
    try {
      const wallet = await await withApiErrorToast(
        GetUserWallet(),
        handleToast
      );
      if (wallet.wallets) {
        setLoading(false);
        setWallets(wallet.wallets);
        if (wallet.wallets.length === 1) {
          setPaymentType(wallet.wallets[0]);
        }
        // if (data != null || data != "" || data || undefined) {
        //   if (data.length === 42) {
        //     setPaymentType(wallet.wallets[0]);
        //     setDepositAddress(data);
        //   } else if (data.length === 34) {
        //     setPaymentType(wallet.wallets[1]);
        //     setDepositAddress(data);
        //   } else {
        //   }
        // }
      } else {
        setLoading(false);
        handleToast("Error getting wallet", "error");
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const getWalletById = async () => {
    try {
      const walletDetails = await GetWalletById(paymentType.id);
      if (walletDetails.wallet) {
        setLoading(false);
        setWalDetails(walletDetails.wallet);
        setError(false);
      }
    } catch (error) {}
  };
  const getWalById = async () => {
    try {
      const walletDetails = await withApiErrorToast(
        GetWalletById(paymentType.id),
        handleToast
      );
      if (walletDetails.wallet) {
        setLoading(false);
        setWalDetails(walletDetails.wallet);
        const transformedAssets = walletDetails.wallet.assets.map((asset) => ({
          ...asset,
          chain: chainNameMapping[asset.chain] || asset.chain,
        }));
        setAllNetwork(transformedAssets);
        const firstAsset = transformedAssets[0];
      } else {
      }
    } catch (error) {}
  };

  useEffect(() => {
    getWalById();
  }, [paymentType]);

  useEffect(() => {
    getWallet();
    if (data) {
      handleAddressInput(data);
    }
    if (asset != "" && asset != null && asset != undefined) {
      setPaymentType(asset);
    }
  }, []);

  const handleScan = (index) => {
    if (index.length === 42) {
      handleAddressInput(index);
      setActiveNetwork(null);
    } else if (index.length === 34) {
      handleAddressInput(index);
      setActiveNetwork(null);
    }
  };
  const detectNetwork = (address) => {
    const availableNetworks = [];

    dummyNetworks.forEach((network) => {
      // Only check networks that are enabled (not coming soon and not unavailable)
      if (
        enabledChains.includes(network.name) &&
        !network.isComingSoon &&
        !network.isUnavailable
      ) {
        const isValid = WAValidator.validate(address, network.chain); // Validate per network
        if (isValid) {
          availableNetworks.push(network.name); // Add valid networks to the list
        }
      }
    });
    return availableNetworks; // Return the list of available networks
  };

  const handleAddressInput = (input) => {
    setDepositAddress(input);
    const network = detectNetwork(input);
    if (network.length > 0) {
      setMatchedNetworks(network);
      setError(false);
      // setDisplayArrivalTime(
      //   networks.find((net) => net.name === network)?.arrival || null
      // );
    } else {
      setError(true); // Show an error for invalid address
      setDisplayArrivalTime(null);
    }
  };

  const sendType = [
    { id: 1, Title: "Instant", subT: "1 minute and 1% fee" },
    { id: 2, Title: "Standard", subT: "13 minute and 0% fee" },
  ];
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Wallet address" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <NoteComponent2
                text={
                  enabledChains.length === 1 &&
                  enabledChains.includes("POLYGON")
                    ? "SFx money app currently supports sending USDC on the Polygon Blockchain only. Other networks are temporarily unavailable."
                    : `SFx money app supports sending USDC on ${enabledChains.join(
                        ", "
                      )} blockchain${enabledChains.length > 1 ? "s" : ""}.`
                }
                type="red"
                contStyle={{
                  backgroundColor: colors.redSubtle,
                  marginBottom: 16,
                }}
              />

              {/* Network Selection - Show First */}
              <TouchableOpacity
                onPress={() => {
                  setShowCountries(true);
                }}
              >
                <Input
                  value={chain}
                  label="Network"
                  placeholder="Select network"
                  inputStyle={{ width: "65%", color: "#161817" }}
                  contStyle={{
                    marginTop: 0,
                  }}
                  editable={false}
                  rightIcon={
                    <View
                      style={{
                        width: "15%",
                        height: "100%",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <SvgXml xml={svg.dropDown} />
                    </View>
                  }
                  error={netError}
                />
              </TouchableOpacity>
              {netError && <P style={styles.errorText}>Select a network</P>}

              {/* Send Type Selection - Only show for non-Polygon networks */}
              {chain && chain !== "POLYGON" && (
                <>
                  <P
                    style={{
                      fontSize: 12,
                      fontFamily: fonts.poppinsRegular,
                      marginBottom: 6,
                      marginTop: 16,
                    }}
                  >
                    Send type
                  </P>
                  {sendType.map((item) => (
                    <TouchableOpacity
                      key={item.id}
                      style={[
                        styles.sendTypeOption,
                        {
                          borderColor:
                            activeSendType?.id === item.id
                              ? colors.primary
                              : colors.stroke,
                        },
                      ]}
                      onPress={() => {
                        setActiveSendType(item);
                      }}
                    >
                      <View>
                        <P
                          style={{
                            fontSize: 12,
                            fontFamily: fonts.poppinsRegular,
                          }}
                        >
                          {item.Title}
                        </P>
                        <P
                          style={{
                            fontSize: 10,
                            fontFamily: fonts.poppinsRegular,
                          }}
                        >
                          {item.subT}
                        </P>
                      </View>
                      {activeSendType?.id === item.id ? (
                        <SvgXml xml={svg.purple_check} />
                      ) : (
                        <View
                          style={{
                            width: 16,
                            height: 16,
                            borderRadius: 100,
                            borderWidth: 1,
                            borderColor: colors.stroke,
                          }}
                        ></View>
                      )}
                    </TouchableOpacity>
                  ))}
                </>
              )}

              {/* Show content when network is selected and (for Polygon OR send type is selected for other networks) */}
              {chain && (chain === "POLYGON" || activeSendType !== null) ? (
                <>
                  {/* <P
                    style={{
                      fontSize: 12,
                      fontFamily: fonts.poppinsRegular,
                      marginBottom: 6,
                    }}
                  >
                    Asset
                  </P> */}
                  {/* <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                    }}
                  >
                    {wallets.map((item, index) => (
                      <TouchableOpacity
                        key={index}
                        onPress={() => {
                          if (data) {
                            setPaymentType(item);
                            setActiveNetwork(null);
                            setDetectedNetwork("");
                            setChain("");
                          } else {
                            setPaymentType(item);
                            setDepositAddress("");
                            setActiveNetwork(null);
                            setDetectedNetwork("");
                            setChain("");
                          }
                        }}
                        disabled={item.rate == "Coming soon.."}
                      >
                        <View
                          style={[
                            styles.pyItem,
                            {
                              borderColor:
                                paymentType === item ||
                                item?.asset === asset?.asset
                                  ? colors.primary
                                  : colors.stroke,
                              width:
                                item.length > 1
                                  ? (126 / baseWidth) * width
                                  : (80 * width) / 100,
                           
                            },
                          ]}
                        >
                          <SvgXml
                            xml={
                              item.asset === "USDT" ? svg.tather : svg.usdCoin
                            }
                          />
                          <View style={{ marginLeft: 8 }}>
                            <P style={styles.pyName}>
                              {item.asset === "USDT" ? "Tether" : "USD coin"}
                            </P>
                        
                          </View>
                          {paymentType === item && (
                            <SvgXml
                              xml={svg.ppTick}
                              style={{ position: "absolute", right: 16 }}
                            />
                          )}
                        </View>
                      </TouchableOpacity>
                    ))}
                  </View> */}
                  <Input
                    value={depositAddress}
                    placeholder="Enter wallet address"
                    label={"Wallet address"}
                    contStyle={{ marginTop: chain == "POLYGON" ? 16 : 0 }}
                    onChangeText={(text) => handleAddressInput(text)}
                    inputStyle={{ paddingLeft: 16, paddingRight: 16 }}
                    error={error}
                    rightIcon={
                      <TouchableOpacity
                        onPress={() => {
                          setShowQrCode(true);
                        }}
                        style={{
                          width: 25,
                          height: 25,
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <SvgXml xml={svg.qrcode} pointerEvents="none" />
                      </TouchableOpacity>
                    }
                  />

                  {error && <P style={styles.errorText}>Invalid address</P>}

                  {/* Show network match status when address is entered */}
                  {depositAddress && chain && !error && (
                    <View
                      style={{
                        marginTop: 16,
                        paddingHorizontal: 16,
                        paddingVertical: 10,
                        backgroundColor: colors.secBackground,
                        borderRadius: 8,
                        minHeight: (44 / baseHeight) * height,
                        alignItems: "center",
                        flexDirection: "row",
                        justifyContent: "space-between",
                      }}
                    >
                      <P
                        style={{
                          fontSize: 12,
                          color: colors.black,
                          fontFamily: fonts.poppinsRegular,
                        }}
                      >
                        {matchedNetworks.includes(chain)
                          ? "Matched network"
                          : "Unmatched network"}
                      </P>
                      <SvgXml
                        xml={
                          matchedNetworks.includes(chain)
                            ? svg.green_check
                            : svg.fail
                        }
                      />
                    </View>
                  )}
                </>
              ) : (
                <></>
              )}

              {/* {displayArrivalTime && (
                <View style={{ marginTop: 6 }}>
                  <NoteComponent2 text={displayArrivalTime} />
                </View>
              )} */}
            </View>
            <View style={{ width: "80%", alignSelf: "center", marginTop: 32 }}>
              <Button
                btnText="Next"
                // onPress={handleValidation}
                onPress={() => {
                  // Check if network is selected
                  if (!chain) {
                    setNetError(true);
                    return;
                  }

                  // Check if send type is selected for non-Polygon networks
                  if (chain !== "POLYGON" && !activeSendType) {
                    handleToast("Please select a send type", "error");
                    return;
                  }

                  // Check if asset is selected
                  if (!paymentType) {
                    handleToast("Please select an asset", "error");
                    return;
                  }

                  // Check address validation
                  if (
                    paymentType.asset === "USDT" &&
                    depositAddress.length < 42
                  ) {
                    setError(true);
                    return;
                  } else if (
                    paymentType.asset === "USDC" &&
                    depositAddress.length < 34
                  ) {
                    setError(true);
                    return;
                  }

                  // Check if network matches the address
                  if (depositAddress && !matchedNetworks.includes(chain)) {
                    handleToast(
                      "Wallet address does not match the selected network",
                      "error"
                    );
                    return;
                  }

                  // All validations passed
                  setError(false);
                  setNetError(false);
                  navigation.navigate("P2pAmountScreen", {
                    walletdetails: waldetails,
                    depositAddress: depositAddress,
                    chain: chain,
                    sendType:
                      activeSendType?.Title === "Instant" ? "fast" : "standard",
                  });
                }}
                disabled={!chain}
              />
            </View>
          </View>
        </ScrollView>
      </Div>
      <BottomSheet
        isVisible={showCountries}
        showBackArrow={false}
        backspaceText="Network"
        onClose={() => setShowCountries(false)}
        modalContentStyle={{ height: dummyNetworks.length < 2 ? "45%" : "75%" }}
        extraModalStyle={{ height: dummyNetworks.length < 2 ? "43%" : "73%" }}
        components={
          <ScrollView
            contentContainerStyle={{ paddingBottom: 300 }}
            showsVerticalScrollIndicator={false}
          >
            <View style={{ paddingTop: 24 }}>
              <P
                style={{
                  fontSize: 12,
                  color: colors.gray,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                select a blockchain to send USDC
              </P>
              <View>
                {dummyNetworks.map((item, index) => {
                  return (
                    <TouchableOpacity
                      key={index}
                      disabled={item.isComingSoon || item.isUnavailable}
                      onPress={() => {
                        if (!item.isComingSoon && !item.isUnavailable) {
                          setActiveNetwork(item.name);
                          setChain(item.name);
                          setShowCountries(false);
                          // Reset send type when network changes
                          if (item.name === "POLYGON") {
                            setActiveSendType({
                              id: 2,
                              Title: "Standard",
                              subT: "13 minute and 0% fee",
                            });
                          } else {
                            setActiveSendType(null);
                          }
                        }
                      }}
                    >
                      <View
                        style={{
                          width: "100%",
                          padding: 16,
                          paddingTop: 8,
                          paddingBottom: 8,
                          borderRadius: 8,
                          marginBottom: 16,
                          marginTop: 6,
                          justifyContent: "center",
                          backgroundColor:
                            activeNetwork === item.name
                              ? colors.lowOpPrimary2
                              : "transparent",
                          opacity:
                            item.isComingSoon || item.isUnavailable ? 0.6 : 1,
                        }}
                      >
                        <View
                          style={{
                            flexDirection: "row",
                            alignItems: "center",
                            marginBottom: 4,
                          }}
                        >
                          <Image
                            source={{ uri: item.icon }}
                            style={{
                              width: 24,
                              height: 24,
                              borderRadius: 12,
                              marginRight: 12,
                            }}
                            resizeMode="contain"
                          />
                          <P style={{ fontSize: 12, lineHeight: 18, flex: 1 }}>
                            {item.altName}{" "}
                            {item.isRecommended && (
                              <P
                                style={{
                                  fontSize: 10,
                                  fontFamily: fonts.poppinsMedium,
                                }}
                              >
                                (Recommended)
                              </P>
                            )}
                          </P>
                        </View>
                        <P
                          style={{
                            fontSize: 12,
                            color: item.isComingSoon
                              ? colors.gray
                              : colors.gray,
                            fontFamily: fonts.poppinsRegular,
                          }}
                        >
                          {item.arrival}
                        </P>

                        {(item.isComingSoon || item.isUnavailable) && (
                          <View
                            style={{
                              minWidth: 80,
                              alignItems: "center",
                              justifyContent: "center",
                              paddingTop: 4,
                              paddingBottom: 4,
                              paddingRight: 10,
                              paddingLeft: 10,
                              position: "absolute",
                              right: 0,
                              top: 8,
                              borderRadius: 100,
                              backgroundColor: item.isComingSoon
                                ? colors.orange || "#FFA500"
                                : colors.gray || "#808080",
                            }}
                          >
                            <P
                              style={{
                                fontSize: 10,
                                color: colors.white,
                                fontFamily: fonts.poppinsMedium,
                              }}
                            >
                              {item.isComingSoon
                                ? "Coming Soon"
                                : "Network Unavailable"}
                            </P>
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                  );
                })}
                <View style={{ marginTop: 16 }}>
                  <NoteComponent2
                    type="red"
                    contStyle={{ backgroundColor: colors.redSubtle }}
                    text={`Please note that minimum Deposit/ withdrawal is $1 USDC. Always make sure you confirm asset and Network`}
                  />
                </View>
              </View>
            </View>
          </ScrollView>
        }
      />
      {loading && <Loader />}
      <BarCodeScanner
        visible={showQrCode}
        onClose={() => setShowQrCode(false)}
        onScan={handleScan}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: "100%",
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    padding: 24,
  },
  pyItem: {
    borderRadius: 8,
    borderWidth: 1,
    // marginBottom: 16,
    alignItems: "center",
    flexDirection: "row",
    paddingTop: (10 / baseHeight) * height,
    paddingBottom: (10 / baseHeight) * height,
    paddingLeft: (14 / baseWidth) * width,
    paddingRight: (14 / baseWidth) * width,
    // padding: 16,
  },
  pyName: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
  },
  rate: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
  },
  noteCont: {
    width: "100%",
    padding: 16,
    paddingTop: 8,
    paddingBottom: 8,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  sendTypeOption: {
    width: "100%",
    minHeight: 44,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderRadius: 8,
    marginBottom: 16,
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 5.5,
  },
});
