import React from "react";
import { Dimensions, StyleSheet, View } from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import Button from "../../components/Button";
import Input from "../../components/Input";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
const { width, height } = Dimensions.get("window");
export default function P2pScreen2({ navigation }) {
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="P2P wallet" navigation={navigation} />
        <View style={styles.cardCont}>
          <Input
            placeholder="0x43404kdk&C04F913ud69i33"
            label={"Wallet address"}
            rightIcon={<SvgXml xml={svg.qrcode} />}
          />
        </View>
        <View style={styles.btnCont}>
          <Button
            btnText="Next"
            onPress={() => navigation.navigate("P2pAmountScreen")}
          />
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    width,
    height,
    backgroundColor: colors.lowOpPrimary2,
  },
  cardCont: {
    width: "90%",
    padding: 24,
    borderRadius: 12,
    marginTop: 32,
    backgroundColor: colors.white,
    alignSelf: "center",
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
});
