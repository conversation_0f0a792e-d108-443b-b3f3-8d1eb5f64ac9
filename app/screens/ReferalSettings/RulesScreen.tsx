import React, { useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  ScrollView,
  Image,
  Text,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { colors } from "../../config/colors";
import AuthenticationHedear2 from "../../components/AuthenticationHedear2";
import { TouchableOpacity } from "react-native-gesture-handler";
import NoteComponent2 from "../../components/NoteComponent2";

const { width, height } = Dimensions.get("window");

export default function RulesScreen({ navigation }) {
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear2 text="Referral" navigation={navigation} />
        <ScrollView
          contentContainerStyle={{ flexGrow: 1, paddingBottom: "15%" }}
        >
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <View style={{ width: "90%" }}>
                <P>Referral Rules & Guidelines</P>
              </View>
              <View
                style={{
                  width: "90%",
                  flexDirection: "row",
                  marginTop: 16,
                }}
              >
                <Text> • </Text>
                <P style={styles.text}>
                  Genuine Referrals: Only unique and legitimate users are
                  eligible for referral rewards. Referrals made to duplicate
                  accounts or accounts created solely for the purpose of earning
                  referral rewards will be disqualified.
                </P>
              </View>
              <View style={styles.textCats}>
                <Text> • </Text>
                <P style={styles.text}>
                  Transaction Completion Requirement: SFx Points are credited
                  only after the referred user completes their first eligible
                  transaction within the app. This ensures the quality of
                  referrals and reduces the risk of fraud.
                </P>
              </View>
              <View style={styles.textCats}>
                <Text> • </Text>
                <P style={styles.text}>
                  Non-Transferable Points: Points earned through referrals are
                  non-transferable and can only be used within the SFx app. They
                  cannot be converted to cash or transferred to other users.
                </P>
              </View>
              <View style={styles.textCats}>
                <Text> • </Text>
                <P style={styles.text}>
                  Referral Limits: Users may refer an unlimited number of
                  friends, but each referral must meet the eligibility criteria
                  to earn points.
                </P>
              </View>
              <View style={styles.textCats}>
                <Text> • </Text>
                <P style={styles.text}>
                  Eligibility of Referred Users: The referred user must be new
                  to SFx, meaning they have never registered or held an account
                  before.
                </P>
              </View>
              <View style={styles.textCats}>
                <Text> • </Text>
                <P style={styles.text}>
                  Referral Expiry: If the referred user does not complete their
                  first transaction within 30 days of signing up, the referral
                  will expire, and no points will be awarded.
                </P>
              </View>
              <View style={styles.textCats}>
                <Text> • </Text>
                <P style={styles.text}>
                  Promotion-Specific Rules: Occasionally, SFx may run special
                  promotions that offer additional points or benefits for
                  referrals. These promotions will have specific terms and
                  conditions that override standard referral rules.
                </P>
              </View>

              <View style={{ width: "90%", marginTop: 24, marginBottom: 24 }}>
                <NoteComponent2
                  text={`  Any fraudulent activity, such as creating fake accounts or manipulating the referral system, will result in the revocation of earned points and potential suspension of accounts.`}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    marginTop: -16,
  },
  detailWrap: {
    width: "85%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent:"center",
    alignItems: "center",
    paddingTop: 24,
    marginBottom: 24,
  },
  card: {
    height: 50,
    width: "85%",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  code: {
    backgroundColor: "#F7F4FF",
    width: 264,
    height: 66,
    borderWidth: 1,
    borderColor: "#E6E5E5",
    borderStyle: "dashed",
    borderRadius: 7,
    marginTop: 32,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    gap: 35,
    paddingRight: 19,
  },
  line: {
    width: "80%",
    height: 1,
    borderWidth: 1,
    borderColor: "#E6E5E5",
    borderStyle: "dashed",
    marginTop: 32,
    marginBottom: 32,
  },
  circle: {
    width: 24,
    height: 24,
    borderRadius: 1000,
    backgroundColor: "#F7F4FF",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  underText: { fontSize: 10, color: "#A5A1A1", textAlign: "center" },
  ott: { width: 86, height: 80, alignItems: "center" },
  detailWrap2: {
    // padding: 24,
    width: "100%",
    alignSelf: "center",
    height: 246,
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent: "center",
    alignItems: "center",
    marginTop: 60,
  },
  deatilsHead: {
    width: "100%",
    height: 42,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: "5%",
  },
  textCats: {
    width: "90%",
    flexDirection: "row",
    marginTop: 10,
    paddingRight: 10,
  },
  text: { fontSize: 12, fontFamily: fonts.poppinsRegular },
});
