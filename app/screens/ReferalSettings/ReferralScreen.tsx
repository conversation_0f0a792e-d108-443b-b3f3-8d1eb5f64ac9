import React, { useCallback, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  ScrollView,
  Image,
  Platform,
  Share,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { colors } from "../../config/colors";
import AuthenticationHedear2 from "../../components/AuthenticationHedear2";
import { TouchableOpacity } from "react-native-gesture-handler";
import NoteComponent2 from "../../components/NoteComponent2";
import { GetReferals, GetUserDetails } from "../../RequestHandlers/User";
import Loader from "../../components/ActivityIndicator";
import { useFocusEffect } from "@react-navigation/native";
import * as Clipboard from "expo-clipboard";
import { formatDate, formatDate2 } from "../../components/FormatDate";
import * as Sharing from "expo-sharing";
import { useToast } from "../../context/ToastContext";

const { width, height } = Dimensions.get("window");

export default function ReferralScreen({ navigation }) {
  const [refCode, setRefCode] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [referrals, setReferrals] = useState<any>([]);
  const [firstName, setFirstName] = useState("");
  const { handleToast } = useToast();

  const onShare = async () => {
    try {
      const result = await Share.share({
        message: `Hi there! 👋
${firstName} has invited you to join SFx Money App - your trusted digital financial partner.
Download the SFx Money App and use this referral code to get started:
${refCode}
Get started here: https://www.sfxchange.co/en
Join now and experience seamless digital banking!`,
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
        } else {
        }
      } else if (result.action === Share.dismissedAction) {
      }
    } catch (error) { }
  };
  function ensureHttps(url) {
    if (url?.startsWith("http://")) {
      return url?.replace("http://", "https://");
    }
    return url; // Return the URL as is if it already starts with "https://"
  }
  const getUserDetetails = async () => {
    setLoading(true);
    try {
      const res = await GetUserDetails();
      if (res.referralCode) {
        setRefCode(res.referralCode);
        setFirstName(res.firstName);
      } else {
        handleToast(res?.message, "error");
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const getReferals = async () => {
    try {
      const res = await GetReferals(1, 10);
      if (res.items) {
        setReferrals(res.items);
      } else {
        handleToast(res.message, "error");
      }
    } catch (error) { }
  };
  useFocusEffect(
    useCallback(() => {
      getUserDetetails();
      getReferals();
    }, [])
  );
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear2
          text="Referral"
          navigation={navigation}
          iconComp={
            <TouchableOpacity
              onPress={() => navigation.navigate("RulesScreen")}
            >
              <P
                style={{
                  fontSize: 12,
                  color: "#8B52FF",
                  textDecorationLine: "underline",
                }}
              >
                Rules
              </P>
            </TouchableOpacity>
          }
        />
        <ScrollView
          contentContainerStyle={{ flexGrow: 1, paddingBottom: "35%" }}
        >
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <P style={{ fontSize: 16, fontFamily: fonts.poppinsMedium }}>
                Refer friends!
              </P>
              <P
                style={{
                  fontSize: 12,
                  color: "#A5A1A1",
                  textAlign: "center",
                  paddingLeft: 24,
                  paddingRight: 24,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Use your referral code to invite your friends and earn once they
                join, verify and fund their account
              </P>
              <Image
                source={require("../../assets/giftBox.png")}
                style={{ width: 135, height: 114.21, marginTop: 24 }}
              />
              <View style={styles.code}>
                <View style={{}}>
                  <P
                    style={{ color: "#A5A1A1", fontSize: 12, marginBottom: 2 }}
                  >
                    Your referral code
                  </P>
                  <TouchableOpacity
                    onPress={async () => {
                      try {
                        await Clipboard.setStringAsync(refCode);
                        handleToast("Referral code copied to clipboard", "success");
                      } catch (error) {
                        handleToast("Failed to copy code", "error");
                      }
                    }}
                  >
                    <P
                      style={{
                        color: "#000000",
                        fontSize: 16,
                        fontFamily: fonts.poppinsBold,
                        textAlign: "center",
                      }}
                    >
                      {refCode}
                    </P>
                  </TouchableOpacity>
                </View>
                <View style={{ position: "absolute", right: 19, flexDirection: 'row', gap: 12 }}>
                  <TouchableOpacity
                    style={{
                      width: 24,
                      height: 24,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                    onPress={async () => {
                      try {
                        await Clipboard.setStringAsync(refCode);
                        handleToast("Referral code copied to clipboard", "success");
                      } catch (error) {
                        handleToast("Failed to copy code", "error");
                      }
                    }}
                  >
                    <SvgXml xml={svg.copy} width={20} height={20}  />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={{
                      width: 24,
                      height: 24,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                    onPress={onShare}
                  >
                    <SvgXml xml={svg.shareLightPurple} />
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.line}></View>
              <View>
                <NoteComponent2
                  text={"You will receive your reward once your friends"}
                />
              </View>
              <View
                style={{
                  width: "90%",
                  flexDirection: "row",
                  marginTop: 16,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <View
                  style={{
                    width: "90%",
                    flexDirection: "row",
                    alignSelf: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <View style={styles.ott}>
                    <View style={styles.circle}>
                      <P style={{ fontSize: 10 }}>1</P>
                    </View>
                    <P style={styles.underText}>Sign up with referral code</P>
                  </View>
                  <View style={styles.ott}>
                    <View style={styles.circle}>
                      <P style={{ fontSize: 10 }}>2</P>
                    </View>
                    <P style={styles.underText}>Verify their identity</P>
                  </View>
                  <View style={styles.ott}>
                    <View style={styles.circle}>
                      <P style={{ fontSize: 10 }}>3</P>
                    </View>
                    <P style={styles.underText}>
                      Add up to $50 in their SFx wallet
                    </P>
                  </View>
                </View>
              </View>
            </View>

            <View style={styles.detailWrap2}>
              <View style={styles.deatilsHead}>
                <P style={{ color: "#A5A1A1" }}>Referrals</P>
                {referrals.length > 0 && (
                  <TouchableOpacity
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                    }}
                    onPress={() => navigation.navigate("ReferralListScreen")}
                  >
                    <P
                      style={{
                        color: "#8B52FF",
                        textDecorationLine: "underline",
                        alignItems: "center",
                      }}
                    >
                      See all
                    </P>
                  </TouchableOpacity>
                )}
              </View>
              {referrals.length === 0 ? (
                <View style={{ width: "100%", alignItems: "center" }}>
                  <SvgXml
                    xml={svg.userGroup}
                    style={{ marginTop: 32, marginBottom: 16 }}
                  />
                  <P
                    style={{ fontFamily: fonts.poppinsMedium, marginBottom: 4 }}
                  >
                    No referral!
                  </P>
                  <P
                    style={{
                      color: "#A5A1A1",
                      fontFamily: fonts.poppinsRegular,
                      width: 190,
                      fontSize: 11,
                      textAlign: "center",
                    }}
                  >
                    You have no referral yet, copy code to refer
                  </P>
                </View>
              ) : (
                <>
                  {referrals?.slice(0, 4)?.map((item, index) => (
                    <View
                      key={index}
                      style={{
                        width: "100%",
                        paddingTop: 13,
                        paddingBottom: 13,
                        paddingLeft: 16,
                        paddingRight: 16,
                        borderBottomWidth:
                          index === referrals?.length - 1 ? 0 : 1,
                        borderColor: colors.stroke,
                        flexDirection: "row",
                        justifyContent: "space-between",
                      }}
                    >
                      <View
                        style={{ flexDirection: "row", alignItems: "center" }}
                      >
                        <Image
                          source={{
                            uri: ensureHttps(item?.referredUser?.picture),
                          }}
                          style={{
                            width: 24,
                            height: 24,
                            borderRadius: 100,
                            objectFit: "cover",
                          }}
                        />
                        <View style={{ marginLeft: 12 }}>
                          <P style={{ fontSize: 12 }}>
                            {item?.referredUser?.firstName}{" "}
                            {item?.referredUser?.lastName}
                          </P>
                          <P
                            style={{
                              fontSize: 12,
                              color: colors.gray,
                              fontFamily: fonts.poppinsRegular,
                            }}
                          >
                            {formatDate(item?.createdAt)}
                          </P>
                        </View>
                      </View>
                      <P style={{ fontSize: 12 }}>
                        +{item?.accumulatedPoints}
                        <P
                          style={{
                            fontSize: 10,
                            fontFamily: fonts.poppinsRegular,
                          }}
                        >
                          SFxp
                        </P>
                      </P>
                    </View>
                  ))}
                </>
              )}
            </View>
          </View>
        </ScrollView>
      </Div>
      {loading && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    marginTop: -16,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent:"center",
    alignItems: "center",
    paddingTop: 24,
    paddingBottom: 24,
  },
  card: {
    height: 50,
    width: "85%",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  code: {
    backgroundColor: "#F7F4FF",
    width: "85%",
    height: 66,
    borderWidth: 1,
    borderColor: "#E6E5E5",
    borderStyle: "dashed",
    borderRadius: 7,
    marginTop: 32,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    // gap: 35,
  },
  line: {
    width: "85%",
    height: 1,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    marginTop: 32,
    marginBottom: 32,
  },
  circle: {
    width: 24,
    height: 24,
    borderRadius: 1000,
    backgroundColor: "#F7F4FF",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  underText: {
    fontSize: 10,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
    textAlign: "center",
  },
  ott: { width: 86, height: 80, alignItems: "center" },
  detailWrap2: {
    // padding: 24,
    width: "90%",
    alignSelf: "center",
    minHeight: 246,
    backgroundColor: "white",
    borderRadius: 12,
    alignItems: "center",
    marginTop: 16,
  },
  deatilsHead: {
    width: "100%",
    height: 42,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: "5%",
  },
});
