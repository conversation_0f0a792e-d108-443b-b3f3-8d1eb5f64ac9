import React, { useState, useCallback } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  TouchableOpacity,
  Text,
  ScrollView,
  FlatList,
  ActivityIndicator,
} from "react-native";
import P from "../../components/P";
import Link from "../../components/Link";
import Button from "../../components/Button";
import { fonts } from "../../config/Fonts";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import {
  ClaimSfxPoint,
  GetReferals,
  GetSfxPoint,
} from "../../RequestHandlers/User";
import { useFocusEffect } from "@react-navigation/native";
import { formatDate2 } from "../../components/FormatDate";
import { formatDate } from "../../components/FormatDate";
import { useToast } from "../../context/ToastContext";

const groupByDate = (items) => {
  return items.reduce((acc, item) => {
    const dateOnly = new Date(item.createdAt).toISOString().split("T")[0];
    acc[dateOnly] = acc[dateOnly] || [];
    acc[dateOnly].push(item);
    return acc;
  }, {});
};

const baseHeight = 812;
const { width, height } = Dimensions.get("window");
export default function ReferralListScreen({ navigation }) {
  const [stImg, setStImg] = useState(require("../../assets/clock.png"));
  const [stText, setStText] = useState("Sent money is pending");
  const [hideBal, setHideBal] = useState(false);
  const [amount, setAmount] = useState(0);
  const [amc, setAmc] = useState("SFx");
  const [limit, setLimit] = useState(20);
  const [referrals, setReferrals] = useState<any>([]);
  const [loading1, setLoading1] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [totalItem, setTotalItem] = useState(0);
  const [length, setLength] = useState(0);
  const [loading, setLoading] = useState(false);
  const [actionLoad, setActionLoad] = useState(false);
  const {handleToast} = useToast()

  const getSfxPoint = async () => {
    try {
      const res = await GetSfxPoint();
      if (res.totalpoints) {
        setAmount(res.totalpoints);
      }
    } catch (error) {
      ;
    }
  };

  const groupedTransactions = groupByDate(referrals);
  useFocusEffect(
    useCallback(() => {
      setLoading(true);
      setReferrals([]);
      setLimit(20);
      setHasMoreData(true);
      getRefWithLimit(20);
      getSfxPoint();
    }, [])
  );
  const fetchMoreTransactions = () => {
    if (!loading1 && hasMoreData) {
      const newLimit = limit + 20;
      setLimit(newLimit);
      // Call getRef with the new limit directly
      getRefWithLimit(newLimit, true);
    }
  };

  const getRefWithLimit = async (currentLimit: number, loadMore = false) => {
    if (loading1) return; // Prevent multiple requests

    setLoading1(true);
    try {
      const res = await GetReferals(1, currentLimit);
      if (res) {
        setTotalItem(res.meta.totalItems);
        setLength(res.items.length);

        if (res.items.length === 0) {
          setHasMoreData(false);
        } else {
          // Sort transactions
          const sortedTransactions = res.items.sort((a: any, b: any) => {
            return (
              new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
            );
          });

          setReferrals(sortedTransactions);

          // Check if there's more data to fetch
          setHasMoreData(res.items.length < res.meta.totalItems);
        }
      }
    } catch (error) {
      console.error("Error fetching referrals:", error);
    } finally {
      setLoading1(false);
      setLoading(false);
    }
  };

  const claimSfxpoint = async () => {
    setActionLoad(true);
    try {
      const res = await ClaimSfxPoint();
      if (res.error) {
        handleToast(res.message, "error");
      } else {
        handleToast(res.message, "success");
      }
    } catch (error) {
      ;
    } finally {
      setActionLoad(false);
    }
  };
  function ensureHttps(url) {
    if (url?.startsWith("http://")) {
      return url?.replace("http://", "https://");
    }
    return url;
  }
  const renderTransactionItem = ({ item, index }) => (
    <TouchableOpacity disabled={true}>
      <View style={styles.item} key={index}>
        <Image
          source={{ uri: ensureHttps(item?.referredUser?.picture) }}
          style={{
            width: 24,
            height: 24,
            borderRadius: 100,
            objectFit: "cover",
          }}
        />
        <View style={{ marginLeft: 12 }}>
          <P style={styles.transactionAmount}>
            {" "}
            {item?.referredUser?.firstName}
            {item?.referredUser?.middleName ? ` ${item?.referredUser?.middleName}` : ""} {item?.referredUser?.lastName}
          </P>
          <P style={styles.transactionDate}> {formatDate(item.createdAt)}</P>
        </View>
        <View
          style={{
            position: "absolute",
            right: 16,
            top: 16,
            bottom: 16,
            alignItems: "flex-end",
          }}
        >
          <P
            style={{
              fontSize: 12,
              fontFamily: fonts.poppinsMedium,
            }}
          >
            +{item?.accumulatedPoints}
            <P
              style={{
                fontSize: 10,
                fontFamily: fonts.poppinsRegular,
              }}
            >
              SFxp
            </P>
          </P>
        </View>
      </View>
    </TouchableOpacity>
  );
  const renderDateGroup = ({ item: date }) => (
    <View style={{ marginTop: 24 }}>
      <P style={styles.datCat}>{formatDate2(date)}</P>
      <FlatList
        data={groupedTransactions[date]} // Transactions for this date
        renderItem={renderTransactionItem}
        keyExtractor={(item, index) => `${date}-${index}`}
      />
    </View>
  );

  const dateKeys = Object.keys(groupedTransactions);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          navigation={navigation}
          text="Reward"
          iconComp={
            <View style={{ position: "absolute", right: 0 }}>
              <Link
                style={{
                  fontSize: 12,
                  fontFamily: fonts.poppinsMedium,
                  textDecorationLine: "underline",
                }}
                onPress={() => {
                  navigation.navigate("RulesScreen");
                }}
              >
                Rules
              </Link>
            </View>
          }
        />
        {/* <ScrollView> */}
        <View style={styles.itemBox}>
          <View style={styles.accountBalance}>
            <View style={{ flexDirection: "row", alignItems: "center" }}>
              <P style={{ fontSize: 12 }}>Available SFx balance</P>
              <TouchableOpacity
                style={{
                  marginLeft: 8,
                  width: 20,
                  height: 20,
                  justifyContent: "center",
                }}
                onPress={() => setHideBal((prevState) => !prevState)} // Toggle state
              >
                <SvgXml
                  width={16}
                  height={16}
                  xml={hideBal === true ? svg.eyeClose : svg.eyeOpen}
                />
                {/* Switch icon */}
              </TouchableOpacity>
            </View>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
                alignItems: "center",

                //   backgroundColor:"red"
              }}
            >
              {/* @ts-ignore */}
              <TouchableOpacity onPress={() => toggleModal()}>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <P style={{ fontSize: 24, lineHeight: 36 }}>
                    {hideBal
                      ? "******"
                      : `${amount != undefined ? amount?.toFixed(2) : 0}`}
                    {""}
                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      {hideBal ? "***" : amc}
                    </Text>
                  </P>
                </View>
              </TouchableOpacity>
              <View>
                <P style={{ fontSize: 12, fontFamily: fonts.poppinsRegular }}>
                  Redeemed
                </P>
                <P style={{ textAlign: "right" }}>
                  {hideBal ? "******" : `0.00`}{" "}
                  <P style={{ fontSize: 12, fontFamily: fonts.poppinsRegular }}>
                    {hideBal ? "***" : `SFx`}
                  </P>
                </P>
              </View>
            </View>
          </View>
        </View>
        <View style={{ width: "90%", alignSelf: "center" }}>
          {Object.keys(groupedTransactions).length === 0 ? (
            <View style={styles.emptyCont}>
              <SvgXml
                xml={svg.walletFace}
                style={{ marginTop: (20 * height) / 100, marginBottom: 16 }}
              />
              <P
                style={{
                  fontFamily: fonts.poppinsMedium,
                  marginBottom: 4,
                }}
              >
                No referrals!
              </P>
              <P
                style={{
                  color: "#A5A1A1",
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                You have no referrals yet
              </P>
            </View>
          ) : (
            <View>
              {/* {Object.keys(groupedTransactions).map((date, index) => (
                  <View key={index} style={{ marginTop: 24 }}>
                    <P style={styles.datCat}>{date}</P>
                    <View>
                      {groupedTransactions[date].map((item, index) => (

                      ))}
                    </View>
                  </View>
                ))} */}

              <View
                style={{
                  width: "100%",
                  alignSelf: "center",
                  // paddingBottom: 300
                  // marginBottom: 300,
                }}
              >
                <FlatList
                  data={Object.keys(groupedTransactions)}
                  renderItem={renderDateGroup}
                  keyExtractor={(date, index) => `group-${index}`}
                  showsVerticalScrollIndicator={false}
                  onEndReached={() => {
                    fetchMoreTransactions();
                  }}
                  onEndReachedThreshold={0.3}
                  contentContainerStyle={{ paddingBottom: 300 }}
                  ListFooterComponent={
                    loading1 && length < totalItem ? (
                      <ActivityIndicator
                        color={colors.primary}
                        style={{ marginTop: 16 }}
                      />
                    ) : null
                  }
                />
              </View>
            </View>
          )}
        </View>

        {amount < 1000 ? (
          <></>
        ) : (
          <View
            style={{
              position: "absolute",
              bottom: 0,
              height: 86,
              backgroundColor: colors.secBackground,
              width: "100%",
            }}
          >
            <Button
              btnText="Redeem SFx point"
              style={{ width: "80%", alignSelf: "center", marginTop: 8 }}
              loading={actionLoad}
              onPress={claimSfxpoint}
            />
          </View>
        )}

        {/* </ScrollView> */}
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  itemBox: {
    width: "90%",
    alignItems: "center",
    marginTop: 16,
    alignSelf: "center",
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    width: "80%",
    fontSize: 12,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
  accountBalance: {
    alignItems: "flex-start",
    // backgroundColor:"red",
  },
  balanceText: {
    fontSize: 12,
    color: "rgba(22, 24, 23, 0.6)",
  },
  balanceAmount: {
    fontSize: 24,
    // fontWeight: "bold",
    fontFamily: "poppins-semibold",
    color: "rgba(22, 24, 23, 1)",
  },
  addMoneyButton: {
    // marginTop: 10,
    // paddingVertical: 8,
    paddingTop: 4,
    paddingRight: 16,
    paddingBottom: 4,
    paddingLeft: 16,
    backgroundColor: "rgba(140, 82, 255, 1)",
    borderRadius: 20,
  },
  addMoneyText: {
    color: "#fff",
    fontSize: 12,
  },
  actions: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  actionButton: {
    alignItems: "center",
  },
  actionButtonText: {
    marginTop: 8,
    fontSize: 12,
    color: "rgba(22, 24, 23, 1)",
  },
  item: {
    width: "100%",
    padding: 16,
    backgroundColor: colors.white,
    marginTop: 8,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
  },
  transactionAmount: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    // fontWeight: "bold",
  },
  transactionDate: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  emptyCont: {
    width: "100%",
    height: (60 * height) / 100,
    // backgroundColor: "red",
    alignItems: "center",
    // justifyContent: "center",
  },
  datCat: {
    fontSize: 12,
  },
});
