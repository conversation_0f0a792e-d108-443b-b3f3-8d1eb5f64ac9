import React, { useCallback, useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  FlatList,
  ActivityIndicator,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import ListItem from "../../components/ListItem";
import BottomSheet from "../../components/BottomSheet";
import {
  GetNewTransationByFilters,
  GetTransation,
  GetTransationByFilters,
} from "../../RequestHandlers/Wallet";
import { useFocusEffect } from "@react-navigation/native";
import { formatDate } from "../../components/FormatDate";
import { HistorySkeleton } from "../../Skeletons/Skeletons";
import Loader from "../../components/ActivityIndicator";
import DateOfBirthPicker from "../../components/DatePicker";
import NetInfo from "@react-native-community/netinfo";
import Offline from "../../components/ErrorSate/Offline";
import FailedToLoad from "../../components/ErrorSate/FailedToLoad";
import {
  getSymbol,
  getTransactionIcon,
  getTransactionLabel,
  TransactionClick,
} from "../../Utils/TransactionClick";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import { handleToast } from "../../components/Toast";
import {
  formatNumberWithCommas,
  formatToTwoDecimals,
} from "../../Utils/numberFormat";

const { width, height } = Dimensions.get("window");

const formatMonthYear = (monthKey) => {
  const [year, month] = monthKey.split("-");
  const date = new Date(parseInt(year), parseInt(month) - 1);
  return date.toLocaleDateString("en-US", { month: "short", year: "numeric" });
};

const groupByCat = (items) => {
  return items.reduce((acc, item) => {
    acc[item.cat] = acc[item.cat] || [];
    acc[item.cat].push(item);
    return acc;
  }, {});
};

const baseHeight = 800;
const baseWidth = 360;
export default function HistoryScreen({ navigation }) {
  const [categories, setCategories] = useState(false);
  const [allStarts, setAllStarts] = useState(false);
  const [activeFilter, setActiveFilter] = useState("All categories");
  const [activeTransactionType, setActiveTransactionType] =
    useState("All Transactions");
  const [activeStatus, setActiveStatus] = useState("All status");
  const [showTranStatus, setShowTranStatus] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [monthlyData, setMonthlyData] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [stat, setStat] = useState("all");
  const [cat, setCat] = useState("all");
  const [totalItem, setTotalItem] = useState(0);
  const [length, setLength] = useState(0);
  const [selectedDate, setSelectedDate] = useState("");
  const [fetchError, setfetchError] = useState(false);
  let onEndReachedCalledDuringMomentum = false;
  function capitalizeFirstLetter(word) {
    if (!word) return ""; // handle empty strings
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
  }
  const [isOnline, setIsOnline] = useState(false);
  const [expandedMonths, setExpandedMonths] = useState<{
    [key: string]: boolean;
  }>({});
  const [gateWay, setGateWay] = useState("all");

  const TIMEOUT_DURATION = 10000;
  const filters = [
    { name: "All categories", cat: "Transaction", filter: "all" },
    { name: "Add money", cat: "Transaction", filter: "add-money" },
    { name: "Send money", cat: "Transaction", filter: "send-money" },
    { name: "All Transactions", cat: "Transaction type", filter: "all" },
    { name: "SFx", cat: "Transaction type", filter: "sfx" },
    { name: "Local bank", cat: "Transaction type", filter: "bank" },
    { name: "Mobile money", cat: "Transaction type", filter: "mobile-money" },
    { name: "Crypto", cat: "Transaction type", filter: "crypto" },
  ];

  const status = [
    { name: "All status", filter: "all" },
    { name: "Successful", filter: "completed" },
    { name: "Pending", filter: "pending" },
    { name: "Failed", filter: "failed" },
  ];
  const getEmptyMessage = () => {
    if (cat === "add-money") {
      return {
        title: "No tansaction",
        description: "You don't have any transaction in this category",
      };
    } else if (cat === "send-money") {
      return {
        title: "No tansaction",
        description: "You don't have any transaction in this category",
      };
    } else if (cat === "card") {
      return {
        title: "No tansaction",
        description: "You don't have any transaction in this category",
      };
    } else if (cat === "sfx-point") {
      return {
        title: "No tansaction",
        description: "You don't have any transaction in this category",
      };
    } else if (stat === "completed") {
      return {
        title: "No tansaction",
        description: "You don't have any transaction in this category",
      };
    } else if (stat === "pending") {
      return {
        title: "No tansaction",
        description: "You don't have any transaction in this category",
      };
    } else if (stat === "failed") {
      return {
        title: "No tansaction",
        description: "You don't have any transaction in this category",
      };
    } else {
      // Default message for all categories and statuses
      return {
        title: "No history",
        description: "You have no transaction history yet.",
      };
    }
  };
  const emptyMessage = getEmptyMessage();
  const [loading1, setLoading1] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(100);
  const [hasMoreData, setHasMoreData] = useState(true);

  // Function to get transaction items
  const getTransaction = async (loadMore = false) => {
    if (loading1) return; // Prevent fetching if already loading
    setLoading1(true);

    const currentPage = loadMore ? page : 1;

    try {
      const transactions = await withApiErrorToast(
        GetNewTransationByFilters(
          currentPage,
          pageSize,
          cat,
          stat,
          gateWay,
          "",
          selectedDate
        ),
        handleToast
      );
      if (transactions) {
        setLoading(false);
        setTotalItem(transactions.pagination.totalCount);
        setLength(transactions.data.length);

        // Check if we have more data based on pagination
        setHasMoreData(transactions.pagination.hasNextPage);

        // Update monthly data - merge new months if loading more, otherwise replace
        setMonthlyData((prevMonthlyData) => {
          if (!loadMore) {
            return transactions.data;
          }

          // When loading more, merge months to avoid duplicates
          const existingMonthKeys = new Set(
            prevMonthlyData.map((month: any) => `${month.year}-${month.month}`)
          );

          const newMonths = transactions.data.filter(
            (month: any) =>
              !existingMonthKeys.has(`${month.year}-${month.month}`)
          );

          console.log(
            "Loading more - existing months:",
            existingMonthKeys.size,
            "new months:",
            newMonths.length
          );

          const newMonthlyData = [...prevMonthlyData, ...newMonths];

          // If loading more data, auto-expand all new month groups (default behavior)
          if (loadMore && newMonths.length > 0) {
            const newMonthKeys = newMonths.map(
              (monthData: any) =>
                `${monthData.year}-${String(monthData.month).padStart(2, "0")}`
            );

            // Auto-expand all new months by default
            setExpandedMonths((prev: any) => {
              const updated = { ...prev };
              newMonthKeys.forEach((monthKey: string) => {
                if (!(monthKey in updated)) {
                  updated[monthKey] = true; // Auto-expand all new months by default
                }
              });
              return updated;
            });
          }

          return newMonthlyData;
        });

        // Note: We no longer need to maintain a flattened transactions array
        // since we're working directly with the grouped monthly data

        // Update page number for next load
        if (loadMore) {
          setPage(currentPage + 1);
        } else {
          setPage(2); // Next page will be 2
        }
      }

      if (transactions.error) {
        setfetchError(true);
      } else {
        setfetchError(false);
      }
    } catch (error) {
      setLoading(false);
      console.error(error);
    } finally {
      setLoading1(false);
    }
  };

  // Convert monthlyData to the expected groupedTransactions format
  const groupedTransactions = React.useMemo(() => {
    const grouped = {};
    monthlyData.forEach((monthData: any) => {
      const monthKey = `${monthData.year}-${String(monthData.month).padStart(
        2,
        "0"
      )}`;
      grouped[monthKey] = monthData.transactions;
    });
    return grouped;
  }, [monthlyData]);

  const groupedFilters = groupByCat(filters);

  // Function to toggle month expansion
  const toggleMonth = (monthKey: string) => {
    setExpandedMonths((prev) => ({
      ...prev,
      [monthKey]: !prev[monthKey],
    }));
  };

  // Fetch transactions and update based on filters (stat)

  const checkConnection = () => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      if (state.isConnected === false) {
        setIsOnline(false);
      } else if (state.isConnected === true) {
        setIsOnline(true);
      }
    });
  };
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      if (state.isConnected === false) {
        setIsOnline(false);
      } else if (state.isConnected === true) {
        setIsOnline(true);
      }
    });
  }, []);
  useEffect(() => {
    setLoading(true);
  }, []);

  useFocusEffect(
    useCallback(() => {
      setLoading(true);
      setMonthlyData([]); // Reset monthly data
      setPage(1); // Reset to first page
      setHasMoreData(true); // Reset hasMoreData flag
      setExpandedMonths({}); // Reset expanded months when filters change
      getTransaction();
    }, [stat, cat, gateWay, selectedDate])
  );

  // Auto-expand all months when monthly data is loaded (default behavior)
  useEffect(() => {
    if (monthlyData.length > 0) {
      // Create month keys for all months
      const allMonthKeys = monthlyData.map(
        (monthData: any) =>
          `${monthData.year}-${String(monthData.month).padStart(2, "0")}`
      );

      // Auto-expand all months by default
      setExpandedMonths((prev) => {
        const updated = { ...prev };
        allMonthKeys.forEach((monthKey: string) => {
          if (!(monthKey in updated)) {
            updated[monthKey] = true; // Auto-expand all months by default
          }
        });
        return updated;
      });
    }
  }, [monthlyData]);

  // Function to load more data (fetch next page)
  const fetchMoreTransactions = () => {
    if (!loading1 && hasMoreData) {
      getTransaction(true);
    }
  };

  // Alternative: Check if there are any collapsed months with data before loading more
  const hasCollapsedMonthsWithData = () => {
    const monthKeys = Object.keys(groupedTransactions);
    return monthKeys.some(
      (monthKey) =>
        !expandedMonths[monthKey] && groupedTransactions[monthKey].length > 0
    );
  };

  // Flatten the monthly data into a single array with headers
  const flattenedData = React.useMemo(() => {
    const result = [];

    // Sort monthly data by year and month (newest first)
    const sortedMonthlyData = [...monthlyData].sort((a, b) => {
      if (a.year !== b.year) return b.year - a.year;
      return b.month - a.month;
    });

    sortedMonthlyData.forEach((monthData) => {
      const monthKey = `${monthData.year}-${String(monthData.month).padStart(
        2,
        "0"
      )}`;

      // Sort transactions within the month by date (newest first)
      const sortedTransactions = monthData.transactions.sort(
        (a: any, b: any) =>
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      );

      // Add month group with header and transactions including new API data
      result.push({
        type: "monthGroup",
        monthKey: monthKey,
        monthName: monthData.monthName
          ? `${monthData.monthName} ${monthData.year}`
          : formatMonthYear(monthKey),
        transactionCount: monthData.transactionCount,
        totalReceived: monthData.totalReceived,
        totalSent: monthData.totalSent,
        isExpanded: expandedMonths[monthKey] || false,
        transactions: sortedTransactions,
        id: `group-${monthKey}`,
      });
    });

    return result;
  }, [monthlyData, expandedMonths]);

  // Memoized function to render each month group with all its transactions
  const renderItem = React.useCallback(
    ({ item }) => {
      if (item.type !== "monthGroup") return null;

      return (
        <View
          style={{
            marginBottom: 24,
            borderRadius: 12,
            overflow: "hidden",
            backgroundColor: colors.white,
          }}
        >
          {/* Month Header */}
          <TouchableOpacity
            style={{
              // marginTop: index === 0 ? 16 : 24,
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              paddingVertical: item.isExpanded ? 12 : 12,
              paddingHorizontal: 16,
            }}
            onPress={() => toggleMonth(item.monthKey)}
          >
            <View style={{}}>
              <View
                style={{ flexDirection: "row", alignItems: "center", gap: 4 }}
              >
                <P style={styles.datCat}>{item.monthName}</P>
                <SvgXml
                  xml={svg.arrowDown}
                  style={{
                    transform: [
                      { rotate: item.isExpanded ? "180deg" : "0deg" },
                    ],
                  }}
                />
              </View>
              <P
                style={{
                  marginTop: 4,
                  fontSize: 12,
                  color: colors.gray,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Sent{" "}
                <P style={styles.amt}>
                  ${formatToTwoDecimals(item.totalSent || 0)}
                </P>{" "}
                • Received{" "}
                <P style={styles.amt}>
                  ${formatToTwoDecimals(item.totalReceived || 0)}
                </P>
              </P>
            </View>
          </TouchableOpacity>

          {/* Transactions Container - Only show if expanded */}
          {item.isExpanded && (
            <View style={{ marginTop: 0 }}>
              {item.transactions.map((transaction, transactionIndex) => (
                <TouchableOpacity
                  key={`transaction-${transaction.id || transactionIndex}`}
                  onPress={() => {
                    TransactionClick(transaction, navigation);
                  }}
                  // style={{ marginTop: transactionIndex === 0 ? 0 : 8 }}
                >
                  <View style={styles.item}>
                    <SvgXml xml={getTransactionIcon(transaction, svg)} />
                    <View style={{ marginLeft: 12 }}>
                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                          gap: 4,
                        }}
                      >
                        <P style={styles.transactionAmount}>
                          <P
                            style={{
                              fontSize: 12,
                              fontFamily: fonts.poppinsMedium,
                              textDecorationLine:
                                transaction.status === "failed"
                                  ? "line-through"
                                  : "none",
                            }}
                          >
                            {`${
                              transaction.sfxToSfxCurrency &&
                              transaction.sfxToSfxCurrency !== "USD"
                                ? getSymbol(transaction.sfxToSfxCurrency)
                                : "$"
                            }${
                              transaction.sfxToSfxCurrency &&
                              transaction.sfxToSfxCurrency !== "USD"
                                ? formatToTwoDecimals(transaction.localAmount)
                                : transaction?.amount
                                ? formatToTwoDecimals(transaction.amount)
                                : "..."
                            }`}
                            {/* <P style={{ fontSize: 10 }}>
                        {" "}
                        {transaction.sfxToSfxCurrency
                          ? transaction.sfxToSfxCurrency
                          : "USD"}
                      </P> */}
                          </P>{" "}
                          {getTransactionLabel(transaction)}
                        </P>
                        {transaction.type !== "sfxPoint" && (
                          <P
                            // @ts-ignore
                            style={[
                              styles.transactionDate,
                              {
                                color: transaction.status
                                  .toLowerCase()
                                  .includes("completed")
                                  ? colors.green
                                  : transaction.status
                                      .toLowerCase()
                                      .includes("pending") ||
                                    transaction.status
                                      .toLowerCase()
                                      .includes("processing") ||
                                    transaction.status
                                      .toLowerCase()
                                      .includes("processing")
                                  ? colors.yellow
                                  : colors.red,
                              },
                            ]}
                          >
                            {transaction?.status === "completed"
                              ? "Successful"
                              : transaction?.status === "processing"
                              ? "Pending"
                              : transaction?.status?.includes(
                                  "awaiting-confirmation"
                                )
                              ? "Pending"
                              : capitalizeFirstLetter(transaction?.status)}
                          </P>
                        )}
                      </View>
                      <P style={styles.transactionDate}>
                        {formatDate(transaction?.updatedAt)}
                      </P>
                    </View>
                    <View
                      style={{
                        position: "absolute",
                        right: 16,
                        top: 16,
                        bottom: 16,
                        alignItems: "flex-end",
                        justifyContent: "center",
                      }}
                    >
                      <P
                        style={{
                          fontSize: 12,
                          fontFamily: fonts.poppinsRegular,
                        }}
                      >
                        {transaction.fee > 0 ? "-" : ""}$
                        {transaction?.type === "DEPOSIT"
                          ? formatNumberWithCommas(
                              transaction.fee / transaction.exchangeRate
                            )
                          : formatNumberWithCommas(transaction.fee)}
                      </P>

                      <P
                        // @ts-ignore
                        style={[
                          styles.transactionDate,
                          { color: colors.dGray },
                        ]}
                      >
                        $
                        {transaction.status === "completed"
                          ? formatToTwoDecimals(
                              Number(transaction?.balanceAfter)
                            )
                          : formatToTwoDecimals(0)}
                      </P>
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      );
    },
    [expandedMonths, toggleMonth, navigation]
  );

  // Memoized keyExtractor for better performance
  const keyExtractor = React.useCallback((item) => item.id, []);

  // Memoized getItemLayout for better performance (optional, if items have consistent height)
  const getItemLayout = React.useCallback(
    (data, index) => ({
      length: 80, // Approximate item height - adjust based on your actual item height
      offset: 80 * index,
      index,
    }),
    []
  );

  if (loading) {
    return <Loader />;
  }

  return (
    <View style={styles.body}>
      <Div>
        {loading ? (
          <Loader />
        ) : (
          <>
            <AuthenticationHedear
              showBackArrow={false}
              text="History"
              navigation={navigation}
              iconComp={
                <TouchableOpacity
                  style={{ position: "absolute", right: 0 }}
                  onPress={() => {
                    if (selectedDate === "") {
                      setShowDatePicker(true);
                    } else {
                      setSelectedDate("");
                    }
                  }}
                >
                  <SvgXml
                    xml={selectedDate === "" ? svg.calander : svg.calander2}
                  />
                </TouchableOpacity>
              }
            />

            <View
              style={{
                width: "100%",
                borderTopWidth: 1,
                borderBottomWidth: 1,
                borderColor: colors.stroke,
                alignItems: "center",
              }}
            >
              <View style={{ width: "90%", height: 64, flexDirection: "row" }}>
                <TouchableOpacity
                  style={{
                    flexDirection: "row",
                    width: "50%",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  onPress={() => setCategories(true)}
                >
                  <P style={{ fontSize: 12, marginRight: 8 }}>
                    {activeFilter === "All categories" &&
                    activeTransactionType === "All Transactions"
                      ? "All categories"
                      : `${activeFilter}${
                          activeTransactionType !== "All Transactions"
                            ? ` • ${activeTransactionType}`
                            : ""
                        }`}
                  </P>
                  <SvgXml xml={svg.chevDown} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={{
                    flexDirection: "row",
                    width: "50%",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  onPress={() => setShowTranStatus(true)}
                >
                  <P style={{ fontSize: 12, marginRight: 8 }}>{activeStatus}</P>
                  <SvgXml xml={svg.chevDown} />
                </TouchableOpacity>
              </View>
            </View>

            {!isOnline ? (
              <Offline
                onPress={() => {
                  checkConnection();
                  getTransaction();
                }}
              />
            ) : fetchError ? (
              <FailedToLoad
                onPress={() => {
                  setLoading(true);
                  getTransaction();
                }}
              />
            ) : (
              <>
                <View style={styles.contentBody}>
                  {Object.keys(groupedTransactions).length === 0 ? (
                    <View style={styles.emptyCont}>
                      <SvgXml xml={svg.noTransaction} />
                      <P
                        style={{
                          fontFamily: fonts.poppinsMedium,
                          lineHeight: 21,
                          marginTop: 16,
                        }}
                      >
                        {emptyMessage.title}
                      </P>
                      <P
                        style={{
                          fontSize: 13,
                          fontFamily: fonts.poppinsRegular,
                          color: colors.gray2,
                          paddingLeft: 20,
                          paddingRight: 20,
                          textAlign: "center",
                        }}
                      >
                        {emptyMessage.description}
                      </P>
                    </View>
                  ) : (
                    <View
                      style={{
                        width: "90%",
                        alignSelf: "center",
                      }}
                    >
                      <FlatList
                        data={flattenedData}
                        renderItem={renderItem}
                        keyExtractor={keyExtractor}
                        showsVerticalScrollIndicator={false}
                        onEndReached={() => {
                          if (!onEndReachedCalledDuringMomentum) {
                            fetchMoreTransactions();
                            onEndReachedCalledDuringMomentum = true;
                          }
                        }}
                        onMomentumScrollBegin={() => {
                          onEndReachedCalledDuringMomentum = false;
                        }}
                        onEndReachedThreshold={0.3}
                        contentContainerStyle={{
                          paddingTop: 16,
                          paddingBottom: 300,
                        }}
                        removeClippedSubviews={true}
                        maxToRenderPerBatch={10}
                        windowSize={10}
                        initialNumToRender={5}
                        updateCellsBatchingPeriod={100}
                        ListFooterComponent={
                          loading1 && length < totalItem ? (
                            <ActivityIndicator
                              color={colors.primary}
                              style={{ marginTop: 16 }}
                            />
                          ) : null
                        }
                      />
                    </View>
                  )}
                </View>
              </>
            )}
          </>
        )}
      </Div>
      {/* {loading && <Loader />} */}
      <BottomSheet
        isVisible={categories}
        onClose={() => setCategories(false)}
        showBackArrow={false}
        backspaceText="Filter"
        modalContentStyle={{ minHeight: "70%" }}
        components={
          <ScrollView contentContainerStyle={{ paddingBottom: 50 }}>
            <View style={{ width: "100%", alignSelf: "center" }}>
              {Object.keys(groupedFilters).map((item) => (
                <React.Fragment key={`filter-group-${item}`}>
                  <P
                    style={{
                      marginTop: 24,
                      fontSize: 12,
                      fontFamily: fonts.poppinsRegular,
                    }}
                    key={`filter-category-${item}`}
                  >
                    {item}
                  </P>
                  <View style={{ flexWrap: "wrap", flexDirection: "row" }}>
                    {groupedFilters[item].map((filterItem: any) => (
                      <TouchableOpacity
                        onPress={() => {
                          if (filterItem.cat === "Transaction type") {
                            // Handle Transaction Type filters
                            setActiveTransactionType(filterItem.name);
                            setGateWay(filterItem.filter);
                          } else {
                            // Handle Transaction Category filters
                            setActiveFilter(filterItem.name);
                            setCat(filterItem.filter);
                          }
                          setCategories(false);
                        }}
                        key={`filter-item-${filterItem.name}`}
                        style={{
                          paddingLeft: 16,
                          paddingRight: 16,
                          paddingTop: 13,
                          paddingBottom: 13,
                          borderWidth: 1,
                          borderColor:
                            filterItem.cat === "Transaction type"
                              ? activeTransactionType === filterItem.name
                                ? colors.primary
                                : colors.stroke
                              : activeFilter === filterItem.name
                              ? colors.primary
                              : colors.stroke,
                          // backgroundColor:
                          //   activeFilter === filterItem.name
                          //     ? colors.primary
                          //     : colors.lowOpPrimary3,
                          marginRight: 16,
                          marginTop: 16,
                          borderRadius: 8,
                          flexDirection: "row",
                          gap: 8,
                          alignItems: "center",
                        }}
                      >
                        <P
                          style={{
                            fontSize: 12,
                            lineHeight: 18,
                            color: colors.black,
                            fontFamily: fonts.poppinsRegular,
                          }}
                        >
                          {filterItem.name}
                        </P>
                        <SvgXml
                          width={16}
                          height={16}
                          xml={
                            filterItem.cat === "Transaction type"
                              ? activeTransactionType === filterItem.name
                                ? svg.checked
                                : svg.check
                              : activeFilter === filterItem.name
                              ? svg.checked
                              : svg.check
                          }
                        />
                      </TouchableOpacity>
                    ))}
                  </View>
                </React.Fragment>
              ))}
            </View>
          </ScrollView>
        }
      />
      <BottomSheet
        isVisible={showTranStatus}
        onClose={() => setShowTranStatus(false)}
        showBackArrow={false}
        backspaceText="Filter"
        components={
          <ScrollView>
            <P
              style={{
                fontSize: 12,
                fontFamily: fonts.poppinsRegular,
                marginTop: 23,
              }}
            >
              Status
            </P>
            <View style={{ flexDirection: "row", flexWrap: "wrap", gap: 16 }}>
              {status.map((item) => (
                <TouchableOpacity
                  key={`status-${item.name}`}
                  onPress={() => {
                    setActiveStatus(item.name);
                    setStat(item.filter);
                    setShowTranStatus(false);
                  }}
                  style={{
                    flexDirection: "row",
                    paddingTop: 13,
                    paddingBottom: 13,
                    paddingLeft: 16,
                    paddingRight: 16,
                    marginTop: 8,
                    borderRadius: 10,
                    borderWidth: 1,
                    borderColor:
                      activeStatus === item.name
                        ? colors.primary
                        : colors.stroke,
                    // backgroundColor:
                    //   activeStatus === item.name
                    //     ? colors.lowOpPrimary3
                    //     : "transparent",
                    gap: 8,
                  }}
                >
                  <P style={{ fontSize: 12, fontFamily: fonts.poppinsRegular }}>
                    {item.name}
                  </P>
                  <SvgXml
                    width={16}
                    height={16}
                    xml={activeStatus === item.name ? svg.checked : svg.check}
                  />
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        }
      />
      <BottomSheet
        isVisible={showDatePicker}
        backspaceText="Date"
        onClose={() => setShowDatePicker(false)}
        showBackArrow={false}
        components={
          <DateOfBirthPicker
            dateofbirth={false}
            selectedDate={selectedDate}
            onDateChange={(date) => {
              setSelectedDate(date);
            }}
            closeModal={() => {
              setShowDatePicker(false);
            }}
          />
        }
        modalContentStyle={{ height: "65%" }}
        extraModalStyle={{ height: "63%" }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.secBackground,
  },
  btnCard: {
    width: "90%",
    minHeight: 156,
    backgroundColor: "white",
    alignSelf: "center",
    marginTop: 24,
    borderRadius: 12,
    paddingTop: 16,
    paddingBottom: 16,
    // paddingLeft: 16,
    // paddingRight: 16,
  },
  btnSec1: {
    width: "100%",
    justifyContent: "space-around",
    flexDirection: "row",
    marginBottom: 24,
    // paddingHorizontal: 18.33
  },
  emptyCont: {
    width: "100%",
    height: "100%",
    alignItems: "center",
    // justifyContent: "center",
    paddingTop: (20 * height) / 100,
  },
  datCat: {
    fontSize: 12,
  },
  item: {
    width: "100%",
    padding: 16,
    backgroundColor: colors.white,
    flexDirection: "row",
    borderTopWidth: 1,
    borderTopColor: "#F0EFEF",
  },
  transactionAmount: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    // fontWeight: "bold",
  },
  transactionDate: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  amt: {
    fontSize: 12,
    // color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
});
