import React, { useState, useCallback, useEffect } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  FlatList,
  ActivityIndicator,
  Image,
  TextInput,
  RefreshControl,
  Modal,
} from "react-native";
import { fonts } from "../config/Fonts";
import Div from "../components/Div";
import AuthenticationHedear from "../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "../components/P";
import MicroBtn from "../components/MicroBtn";
import { colors } from "../config/colors";
import ListItem from "../components/ListItem";
import {
  DeleteBene,
  GetBeneByID,
  GetBeneficiaries,
} from "../RequestHandlers/User";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../components/ActivityIndicator";
import Swipeable from "react-native-gesture-handler/Swipeable";
import { countries } from "../components/counties";
import { TestBanks } from "../components/TestBanks";
import { useToast } from "../context/ToastContext";
import { withApiErrorToast } from "../Utils/withApiErrorToast";
import BottomSheetComponent from "../components/BottomSheetComponent";
import BottomSheet from "../components/BottomSheet";
import NoteComponent2 from "../components/NoteComponent2";
import H4 from "../components/H4";
import Button from "../components/Button";
import Link from "../components/Link";

const { width, height } = Dimensions.get("window");

// Group beneficiaries alphabetically by first letter of name
const groupByAlphabet = (items) => {
  return items.reduce((acc, item) => {
    const firstLetter = item.name.charAt(0).toUpperCase();
    acc[firstLetter] = acc[firstLetter] || [];
    acc[firstLetter].push(item);
    return acc;
  }, {});
};

export default function Beneficiaries({ navigation, route }) {
  const [acctiveTab, setActiveTab] = useState(0);
  const tabs = [
    { name: "SFx wallet", prop: "sfx-money-app" },
    { name: "Local bank", prop: "bank-account" },
    { name: "Mobile money", prop: "mobile-money" },
  ];
  const { handleToast } = useToast();
  const [isNewNoti, setIsNewNoti] = useState(false);
  const [loader, setLoader] = useState(false);
  const [beneficiaries, setBeneficiaries] = useState([]);

  // Get the active tab from navigation params or default to "sfx-money-app"
  const initialActiveTab = route?.params?.actT || "sfx-money-app";
  const [activeProp, setActiveProp] = useState(initialActiveTab);

  // Handle route parameter changes (when navigating from different screens)
  useEffect(() => {
    const newActiveTab = route?.params?.actT;
    if (newActiveTab && newActiveTab !== activeProp) {
      setActiveProp(newActiveTab);
    }
  }, [route?.params?.actT]);

  // Sync acctiveTab index with activeProp
  useEffect(() => {
    const tabIndex = tabs.findIndex((tab) => tab.prop === activeProp);
    if (tabIndex !== -1) {
      setActiveTab(tabIndex);
    }
  }, [activeProp]);
  const [loading1, setLoading1] = useState(false);
  const [page, setPage] = useState(1); // Current page number
  const [limit] = useState(200); // Items per page (constant)
  const [hasMoreData, setHasMoreData] = useState(true);
  const [totalItem, setTotalItem] = useState(0);
  const [currentItemCount, setCurrentItemCount] = useState(0); // Track current loaded items
  const [actionLoad, setActionLoad] = useState(false);
  const [bImg, setBImg] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredBeneficiaries, setFilteredBeneficiaries] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [expandedGroups, setExpandedGroups] = useState<{
    [key: string]: boolean;
  }>({});
  const [showBottomSheet, setShowBottomSheet] = useState(false);
  const [selectedBeneficiary, setSelectedBeneficiary] = useState(null);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  const getBeneficiaries = async (loadMore = false) => {
    if (loading1) return; // Prevent multiple requests if already loading
    setLoading1(true);

    try {
      const currentPage = loadMore ? page + 1 : 1;
      const response = await withApiErrorToast(
        GetBeneficiaries(currentPage, limit, activeProp),
        handleToast
      );

      if (response.items) {
        setBeneficiaries((prevBeneficiaries) =>
          loadMore ? [...prevBeneficiaries, ...response.items] : response.items
        );
        setFilteredBeneficiaries((prevBeneficiaries) =>
          loadMore ? [...prevBeneficiaries, ...response.items] : response.items
        );

        setTotalItem(response.meta.totalItems);
        setPage(currentPage);

        // Update current item count
        const newItemCount = loadMore
          ? currentItemCount + response.items.length
          : response.items.length;
        setCurrentItemCount(newItemCount);

        // Check if there is more data to fetch
        setHasMoreData(newItemCount < response.meta.totalItems);

        setLoader(false);
      }
    } catch (error) {
      console.error("Error fetching beneficiaries:", error);
    } finally {
      setLoading1(false);
    }
  };

  const getBeneByID = async (id) => {
    setLoader(true);
    try {
      const bene = await withApiErrorToast(GetBeneByID(id), handleToast);
      if (bene.account) {
        if (bene.type === "sfx-money-app") {
          // Check if sfxBeneficiaryUser exists and has the verified property
          if (
            bene.sfxBeneficiaryUser &&
            bene.sfxBeneficiaryUser.verified === "true"
          ) {
            navigation.navigate("AmountScreen", {
              username: bene.account,
              note: "",
              details: bene.sfxBeneficiaryUser,
            });
            setTimeout(() => {
              setLoader(false);
            }, 1000);
          } else {
            handleToast("You can't send money to an unverified user", "error");
            setLoader(false);
          }
        } else if (bene.type === "mobile-money") {
          navigation.navigate("AmountScreen1", {
            country: bene?.country,
            channelID: bene?.yellowCardChannelId,
            currencyCode: getCurrencyCode(bene?.country),
            symbol: getSymbol(bene?.country),
            networkID: bene?.yellowCardNetworkId,
            provider: bene?.providerName,
            phone: bene?.account?.trim(),
            accName: bene?.name,
            note: "Other",
            aplhaCode2: bene?.country,
          });
          setLoader(false);
        } else if (bene.type === "bank-account") {
          if (
            (bene.country === "NG" && !bene.yellowCardChannelId) ||
            bene.yellowCardChannelId.includes("https")
          ) {
            const provider = TestBanks.BankList[0].banks.find(
              (it) => it.name === bene.providerName
            );
            const bImg = provider ? provider.image : null;
            setBImg(bImg);
            navigation.navigate("LinkSendAmount", {
              data: {
                bankName: bene?.providerName,
                accountName: bene?.name,
                accountNumber: bene?.account?.trim(),
                reason: "Other",
                bankImg: bImg,
              },
            });
          } else {
            navigation.navigate("BankAmountScreen1", {
              country: bene?.country,
              channelID: bene?.yellowCardChannelId,
              currencyCode: getCurrencyCode(bene?.country),
              symbol: getSymbol(bene?.country),
              networkID: bene?.yellowCardNetworkId,
              provider: bene?.providerName,
              accNum: bene?.account?.trim(),
              accName: bene?.name,
              note: "Other",
              aplhaCode2: bene?.country,
              isManualInput: bene?.isManualNetworkInput || false,
            });
          }
          setLoader(false);
        } else {
          handleToast("Unknown beneficiary type", "error");
          setLoader(false);
        }
      } else {
        handleToast("Beneficiary information not found", "error");
        setLoader(false);
      }
    } catch (error) {
      console.error("Error fetching beneficiary:", error);
      handleToast("Error loading beneficiary details", "error");
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };
  const getCurrencyCode = (yc: string): string => {
    if (yc === "TR") {
      return "TRY";
    }
    const country = countries.find((item) => item.YellowCardCode === yc);
    return country ? country.currencyCode : "Country not found";
  };

  const getSymbol = (currencyCode: string): string => {
    if (currencyCode === "TR") {
      return "₺";
    }
    const curSymbol = countries.find(
      (item) => item.YellowCardCode === currencyCode
    );
    return curSymbol ? curSymbol.symbol : "Symbol not found";
  };

  // Search filter function
  const filterBeneficiaries = useCallback(
    (query: string) => {
      if (!query.trim()) {
        setFilteredBeneficiaries(beneficiaries);
        return;
      }

      const filtered = beneficiaries.filter(
        (item: any) =>
          item.name.toLowerCase().includes(query.toLowerCase()) ||
          item.account.toLowerCase().includes(query.toLowerCase()) ||
          item.providerName.toLowerCase().includes(query.toLowerCase())
      );
      setFilteredBeneficiaries(filtered);
    },
    [beneficiaries]
  );

  // Update filtered list when search query changes
  useEffect(() => {
    filterBeneficiaries(searchQuery);
  }, [searchQuery, filterBeneficiaries]);

  // Function to toggle group expansion
  const toggleGroup = (letter: string) => {
    setExpandedGroups((prev) => ({
      ...prev,
      [letter]: prev[letter] === undefined ? false : !prev[letter], // Handle undefined properly
    }));
  };

  // Function to handle opening bottom sheet for beneficiary actions
  const handleBeneficiaryMenu = (beneficiary: any) => {
    setSelectedBeneficiary(beneficiary);
    setShowBottomSheet(true);
  };

  // Function to handle showing delete confirmation modal
  const handleDeleteFromBottomSheet = () => {
    setShowDeleteConfirmation(true);
  };

  // Function to handle confirming delete action
  const handleConfirmDelete = () => {
    if (selectedBeneficiary) {
      deleteBene(selectedBeneficiary.id);
      setShowDeleteConfirmation(false);
      setShowBottomSheet(false);
      setSelectedBeneficiary(null);
    }
  };

  // Function to handle canceling delete action
  const handleCancelDelete = () => {
    setShowDeleteConfirmation(false);
    setShowBottomSheet(false);
  };

  // Create grouped and flattened data structure for FlatList
  const groupedData = React.useMemo(() => {
    const dataToGroup =
      searchQuery.length > 0 ? filteredBeneficiaries : beneficiaries;

    // First sort all beneficiaries alphabetically
    const sortedData = [...dataToGroup].sort((a, b) =>
      a.name.localeCompare(b.name)
    );

    // Group by first letter
    const grouped = groupByAlphabet(sortedData);

    // Get sorted letters
    const sortedLetters = Object.keys(grouped).sort();

    // Create flattened array with headers and items
    const result = [];

    sortedLetters.forEach((letter) => {
      // Check if group is expanded (default to true if not set)
      const isExpanded =
        expandedGroups[letter] === undefined ? true : expandedGroups[letter];

      // Create a group object that contains header info and all its beneficiaries
      result.push({
        type: "group",
        letter: letter,
        count: grouped[letter].length,
        isExpanded: isExpanded,
        beneficiaries: grouped[letter], // Include all beneficiaries in this group
        id: `group-${letter}`,
      });
    });

    return result;
  }, [beneficiaries, filteredBeneficiaries, searchQuery, expandedGroups]);

  useFocusEffect(
    useCallback(() => {
      setLoader(true);
      setBeneficiaries([]);
      setFilteredBeneficiaries([]);
      setPage(1); // Reset to first page
      setCurrentItemCount(0); // Reset item count
      setHasMoreData(true); // Reset pagination state
      getBeneficiaries();
    }, [activeProp])
  );

  const fetchMoreTransactions = () => {
    if (!loading1 && hasMoreData) {
      getBeneficiaries(true); // Load more data with next page
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    setBeneficiaries([]);
    setFilteredBeneficiaries([]);
    setPage(1);
    setCurrentItemCount(0);
    setHasMoreData(true);
    await getBeneficiaries();
    setRefreshing(false);
  }, [activeProp]);

  const deleteBene = async (id: string) => {
    setActionLoad(true);
    try {
      const res = await DeleteBene(id);
      if (res.status === true) {
        setActionLoad(false);
        getBeneficiaries();
      } else {
        setActionLoad(false);
      }
    } catch (error) {
      console.error("Error deleting beneficiary:", error);
      setActionLoad(false);
    }
  };

  const renderItem = React.useCallback(
    ({ item }) => {
      if (item.type === "group") {
        return (
          <View
            style={{
              backgroundColor: colors.white,
              borderRadius: 12,
              overflow: "hidden",
              marginTop: 12,
            }}
          >
            {/* Group Header */}
            <TouchableOpacity
              style={[
                styles.groupHeader,
                { paddingVertical: item.isExpanded ? 12 : 16 },
              ]}
              onPress={() => toggleGroup(item.letter)}
            >
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                  width: "100%",
                }}
              >
                <P style={styles.groupHeaderText}>{item.letter}</P>
                <SvgXml
                  xml={svg.arrowDown}
                  width={18}
                  height={18}
                  style={{
                    transform: [
                      { rotate: item.isExpanded ? "180deg" : "0deg" },
                    ],
                  }}
                />
              </View>
            </TouchableOpacity>

            {/* Group Items - Only render if expanded */}
            {item.isExpanded &&
              item.beneficiaries.map((beneficiary, index) => (
                <View
                  key={`beneficiary-${beneficiary.id}-${index}`}
                  style={{
                    width: "100%",
                    borderTopWidth: 1,
                    backgroundColor: colors.white,
                    borderTopColor: colors.newStrokeColor,
                  }}
                >
                  <TouchableOpacity
                    onPress={() => {
                      getBeneByID(beneficiary?.id);
                    }}
                  >
                    <View style={styles.item}>
                      {(() => {
                        const provider = TestBanks.BankList[0].banks.find(
                          (it) => it.name === beneficiary.providerName
                        );
                        const bImg = provider ? provider.image : null;

                        return bImg === null ? (
                          <SvgXml
                            xml={
                              acctiveTab === 0
                                ? svg.smalllogo
                                : acctiveTab === 1
                                ? svg.bankGreen
                                : svg.mobile
                            }
                          />
                        ) : (
                          <Image
                            source={{ uri: bImg }}
                            width={30}
                            height={30}
                          />
                        );
                      })()}

                      <View style={{ marginLeft: 12 }}>
                        <P style={styles.transactionAmount} numberOfLines={1}>
                          {beneficiary.name}
                        </P>
                        <P style={styles.transactionDate} numberOfLines={1}>
                          {beneficiary.account} |{" "}
                          {beneficiary?.providerName === "sfx-money-app"
                            ? "SFx money app"
                            : beneficiary?.providerName}
                        </P>
                      </View>
                      <TouchableOpacity
                        style={{ position: "absolute", right: 16 }}
                        onPress={() => handleBeneficiaryMenu(beneficiary)}
                      >
                        <SvgXml xml={svg.dotMenu} />
                      </TouchableOpacity>
                    </View>
                  </TouchableOpacity>
                </View>
              ))}
          </View>
        );
      }

      return null;
    },
    [actionLoad, acctiveTab, toggleGroup, handleBeneficiaryMenu]
  );

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Beneficiary"
          navigation={navigation}
          search={true}
          navStyle={{ justifyContent: "space-between", height: "auto" }}
          contStyle={{ height: "auto" }}
          searchValue={searchQuery}
          onSearchChange={setSearchQuery}
          onClearPress={() => {
            setSearchQuery("");
          }}
        />
        <View style={styles.tabCont}>
          <ScrollView horizontal>
            {tabs.map((item, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  setActiveTab(index);
                  setActiveProp(item?.prop);
                }}
              >
                <View
                  style={{
                    backgroundColor:
                      index === acctiveTab ? colors.primary : "transparent",
                    paddingVertical: 7.5,
                    paddingHorizontal: 12,
                    borderRadius: 100,
                  }}
                >
                  <P
                    style={{
                      fontSize: 12,
                      color:
                        index === acctiveTab ? colors.white : colors.dark500,
                    }}
                  >
                    {item.name}
                  </P>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Search Input */}
        {/* {beneficiaries.length > 0 && (
         
        )} */}

        <View style={styles.contentBody}>
          {beneficiaries.length === 0 ? (
            <View style={styles.emptyCont}>
              <SvgXml xml={svg.userGroup} />
              <P style={styles.noHistoryText}>No Beneficiary</P>
              <P style={styles.noTransactionText}>
                You have no beneficiary yet
              </P>
            </View>
          ) : (
            <View style={{ width: "90%", alignSelf: "center" }}>
              {searchQuery.length > 0 && filteredBeneficiaries.length === 0 ? (
                <View style={styles.emptyCont}>
                  <SvgXml xml={svg.search} />
                  <P style={styles.noHistoryText}>No Results Found</P>
                  <P style={styles.noTransactionText}>
                    No beneficiaries match your search
                  </P>
                </View>
              ) : (
                <FlatList
                  data={groupedData}
                  renderItem={renderItem}
                  keyExtractor={(item) => item.id}
                  showsVerticalScrollIndicator={false}
                  onEndReached={
                    searchQuery.length === 0 ? fetchMoreTransactions : undefined
                  }
                  onEndReachedThreshold={0.3}
                  contentContainerStyle={{ paddingBottom: 300 }}
                  refreshControl={
                    <RefreshControl
                      refreshing={refreshing}
                      onRefresh={onRefresh}
                      colors={[colors.primary]}
                      tintColor={colors.primary}
                    />
                  }
                  ListFooterComponent={
                    loading1 && hasMoreData && searchQuery.length === 0 ? (
                      <ActivityIndicator
                        color={colors.primary}
                        style={{ marginTop: 16 }}
                      />
                    ) : null
                  }
                />
              )}
            </View>
          )}
        </View>
      </Div>
      {loader && <Loader />}

      {/* Bottom Sheet for Beneficiary Actions */}
      <BottomSheet
        isVisible={showBottomSheet}
        onClose={() => {
          setShowBottomSheet(false);
          setSelectedBeneficiary(null);
        }}
        backspaceText=""
        showBackArrow={false}
        modalContentStyle={{ height: "35%" }}
        components={
          <View style={{ paddingTop: 24 }}>
            <View>
              <NoteComponent2
                type="red"
                contStyle={{ backgroundColor: colors.redSubtle }}
                text={"Any change will not reflected on previous transaction"}
              />
            </View>
            {selectedBeneficiary && (
              <>
                <TouchableOpacity
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    paddingVertical: 16,
                    borderRadius: 12,
                    marginBottom: 16,
                    marginTop: 8,
                  }}
                  onPress={handleDeleteFromBottomSheet}
                  disabled={actionLoad}
                >
                  <SvgXml xml={svg.trash} style={{ marginRight: 12 }} />
                  <P
                    style={{
                      fontSize: 14,
                      fontFamily: fonts.poppinsMedium,
                      color: colors.black,
                      flex: 1,
                    }}
                  >
                    Delete Beneficiary
                  </P>
                  {actionLoad ? (
                    <ActivityIndicator size="small" color={colors.red} />
                  ) : (
                    <SvgXml xml={svg.arrowBlack} />
                  )}
                </TouchableOpacity>
              </>
            )}
          </View>
        }
      />

      {/* Full Screen Delete Confirmation Modal */}
      <Modal
        visible={showDeleteConfirmation}
        animationType="slide"
        transparent={false}
        statusBarTranslucent={true}
      >
        <View style={styles.confirmationModalContainer}>
          <View style={styles.confirmationContent}>
            <View style={styles.confirmationHeader}>
              <View
                style={{
                  width: "90%",
                  paddingVertical: 24,
                  flexDirection: "row",
                  justifyContent: "flex-end",
                  alignSelf: "center",
                }}
              >
                <TouchableOpacity
                  style={{
                    paddingHorizontal: 12,
                    paddingVertical: 7,
                    borderRadius: 99,
                    borderWidth: 1,
                    borderColor: colors.stroke,
                  }}
                  onPress={() => {
                    handleCancelDelete();
                  }}
                >
                  <P style={{ fontSize: 12 }}>Close</P>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.confirmationBody}>
              <View style={styles.warningIconContainer}>
                <SvgXml xml={svg.LargeTrash} />
              </View>

              <H4 style={styles.confirmationTitle}>Delete Beneficiary</H4>

              <P style={styles.confirmationMessage}>
                You're about to delete this beneficiary from your list. You can
                always add them back later if needed.
              </P>
            </View>

            <Button
              loading={actionLoad}
              onPress={handleConfirmDelete}
              btnText="Delete beneficiary"
              style={{ width: "80%", alignSelf: "center", marginTop: 16 }}
            />
            <Link
              style={{ textAlign: "center", marginTop: 20 }}
              onPress={handleCancelDelete}
            >
              Cancel
            </Link>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    backgroundColor: colors.secBackground,
  },
  emptyCont: {
    width: "100%",
    height: "80%",
    alignItems: "center",
    justifyContent: "center",
  },
  noHistoryText: {
    fontFamily: fonts.poppinsMedium,
    lineHeight: 21,
    marginTop: 16,
  },
  noTransactionText: {
    fontSize: 13,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray2,
  },
  item: {
    width: "100%",
    padding: 16,
    backgroundColor: colors.white,
    alignItems: "center",
    // position: "absolute",
    borderRadius: 12,
    flexDirection: "row",
  },
  transactionAmount: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
  },
  transactionDate: {
    fontSize: 12,
    color: colors.dark500,
    fontFamily: fonts.poppinsRegular,
  },
  tabCont: {
    width: "100%",
    padding: 24,
    paddingTop: 12,
    paddingBottom: 12,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    flexDirection: "row",
  },
  edit: {
    width: "25%",
    height: "100%",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(194, 255, 223, 1)",
    alignSelf: "center",
    zIndex: 10,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    fontSize: 12,
    // position: "absolute",
    left: 0,
  },
  delete: {
    width: "25%",
    height: "100%",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(255, 194, 195, 1)",
    alignSelf: "center",
    zIndex: 10,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    // position: "absolute",
    left: 0,
  },
  searchContainer: {
    width: "100%",
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: colors.secBackground,
  },
  searchInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: colors.stroke,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    color: colors.black,
  },
  clearButton: {
    padding: 4,
    marginLeft: 8,
  },
  groupHeader: {
    backgroundColor: colors.white,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  groupHeaderText: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
  },
  groupHeaderCount: {
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
  },
  // Confirmation Modal Styles
  confirmationModalContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  confirmationContent: {
    flex: 1,
    paddingHorizontal: 24,
  },
  confirmationHeader: {
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "center",
    paddingTop: 60,
    paddingBottom: 20,
  },
  cancelButton: {
    padding: 8,
  },
  confirmationBody: {
    alignItems: "center",
    paddingHorizontal: 20,
    marginTop: 30,
  },
  warningIconContainer: {
    marginBottom: 16,
  },
  confirmationTitle: {
    fontFamily: fonts.poppinsSemibold,
    color: colors.black,
    textAlign: "center",
    marginBottom: 4,
  },
  confirmationMessage: {
    fontSize: 16,
    fontFamily: fonts.poppinsRegular,
    color: colors.dark500,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 32,
  },
  beneficiaryInfoContainer: {
    backgroundColor: colors.secBackground,
    padding: 16,
    borderRadius: 12,
    width: "100%",
    marginBottom: 32,
  },
  beneficiaryName: {
    fontSize: 16,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
    marginBottom: 4,
  },
  beneficiaryDetails: {
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
  },
  confirmationActions: {
    width: "100%",
    flexDirection: "row",
  },
  cancelActionButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.stroke,
    alignItems: "center",
    justifyContent: "center",
  },
  cancelActionText: {
    fontSize: 16,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
  },
  continueButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    backgroundColor: colors.red,
    alignItems: "center",
    justifyContent: "center",
  },
  continueButtonText: {
    fontSize: 16,
    fontFamily: fonts.poppinsMedium,
    color: colors.white,
  },
});
