import React, { useRef, useState, useEffect } from "react";
import { ScrollView, StyleSheet, View, Dimensions, Image } from "react-native";
import Div from "../components/Div";
import { colors } from "../config/colors";
import P from "../components/P";
import { fonts } from "../config/Fonts";
import Button from "../components/Button";
import Link from "../components/Link";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import Input from "../components/Input";
import BottomComponent from "../components/BottomComponent";

const screenHeight = Dimensions.get("window").height;
const { width } = Dimensions.get("window");
export default function ResetSuccessScreen2({ navigation }) {
  return (
    <View style={styles.body}>
      <Div style={{ height: screenHeight }}>
        <ScrollView style={styles.container}>
          <View
            style={{
              width: "100%",
              marginTop: 60,
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Image
              source={require("../assets/celeb.png")}
              style={{ marginTop: 120, width: 80, height: 80 }}
            />
            <P style={styles.text1}>PIN reset</P>
            <P style={styles.text2}>
              Your account PIN has been {"\n"}reset successfully
            </P>
          </View>
          <View style={styles.components}>
            <Button
              btnText="Login"
              onPress={() => navigation.navigate("ExistingLoginScreen")}
            />
          </View>
        </ScrollView>
        <BottomComponent navigation={navigation} />
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
    height: screenHeight,
    width,
  },

  text1: {
    fontSize: 20,
    fontFamily: fonts.poppinsBold,
    marginTop: 20,
    lineHeight: 30,
  },
  text2: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 22.4,
    color: "#A5A1A1",
    fontFamily: fonts.poppinsRegular,
  },
  components: {
    width: "80%",
    marginTop: 48,
    alignSelf: "center",
    // backgroundColor:"red"
  },
});
