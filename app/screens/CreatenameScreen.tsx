import React, { useRef, useState, useEffect } from "react";
import { StyleSheet, View, Keyboard } from "react-native";
import Div from "../components/Div";
import { colors } from "../config/colors";
import P from "../components/P";
import { fonts } from "../config/Fonts";
import Button from "../components/Button";
import Link from "../components/Link";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import Input from "../components/Input";
import BottomComponent from "../components/BottomComponent";
import SignupHeader from "../components/SignupHeader";
import { CheckUsername } from "../RequestHandlers/Authentication";
import { Formik } from "formik";
import * as yup from "yup";
import { useToast } from "../context/ToastContext";
import AuthHeader from "../components/AuthHeader";
import H4 from "../components/H4";

export default function CreatenameScreen({ navigation, route }) {
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();
  const { values1, values2, countryCode } = route?.params;
  const usernameScheme = yup.object().shape({
    username: yup.string().required("Username is required"),
  });

  return (
    <View style={styles.container}>
      <Formik
        initialValues={{
          username: "",
        }}
        validationSchema={usernameScheme}
        onSubmit={async (values, actions) => {
          Keyboard.dismiss();
          setLoading(true);
          const trimmedUsername = values.username.trim();
          if (/\s/.test(trimmedUsername)) {
            actions.setFieldError("username", "Username cannot contain spaces");
            setLoading(false);
            return;
          }
          // Proceed with API call if no spaces
          try {
            const checkUserName = await CheckUsername({
              username: trimmedUsername,
            });
            if (checkUserName.status === true) {
              navigation.navigate("TransactionPinScreen", {
                values1: values1,
                values2: values2,
                countryCode: countryCode,
                username: values,
              });
            } else {
              handleToast(checkUserName.message, "error");
            }
          } catch (error) {
            handleToast("Network error", "error");
          } finally {
            setLoading(false);
          }
        }}
      >
        {(formikProps) => (
          <Div>
            <AuthHeader style={{ width: "95%" }} navigation={navigation} />
            <View
              style={{
                width: "90%",
                justifyContent: "center",
                alignSelf: "center",
                marginTop: 8,
                alignItems: "center"
              }}
            >
              <H4 style={styles.text1}>Create your username</H4>
              {/* @ts-ignore */}
              <P style={[styles.text2]}>
                Provide a username that you like to{'\n'}receive money from friends
                and family
              </P>
            </View>
            <View style={styles.components}>
              <Input
                placeholder="John111"
                label="Username"
                inputStyle={{ width: "85%" }}
                onChangeText={formikProps.handleChange("username")}
                value={formikProps.values.username}
                onBlur={formikProps.handleBlur("username")}
                autoCapitalize="none"
                maxLenght={20}
                error={
                  formikProps.errors.username && formikProps.touched.username
                }
              />
              {/* {formikProps.errors.username && formikProps.touched.username && (
                <P style={styles.errorText}>{formikProps.errors.username}</P>
              )} */}
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                }}
              >
                {formikProps.errors.username && formikProps.touched.username ? (
                  <P style={styles.errorText}>{formikProps.errors.username}</P>
                ) : (
                  <P>{""}</P>
                )}
                <P
                  style={{
                    // position: "absolute",
                    bottom: 0,
                    right: 0,
                    fontSize: 12,
                    fontFamily: fonts.poppinsRegular,
                    marginTop: 4,
                  }}
                >
                  Max characters 20
                </P>
              </View>
              <View style={{ marginTop: 32 }}>
                <Button
                  btnText="Next"
                  onPress={formikProps.handleSubmit}
                  loading={loading}
                />
              </View>

              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "center",
                  marginTop: 32,
                }}
              >
                <P
                  style={{
                    fontSize: 12,
                    lineHeight: 22.4,
                    color: "#A5A1A1",
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  Do you have an account?{" "}
                </P>
                <Link
                  style={{
                    fontSize: 12,
                    lineHeight: 21,
                    textDecorationLine: "underline",
                  }}
                  onPress={() => {
                    navigation.navigate("NewLoginScreen");
                  }}
                >
                  Login
                </Link>
              </View>
            </View>

            <BottomComponent navigation={navigation} />
          </Div>
        )}
      </Formik>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  text1: {
    fontSize: 20,
    fontFamily: fonts.poppinsBold,
    lineHeight: 30,
  },
  text2: {
    fontSize: 14,
    lineHeight: 22.4,
    fontFamily: fonts.poppinsRegular,
    textAlign: "center"
  },
  components: {
    width: "90%",
    marginTop: 24,
    alignSelf: "center",
    // backgroundColor:"red"
  },
  bottomCont: {
    position: "absolute",
    bottom: 32,
    width: "100%",
    flexDirection: "row",
    // backgroundColor:"red"
  },
  bottomIcons: {
    width: 104,
    height: 22,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  bottomIconsText: {
    fontSize: 12,
    lineHeight: 22,
    color: "#A5A1A1",
    marginLeft: 4,
    textDecorationLine: "underline",
    textDecorationColor: "#A5A1A1",
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
});
