import React, { useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import Content from "../../components/Content";
import Content2 from "../../components/Content2";
import Input from "../../components/Input";
import { Switch } from "react-native-gesture-handler";
import CustomSwitch from "../../components/CustomSwitch";
import BottomSheet from "../../components/BottomSheet";
import CountrySelect from "../../components/CountrySelect";

const { width, height } = Dimensions.get("window");

export default function ElecricityIndex({ navigation }) {
  const [isEnabled, setIsEnabled] = useState(false);
  const [bene, setBene] = useState(false);
  const [flag, setFlag] = useState(require("../../assets/turkey.png"));
  const [postPaid, setpostPaid] = useState(false);
  const [prePaid, setprePaid] = useState(false);
  const [showCountries, setShowCountries] = useState(false);

  const toggleSwitch = () => setIsEnabled((previousState) => !previousState);
  const checkBeneficiary = () => {
    setBene(true);
  };
  const chooseElectricType = (type:number)=>{
    if (type==1) {
      setpostPaid(false)
      setprePaid(true)
    }
    else{
      setpostPaid(true)
      setprePaid(false)
    }
  }

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Electricity" navigation={navigation} />
        <ScrollView
          contentContainerStyle={{ flexGrow: 1, paddingBottom: "35%" }}>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <P style={{ width: "100%", fontSize: 12, marginBottom: 6 }}>
                Service provider
              </P>
              <View style={styles.topContainer}>
                <Image
                  source={require("../../assets/gtb.png")}
                  style={{ marginRight: 8, width: 24, height: 24}}
                />
                <P style={{ fontFamily: fonts.poppinsSemibold }}>Kib-tek</P>
              </View>
              <View style={{ width: "100%", marginBottom: 16 }}>
                <P style={{ fontSize: 12, marginBottom: 6 }}>Electric type</P>
                <View
                  style={{
                    width: "100%",
                    height: 44,
                    flexDirection: "row",
                    justifyContent: "space-between",
                  }}>
                  <TouchableOpacity
                  onPress={()=>chooseElectricType(0)}
                    style={[
                      styles.electricityBtn,
                      {
                        borderColor: postPaid ? "#8C52FF" : "#E6E5E5",
                      },
                    ]}>
                    <P style={{ fontSize: 12 }}>Post-paid</P>
                    {postPaid ? (
                      <SvgXml
                        xml={svg.purple_check}
                        style={{ position: "absolute", right: "5%" }}
                      />
                    ) : (
                      <></>
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                   onPress={()=>chooseElectricType(1)}
                    style={[
                      styles.electricityBtn,
                      {
                        borderColor: prePaid ? "#8C52FF" : "#E6E5E5",
                      },
                    ]}>
                    <P style={{ fontSize: 12 }}>Pre-paid</P>
                    {prePaid ? (
                      <SvgXml
                        xml={svg.purple_check}
                        style={{ position: "absolute", right: "5%" }}
                      />
                    ) : (
                      <></>
                    )}
                  </TouchableOpacity>
                  
                </View>
              </View>
              <Input
                label="Electric number"
                placeholder="0555750000"
                inputStyle={{ width: "85%" }}
                contStyle={{ marginBottom: 16 }}
                onBlur={() => checkBeneficiary()}
              />

              <Input
                label="Address"
                placeholder="4 Faydali apartment, Salamis Yolu....."
                inputStyle={{ width: "85%" }}
                contStyle={{ marginBottom: 16 }}
                onBlur={() => checkBeneficiary()}
              />

              {bene && (
                <View style={styles.benefeciary}>
                  <P>John Doe</P>
                  <SvgXml xml={svg.green_check} />
                </View>
              )}
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  width: "100%",
                  marginBottom: 16,
                }}>
                <CustomSwitch />
                <P
                  style={{
                    color: "#A5A1A1",
                    marginLeft: 8,
                    fontFamily: fonts.poppinsRegular,
                  }}>
                  Save Beneficiary
                </P>
              </View>
              <Input
                label="Note (Optional)"
                placeholder="Enter note here..."
                inputStyle={{ width: "85%" }}
                contStyle={{ marginBottom: 16 }}
              />
            </View>
            <View style={{ width: "80%", marginTop: 32 }}>
              <Button
                btnText="Next"
                onPress={() => navigation.navigate("ElectricityAmountScreen")}
              />
            </View>

            <View style={styles.detailWrap2}>
              <View style={styles.deatilsHead}>
                <P style={{ color: "#A5A1A1" }}>Beneficiaries</P>
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                  }}>
                  <P
                    style={{
                      color: "#8B52FF",
                      textDecorationLine: "underline",
                      alignItems: "center",
                    }}>
                    See all
                  </P>
                </View>
              </View>
              <SvgXml
                xml={svg.walletFace}
                style={{ marginTop: 52, marginBottom: 16 }}
              />
              <P style={{ fontFamily: fonts.poppinsMedium, marginBottom: 4 }}>
                No beneficiary!
              </P>
              <P style={{ color: "#A5A1A1", fontFamily: fonts.poppinsRegular }}>
                You have no benefeciary yet
              </P>
            </View>
          </View>
        </ScrollView>
        <BottomSheet
          isVisible={showCountries}
          showBackArrow={false}
          backspaceText="Select country"
          onClose={() => setShowCountries(false)}
          modalContentStyle={{ height: "65%" }}
          extraModalStyle={{ height: "63%" }}
          components={
            <CountrySelect
              // onActiveCountryChange={handleActiveCountry}
              // onActiveFlag={handleActiveFlag}
              onPress={() => {
                setShowCountries(false);
              }}
            />
          }
        />
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    // justifyContent:"center",
    alignItems: "center",
  },
  benefeciary: {
    paddingTop: 4,
    paddingRight: 24,
    paddingBottom: 4,
    paddingLeft: 24,
    backgroundColor: "#F7F4FF",
    borderRadius: 8,
    flexDirection: "row",
    width: "100%",
    height: 44,
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  deatilsHead: {
    width: "100%",
    height: 42,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: "5%",
  },
  detailWrap: {
    padding: 24,
    width: "90%",
    alignSelf: "center",
    // height:200,
    backgroundColor: "white",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  detailWrap2: {
    // padding: 24,
    width: "90%",
    alignSelf: "center",
    height: 246,
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent: "center",
    alignItems: "center",
    marginTop: 60,
  },

  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  topContainer: {
    paddingTop: 10,
    paddingRight: 14,
    paddingBottom: 10,
    paddingLeft: 14,
    backgroundColor: "#F7F4FF",
    width: "100%",
    height: 44,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  electricityBtn: {
    width: "47%",
    height: "100%",
    borderRadius: 8,
    borderColor: "#E6E5E5",
    borderWidth: 1,
    justifyContent: "center",
    paddingLeft: 10,
  },
});
