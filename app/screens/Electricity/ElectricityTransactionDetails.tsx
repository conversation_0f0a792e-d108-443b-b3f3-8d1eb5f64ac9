import React, { useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import SendMoneyStatus from "../../components/SeendMoneyStatus";
import CustomSwitch from "../../components/CustomSwitch";
import Content2 from "../../components/Content2";

const baseHeight = 802
const { width, height } = Dimensions.get("window");
export default function ElectricityTransactionDetails({ navigation }) {
  const [showSendStatus, setShowSendStatus] = useState(false);
  const [selectedAcc, setSelectedAcc] = useState(null);
  const accounts = [
    {
      id: 1,
      balance: 3500,
      currency: "USDT",
      exchangeRate: "1 USDT ~ 1 USD",
      type: "Tether",
    },
    {
      id: 2,
      balance: 0,
      currency: "USDT",
      exchangeRate: "1 USD ~ 1 USD",
      type: "USDC",
    },
  ];
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Transaction details"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <DetailCard
                headText={"Money you’re sending"}
                amount={
                  <>
                    <P style={{ fontSize: 32, lineHeight: 48, marginRight: 2 }}>
                      $1000
                    </P>
                    <P style={{ marginTop: 5 }}>USD</P>
                  </>
                }
                convertedAmount={
                  <>
                    <P style={{ fontSize: 16, lineHeight: 24, marginRight: 2 }}>
                      ₺1,600,000
                    </P>
                    <P style={{ marginTop: 2, fontSize: 12, lineHeight: 18 }}>
                      TRY
                    </P>
                  </>
                }
                bottomComponent={
                  <View style={styles.desCont}>
                    <View style={styles.items}>
                      <P style={styles.holder}>Service provider</P>
                      <P style={styles.value}>Kib-tek</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Electric type</P>
                      <P style={styles.value}>Pre-paid</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Electric number</P>
                      <P style={styles.value}>**********</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Address</P>
                      {/* @ts-ignore */}
                      <P style={[styles.value, { textAlign: "right" }]}>
                        4 Faydali apartment,{"\n"} Salamis Yolu , Gazimagusa
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Note</P>
                      {/* @ts-ignore */}
                      <P style={[styles.value, { textAlign: "right" }]}>N/A</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Fee</P>
                      <P style={styles.value}>
                        0{" "}
                        <P
                          // @ts-ignore
                          style={[
                            styles.value,
                            { fontFamily: fonts.poppinsRegular },
                          ]}>
                          USD
                        </P>
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Exchange rate</P>
                      <P style={styles.value}>1 USD ~33 TRY</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Bill payment</P>
                      <P style={styles.value}>Electricity</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Earn SFx point</P>
                      <P style={styles.value}>$2</P>
                    </View>
                    <View style={styles.line}></View>

                    <P
                      style={{
                        color: "#A5A1A1",
                        fontSize: 12,
                        textAlign: "center",
                      }}>
                      Payment method
                    </P>
                    <View
                      style={{
                        width: "100%",
                        flexDirection: "row",
                        justifyContent: "space-between",
                        marginBottom:16
                      }}>
                      <P style={{ color: "#A5A1A1", fontSize: 12 }}>
                        SFx point
                      </P>
                      <View style={{ flexDirection: "row" }}>
                        <P style={{ marginRight: 8 }}>$50</P>
                        <CustomSwitch />
                      </View>
                    </View>
                    {accounts.map((item, index) => (
                      <Content2
                        svgg={index == 0 ? svg.tather : svg.usdCoin}
                        onclick={index === selectedAcc}
                        ClickedMe={() => {
                          if (selectedAcc === index) {
                            setSelectedAcc(null);
                          } else if (item.balance > 0) {
                            setSelectedAcc(index);
                          }
                        }}
                        header={
                          <>
                            <P
                              style={{
                                fontSize: 12,
                                lineHeight: 18,
                                fontFamily: fonts.poppinsMedium,
                              }}
                            >
                              {item.balance}
                            </P>{" "}
                            <P
                              style={{
                                fontSize: 12,
                                lineHeight: 18,
                                fontFamily: fonts.poppinsRegular,
                                color: colors.gray,
                              }}
                            >
                              {item.currency}
                            </P>
                          </>
                        }
                        body={item.exchangeRate}
                        containerStyle={{
                          justifyContent: "flex-start",
                          paddingLeft: 16,
                        }}
                        itemWrapper={{ marginLeft: 8 }}
                        headerStyle={{ marginBottom: 4 }}
                        textStyle={{
                          fontFamily: fonts.poppinsRegular,
                          fontSize: 12,
                        }}
                        rightComponent={
                          item.balance === 0 && (
                            <Button
                              btnText="Add money"
                              btnTextStyle={{ color: colors.primary }}
                              type="alt"
                              style={{ width: "80%", height: "50%" }}
                            />
                          )
                        }
                      />
                    ))}
                  </View>
                }
              />
              <View style={styles.buttonWrap}>
                <Button
                  btnText="Confirm"
                  onPress={() => {
                    navigation.navigate("BankTransactionPin");
                    // setShowSendStatus(true);
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    // height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
    marginBottom: 40 / baseHeight * height
  },
  line: {
    width: "100%",
    // height: 1,
    borderBottomWidth: 1,
    borderStyle: "dashed",
    borderColor: colors.stroke,
    marginTop: (3.5 * height) / 100,
    marginBottom: (3.5 * height) / 100,
  },
});
