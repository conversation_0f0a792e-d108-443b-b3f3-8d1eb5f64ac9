import React, { useRef, useState, useEffect, useContext } from "react";
import {
  Dimensions,
  Keyboard,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import Div from "../components/Div";
import { colors } from "../config/colors";
import P from "../components/P";
import { fonts } from "../config/Fonts";
import Button from "../components/Button";
import Link from "../components/Link";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import Input from "../components/Input";
import ResendOtp from "../components/ResendOtp";
import BottomComponent from "../components/BottomComponent";
import SignupHeader from "../components/SignupHeader";

import {
  VerifyOtp,
  SendOtp,
  NewUserVerifyOtp,
  CreateUser,
} from "../RequestHandlers/Authentication";
import { useToast } from "../context/ToastContext";
import AuthHeader from "../components/AuthHeader";
import H4 from "../components/H4";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "../RequestHandlers/CredentailsContext";
import { withApiErrorToast } from "../Utils/withApiErrorToast";
const screenHeight = Dimensions.get("window").height;

export default function VerifyEmailScreen2({ navigation, route }) {
  const { values1 } = route?.params;
  const { handleToast } = useToast();
  const ref_input1 = useRef();
  const ref_input2 = useRef();
  const ref_input3 = useRef();
  const ref_input4 = useRef();
  const ref_input5 = useRef();
  const refs = [ref_input1, ref_input2, ref_input3, ref_input4, ref_input5];
  const [fields, setFields] = useState(["", "", "", "", ""]);
  const [code, setCode] = useState("");
  const [loading, setLoading] = useState(false);
  const { setStoredCredentails } = useContext(CredentailsContext);
  const [validationError, setValidationError] = useState([
    false,
    false,
    false,
    false,
    false,
  ]);

  const focusNextField = (nextField: any) => {
    nextField.current.focus();
  };

  const handleKeyPress = (index: any, event: any) => {
    const { nativeEvent } = event;

    if (nativeEvent.key === "Backspace" || nativeEvent.key === "Delete") {
      if (fields[index] === "") {
        const prevIndex = index - 1;
        if (prevIndex >= 0) {
          setFields((prevFields) => {
            const updatedFields = [...prevFields];
            updatedFields[prevIndex] = "";
            return updatedFields;
          });
          focusNextField(refs[prevIndex]);
        }
      } else {
        setFields((prevFields) => {
          const updatedFields = [...prevFields];
          updatedFields[index] = "";
          return updatedFields;
        });
      }
    }
  };

  const handleChangeText = (index: any, text: any) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;

      if (text !== "") {
        const nextIndex = index + 1;
        if (nextIndex < updatedFields.length) {
          focusNextField(refs[nextIndex]);
        } else {
          Keyboard.dismiss();
        }
      }

      return updatedFields;
    });

    // Reset validation error when user types
    setValidationError((prevErrors) => {
      const updatedErrors = [...prevErrors];
      updatedErrors[index] = false;
      return updatedErrors;
    });
  };

  const handlePaste = (index: any, pastedText: string) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      const characters = pastedText.split("");

      for (let i = 0; i < characters.length; i++) {
        const fieldIndex = index + i;
        if (fieldIndex < updatedFields.length) {
          updatedFields[fieldIndex] = characters[i];
        }
      }

      return updatedFields;
    });
  };

  const validateFields = () => {
    const errors = fields.map((field) => field === "");
    setValidationError(errors);
    return errors.every((error) => !error);
  };

  const persistLogin = (credentail: any, message?: any, status?: any) => {
    AsyncStorage.setItem("cookies", JSON.stringify(credentail))
      .then(async () => {
        // @ts-ignore
        setStoredCredentails(credentail);
      })
      .catch((err) => {});
  };

  const createUser = async () => {
    const body: any = {
      email: values1.email.trim(),
      password: values1.password.trim(),
      confirmPassword: values1.confirmPassword.trim(),
      referralCode: values1.referralCode.trim(),
    };
    try {
      const createUser = await withApiErrorToast(CreateUser(body), handleToast);
      if (createUser.newAccount === true) {
        await AsyncStorage.setItem("newUser", "true");
      }
      if (createUser.token) {
        setTimeout(() => {
          persistLogin(createUser);
        }, 500);
      } else {
        handleToast(createUser.message, "error");
      }
    } catch (error) {
      setLoading(false);
      handleToast(error.message, "error");
    } finally {
      setLoading(false);
    }
  };
  const verifyOtp = async () => {
    if (!validateFields()) {
      return;
    }
    setLoading(true);
    try {
      const body = {
        otp: Number(code),
        email: values1.email,
        type: "verify",
      };
      const verifyOtp = await withApiErrorToast(
        NewUserVerifyOtp(body),
        handleToast
      );
      if (verifyOtp.status === true) {
        createUser();
      } else {
        setLoading(false);
        handleToast(verifyOtp.message, "error");
      }
    } catch (error) {
      setLoading(false);
      handleToast("Network error", "error");
    }finally{
      setLoading(false)
    }
  };

  useEffect(() => {
    if (fields.every((field) => field !== "")) {
      setCode(fields.join(""));
    }
  }, [fields]);

  const resendOtp = async () => {
    try {
      const body = {
        email: values1.email,
        type: "verify",
      };
      const resendOtp = await SendOtp(body);
      // if (resendOtp.status === true) {
      //   handleToast("Code sent succefully", "success");
      // } else {
      //   handleToast(resendOtp.message, "error");
      // }
    } catch (error) {
      handleToast("Network error", "error");
    }
  };

  return (
    <View style={styles.container}>
      <Div>
        <AuthHeader navigation={navigation} />
        <View
          style={{
            width: "85%",
            justifyContent: "center",
            alignSelf: "center",
            alignItems: "center",
          }}
        >
          <H4 style={styles.text1}>Verify email</H4>
          <P style={styles.text2}>
            Enter the code sent to{"\n"}
            {values1.email} via email
          </P>
        </View>
        <View style={styles.components}>
          <View style={styles.con}>
            {refs.map((ref, index) => (
              <View
                style={[
                  styles.pinInput,
                  validationError[index] && { borderColor: colors.red },
                ]}
                key={index}
              >
                <TextInput
                  style={styles.pinTextInput}
                  placeholderTextColor="#000"
                  keyboardType="numeric"
                  ref={ref}
                  key={index}
                  onChangeText={(text) => handleChangeText(index, text)}
                  onKeyPress={(event) => handleKeyPress(index, event)}
                  // @ts-ignore
                  onTextInput={(event) => {
                    const pastedText = event.nativeEvent.text;
                    handlePaste(index, pastedText);
                  }}
                  value={fields[index]}
                />
              </View>
            ))}
          </View>

          <ResendOtp setResend={resendOtp} />
          <Button btnText="Continue" loading={loading} onPress={verifyOtp} />

          <View
            style={{
              flexDirection: "row",
              justifyContent: "center",
              marginTop: 32,
            }}
          >
            <P
              style={{
                fontSize: 12,
                lineHeight: 22.4,
                color: "#A5A1A1",
                fontFamily: fonts.poppinsRegular,
              }}
            >
              Do you have an account?{" "}
            </P>
            <Link
              style={{
                fontSize: 12,
                lineHeight: 21,
                textDecorationLine: "underline",
              }}
              onPress={() => navigation.navigate("NewLoginScreen")}
            >
              Login
            </Link>
          </View>
        </View>

        <BottomComponent navigation={navigation} />
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    height: screenHeight,
  },

  text1: {
    fontSize: 20,
    fontFamily: fonts.poppinsBold,
    marginTop: 20,
    lineHeight: 30,
  },
  text2: {
    fontSize: 14,
    lineHeight: 22.4,
    fontFamily: fonts.poppinsRegular,
    textAlign: "center",
  },
  components: {
    width: "85%",
    marginTop: 24,
    alignSelf: "center",
    // backgroundColor:"red"
  },
  bottomCont: {
    position: "absolute",
    bottom: 32,
    width: "100%",
    flexDirection: "row",
    // backgroundColor:"red"
  },
  bottomIcons: {
    width: 104,
    height: 22,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  bottomIconsText: {
    fontSize: 12,
    lineHeight: 22,
    color: "#A5A1A1",
    marginLeft: 4,
    textDecorationLine: "underline",
    textDecorationColor: "#A5A1A1",
  },
  pinInput: {
    borderWidth: 1,
    borderColor: "#E6E5E5",
    borderRadius: 8,
    width: 56,
    height: 56,
    marginBottom: 18,
  },
  pinTextInput: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
    width: "100%",
    height: 56,
  },
  con: {
    flexDirection: "row",
    width: "100%",
    justifyContent: "center",
    gap: 8,
  },
});
