import React, { useEffect, useRef, useState, useContext } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  // Keyboard,
  Text,
  Platform,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import Button from "../../components/Button";
import { colors } from "../../config/colors";
import SendMoneyStatus from "../../components/SeendMoneyStatus";
import Keyboard from "../../components/Keyboard";
import { ValidatePin } from "../../RequestHandlers/User";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { GetUserDetails } from "../../RequestHandlers/User";
import * as LocalAuthentication from "expo-local-authentication";
import { FingerPrintStatus } from "../../context/FingerPrintContext";
import EnableBiomatricComponent from "../../components/EnableBiomatricComponent";
import { TransactionAuth } from "../../components/TransactionAuth";
import { MomoSendMoney } from "../../RequestHandlers/Wallet";
import { useToast } from "../../context/ToastContext";
import { encryptPIN } from "../../Utils/encrypt";
import * as SecureStore from "expo-secure-store";
import { useFocusEffect } from "@react-navigation/native";
import { useGlobalModal } from "../../context/GlobalModalContext";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";

const baseHeight = 802;
const { width, height } = Dimensions.get("window");

export default function MobileMoneyTransactionPin({ navigation, route }) {
  const { handleToast } = useToast();
  const [showSendStatus, setShowSendStatus] = useState(false);
  const [fields, setFields] = useState(["", "", "", ""]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [showDots, setShowDots] = useState([false, false, false, false]);
  const refs = [useRef(), useRef(), useRef(), useRef()];
  const [loading, setLoading] = useState(false);
  const {
    channelID,
    networkID,
    note,
    amount,
    accName,
    currencyCode,
    ngnRate,
    phone,
    country,
    amountCurrency,
  } = route.params || "";
  const [invalidFields, setInvalidFields] = useState([
    false,
    false,
    false,
    false,
  ]);
  const [isBioMatricSupported, setIsBioMatricSupported] = useState(true);
  const [showEnabler, setShowEnabler] = useState(false);
  const [id, setId] = useState("");
  const [currentFingerPrintStatus, setCurrentFingerPrintStatus] =
    useState(null);
  const [currentPrivateKey, setCurrentPrivateKey] = useState(null);
  const { showAddMoneyFailedModal } = useGlobalModal();
  const {
    storedFingerPrintStatus,
    setStoredFingerPrintStatus,
    storedPrivateKey,
  } = useContext(FingerPrintStatus);

  const handleKeyPress = (key) => {
    if (key === "←") {
      if (activeIndex > 0 || fields[activeIndex] !== "") {
        handleChangeText(activeIndex, "");
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[activeIndex] = false;
          return updatedDots;
        });
        if (activeIndex > 0) {
          setActiveIndex(activeIndex - 1);
        }
      }
    } else if (key === "Enter") {
      // Handle enter key press
    } else {
      handleChangeText(activeIndex, key);
      if (activeIndex < 3) {
        setActiveIndex(activeIndex + 1);
      }
    }
  };

  const handleChangeText = (index, text) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;

      if (text !== "") {
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[index] = false;
          return updatedDots;
        });

        setTimeout(() => {
          setShowDots((prevShowDots) => {
            const updatedDots = [...prevShowDots];
            updatedDots[index] = true;
            return updatedDots;
          });
        }, 500);

        if (index === refs.length - 1) {
          // Keyboard.dismiss();
        }
      }

      return updatedFields;
    });
    // Reset invalid fields if user starts typing
    setInvalidFields((prevInvalidFields) => {
      const updatedInvalidFields = [...prevInvalidFields];
      updatedInvalidFields[index] = false;
      return updatedInvalidFields;
    });
  };

  const momoSendMoney = async () => {
    setLoading(true);
    try {
      const body = {
        channelId: channelID,
        networkId: networkID,
        reason: note.toLowerCase(),
        accountName: accName?.trim(),
        account: phone.replace(/\s/g, ""),
        amount: Number(amount),
        currency: currencyCode,
        paymentGateway: "momo",
        country: country,
        amountCurrency: amountCurrency,
      };
      const sendMoney = await withApiErrorToast(
        MomoSendMoney(body),
        handleToast
      );
      if (sendMoney.payment) {
        setLoading(false);
        navigation.navigate("SendMoneyStatus", { response: sendMoney });
      } else {
        setLoading(false);
        showAddMoneyFailedModal(sendMoney.message);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const navigate = (activityId) => {
    navigation.navigate("VerifyActivityScreen", {
      activityId: activityId,
      activityType: "yellow-withdraw",
      ActivityFunction: momoSendMoney,
    });
  };

  const validatePin = async (pin) => {
    setLoading(true);
    if (
      !pin ||
      (typeof pin === "string" &&
        pin.length < 4 &&
        fields.some((field) => field === ""))
    ) {
      // Show red borders for empty fields
      setInvalidFields(fields.map((field) => field === ""));
      handleToast("Please fill in all PIN fields", "error");
      setLoading(false);
      return;
    }
    try {
      const response = await withApiErrorToast(
        ValidatePin({
          pin: pin,
          activityType: "yellow-withdraw",
        }),
        handleToast
      );
      if (response.error) {
        setInvalidFields([true, true, true, true]);
        handleToast(response.message || "Invalid PIN", "error");
        setShowEnabler(false);
      } else {
        navigate(response?.activityId);
        setShowEnabler(false);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const handleBiomatricSubmit = async () => {
    // Use currentPrivateKey if available, otherwise fall back to storedPrivateKey from context
    // This handles the case where biometric auth is being set up for the first time
    const privateKey = currentPrivateKey || storedPrivateKey;
    if (privateKey) {
      setLoading(true);
      validatePin(privateKey);
    } else {
      console.log("No private key available for biometric authentication");
    }
  };
  const handleSubmit = async () => {
    const encryptedPin = await encryptPIN(fields.join(""));
    validatePin(encryptedPin);
  };
  const getUserDetails = async () => {
    try {
      const userDetails = await GetUserDetails();
      setId(userDetails?.id);
    } catch (error) {}
  };

  // Function to check current fingerprint status from storage
  const checkCurrentFingerPrintStatus = async () => {
    try {
      const userDetails = await GetUserDetails();
      if (userDetails?.id) {
        const fingerPrintStatus = await AsyncStorage.getItem(
          `fingerPrintStatus${userDetails.id}`
        );
        const privateKey = await SecureStore.getItemAsync(
          `privateKey${userDetails.id}`
        );

        setCurrentFingerPrintStatus(fingerPrintStatus);
        setCurrentPrivateKey(privateKey);
      }
    } catch (error) {
      console.error("Error checking fingerprint status:", error);
    }
  };

  const persistBiomatric = () => {
    AsyncStorage.setItem(`fingerPrintStatus${id}`, "true")
      .then(() => {
        // @ts-ignore
        setStoredFingerPrintStatus("true");

        // Refresh the current fingerprint status to get the latest private key
        checkCurrentFingerPrintStatus();

        handleBiomatricSubmit();
      })
      .catch((err) => {});
  };

  useEffect(() => {
    getUserDetails();
    async () => {
      const isCompatible = await LocalAuthentication.hasHardwareAsync();
      if (!isCompatible) {
        setIsBioMatricSupported(false);
        throw new Error("Your device isn't compatible.");
      }
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      if (!isEnrolled) {
        setIsBioMatricSupported(false);
        throw new Error("No Faces / Fingers found.");
      }
    };
  }, []);

  // Check fingerprint status when screen is focused
  useFocusEffect(
    React.useCallback(() => {
      checkCurrentFingerPrintStatus();
    }, [])
  );

  useEffect(() => {
    if (fields.every((field) => field !== "")) {
      // All fields have been filled, call verifyPin
      // handleSubmit();
    }
  }, [fields]);

  return (
    <>
      <View style={styles.body}>
        <Div>
          <ScrollView>
            <AuthenticationHedear
              text="Transaction PIN"
              navigation={navigation}
            />
            <View style={styles.contentBody}>
              <View style={styles.inputCardWrap}>
                <P
                  style={{
                    color: colors.dGray,
                    textAlign: "center",
                    marginBottom: 16,
                    fontSize: 12,
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  Enter transaction PIN
                </P>
                <View style={styles.con}>
                  {refs.map((ref, index) => (
                    <View
                      key={index}
                      style={[
                        styles.pinInput,
                        {
                          marginRight: index === refs.length - 1 ? 0 : 16,
                          borderColor: invalidFields[index]
                            ? colors.red // Red border for invalid fields
                            : activeIndex === index
                            ? colors.primary
                            : "#E6E5E5",
                        },
                      ]}
                    >
                      <View style={styles.pinView}>
                        {showDots[index] ? (
                          <View style={styles.dot} />
                        ) : (
                          <Text style={styles.pinText}>{fields[index]}</Text>
                        )}
                      </View>
                    </View>
                  ))}
                </View>
              </View>
              <View style={styles.bottom}>
                <View style={{ width: "90%", alignSelf: "center" }}>
                  <Keyboard onKeyPress={handleKeyPress} />
                </View>
                <View
                  style={{
                    width: "80%",
                    alignSelf: "center",
                    marginTop: (2 * height) / 100,
                  }}
                >
                  <Button
                    btnText="Enter pin"
                    onPress={handleSubmit}
                    loading={loading}
                  />
                  {isBioMatricSupported && (
                    <>
                      <View style={styles.seprator}>
                        <View style={styles.line}></View>
                        <P
                          style={{
                            fontSize: 12,
                            fontFamily: fonts.poppinsRegular,
                            color: colors.gray,
                          }}
                        >
                          Or
                        </P>
                        <View style={styles.line}></View>
                      </View>
                      <TouchableOpacity
                        style={{
                          width: "100%",
                          alignSelf: "center",
                          alignItems: "center",
                          justifyContent: "center",
                          borderWidth: 1,
                          borderColor: colors.stroke,
                          borderRadius: 100,
                          height: (44 / baseHeight) * height,
                          flexDirection: "row",
                          marginTop: (2 * height) / 100,
                        }}
                        onPress={() => {
                          if (
                            currentFingerPrintStatus &&
                            currentPrivateKey !== null
                          ) {
                            TransactionAuth(handleBiomatricSubmit, handleToast);
                          } else {
                            setShowEnabler(true);
                          }
                        }}
                      >
                        <SvgXml
                          xml={
                            Platform.OS === "ios"
                              ? svg.faceIdGray
                              : svg.fingerPrint
                          }
                          style={{ marginRight: 4 }}
                        />
                        <Text
                          style={{
                            marginLeft: 4,
                            color: colors.primary,
                            fontFamily: fonts.poppinsRegular,
                            fontSize: 12,
                          }}
                        >
                          {Platform.OS === "ios" ? "Face ID" : "Fingerprint"}
                        </Text>
                      </TouchableOpacity>
                    </>
                  )}
                </View>
              </View>
            </View>
          </ScrollView>
        </Div>
        {/* {showSendStatus && (
          <SendMoneyStatus
            okayPress={() => {
              navigation.navigate("Home");
            }}
            viewDetailPress={() =>
              navigation.navigate("AllTransactionDetails", {
                transactionType: "sfx money app",
              })
            }
          />
        )} */}
        {showEnabler && (
          <EnableBiomatricComponent
            visible={showEnabler}
            onClose={() => {
              setShowEnabler(false);
            }}
            secondaryFunction={() => {
              persistBiomatric();
            }}
          />
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: "rgba(247, 244, 255, 1)",
  },
  contentBody: {
    width,
    height: (92 * height) / 100,
    backgroundColor: "rgba(247, 244, 255, 1)",
    paddingTop: 16,
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
    paddingTop: 24,
    backgroundColor: "#fff",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    width: 56,
    height: 56,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 18,
  },
  pinView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  pinText: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
  },
  dot: {
    width: 16, // Bigger size for the dot
    height: 16,
    backgroundColor: "#000",
    borderRadius: 12,
  },
  con: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: "75%",
    marginBottom: 32,
  },
  bottom: {
    width,
    marginTop: (80 / baseHeight) * height,
  },
  line: {
    width: "43%",
    height: 1,
    backgroundColor: colors.stroke,
  },
  seprator: {
    width: "100%",
    lineHeight: 19,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 16,
  },
});
