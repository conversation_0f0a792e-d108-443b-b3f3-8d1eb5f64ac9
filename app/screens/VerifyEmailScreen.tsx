import React, { useRef, useState, useEffect } from "react";
import {
  Dimensions,
  Keyboard,
  ScrollView,
  StyleSheet,
  TextInput,
  View,
} from "react-native";
import Div from "../components/Div";
import { colors } from "../config/colors";
import P from "../components/P";
import { fonts } from "../config/Fonts";
import Button from "../components/Button";
import Link from "../components/Link";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import ResendOtp from "../components/ResendOtp";
import BottomComponent from "../components/BottomComponent";
import {
  ResetPasswordVerifyOtp,
  SendOtp,
  VerifyOtp,
} from "../RequestHandlers/Authentication";
import { useToast } from "../context/ToastContext";
import AuthHeader from "../components/AuthHeader";
import H4 from "../components/H4";
import { withApiErrorToast } from "../Utils/withApiErrorToast";

const screenHeight = Dimensions.get("window").height;

export default function VerifyEmailScreen({ navigation, route }) {
  const { handleToast } = useToast();
  const { email } = route.params;
  const ref_input1 = useRef();
  const ref_input2 = useRef();
  const ref_input3 = useRef();
  const ref_input4 = useRef();
  const ref_input5 = useRef();
  const refs = [ref_input1, ref_input2, ref_input3, ref_input4, ref_input5];
  const [fields, setFields] = useState(["", "", "", "", ""]);
  const [code, setCode] = useState("");
  const [loading, setLoading] = useState(false);
  const [validationError, setValidationError] = useState([
    false,
    false,
    false,
    false,
    false,
  ]);
  const [activityId, setActivityId] = useState("");

  const focusNextField = (nextField: any) => {
    nextField.current.focus();
  };

  const handleKeyPress = (index: any, event: any) => {
    const { nativeEvent } = event;

    if (nativeEvent.key === "Backspace" || nativeEvent.key === "Delete") {
      if (fields[index] === "") {
        const prevIndex = index - 1;
        if (prevIndex >= 0) {
          setFields((prevFields) => {
            const updatedFields = [...prevFields];
            updatedFields[prevIndex] = "";
            return updatedFields;
          });
          focusNextField(refs[prevIndex]);
        }
      } else {
        setFields((prevFields) => {
          const updatedFields = [...prevFields];
          updatedFields[index] = "";
          return updatedFields;
        });
      }
    }
  };

  const handleChangeText = (index: any, text: any) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;

      if (text !== "") {
        const nextIndex = index + 1;
        if (nextIndex < updatedFields.length) {
          focusNextField(refs[nextIndex]);
        } else {
          Keyboard.dismiss();
        }
      }

      return updatedFields;
    });

    // Reset validation error when user types
    setValidationError((prevErrors) => {
      const updatedErrors = [...prevErrors];
      updatedErrors[index] = false;
      return updatedErrors;
    });
  };

  const handlePaste = (index: any, pastedText: string) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      const characters = pastedText.split("");

      for (let i = 0; i < characters.length; i++) {
        const fieldIndex = index + i;
        if (fieldIndex < updatedFields.length) {
          updatedFields[fieldIndex] = characters[i];
        }
      }

      return updatedFields;
    });
  };

  const validateFields = () => {
    const errors = fields.map((field) => field === "");
    setValidationError(errors);
    return errors.every((error) => !error);
  };

  const verifyOtp = async () => {
    if (!validateFields()) {
      return;
    }
    setLoading(true);
    try {
      const body = {
        otp: Number(code),
        email: email.email,
        type: "reset",
      };
      const verifyOtp = await withApiErrorToast(
        ResetPasswordVerifyOtp(body),
        handleToast
      );
      console.log();

      if (verifyOtp.status === true) {
        setLoading(false);
        handleToast("Successful", "success");
        setTimeout(() => {
          navigation.navigate("ResetPasswordScreen", {
            email: email.email,
            activityId: verifyOtp.activityId,
          });
        }, 2000);
      } else {
        setLoading(false);
        handleToast(verifyOtp.message, "error");
      }
    } catch (error) {
      setLoading(false);
      console.log(error);
      handleToast("Network error", "error");
    }finally{
      setLoading(false)
    }
  };

  useEffect(() => {
    if (fields.every((field) => field !== "")) {
      setCode(fields.join(""));
    }
  }, [fields]);

  const resendOtp = async () => {
    try {
      const body = {
        email: email.email,
        type: "reset",
      };
      const resendOtp = await SendOtp(body);
      // if (resendOtp.email) {
      //   handleToast("Code sent succefully", "success");
      // } else {
      //   handleToast(resendOtp.message, "error");
      // }
    } catch (error) {
      handleToast("Network error", "error");
    }
  };

  return (
    <View style={styles.container}>
      <Div>
        <AuthHeader style={{ width: "95%" }} navigation={navigation} />
        <View
          style={{
            width: "90%",
            justifyContent: "center",
            alignSelf: "center",
            marginTop: 8,
            alignItems: "center",
          }}
        >
          <H4 style={styles.text1}>Verify email</H4>
          <P style={styles.text2}>
            Enter the code sent to{"\n"}
            {email.email} via email
          </P>
        </View>
        <View style={styles.components}>
          <View style={styles.con}>
            {refs.map((ref, index) => (
              <View
                style={[
                  styles.pinInput,
                  validationError[index] && { borderColor: colors.red }, // Apply red border if there's an error
                ]}
                key={index}
              >
                <TextInput
                  style={styles.pinTextInput}
                  placeholderTextColor="#000"
                  keyboardType="numeric"
                  ref={ref}
                  key={index}
                  onChangeText={(text) => handleChangeText(index, text)}
                  onKeyPress={(event) => handleKeyPress(index, event)}
                  // @ts-ignore
                  onTextInput={(event) => {
                    const pastedText = event.nativeEvent.text;
                    handlePaste(index, pastedText);
                  }}
                  value={fields[index]}
                />
              </View>
            ))}
          </View>

          <ResendOtp setResend={resendOtp} />
          <Button btnText="Continue" loading={loading} onPress={verifyOtp} />

          <View
            style={{
              flexDirection: "row",
              justifyContent: "center",
              marginTop: 32,
            }}
          >
            <P
              style={{
                fontSize: 12,
                lineHeight: 22.4,
                fontFamily: fonts.poppinsRegular,
              }}
            >
              Remember password?{" "}
            </P>
            <Link
              style={{
                fontSize: 12,
                lineHeight: 21,
                textDecorationLine: "underline",
              }}
              onPress={() => navigation.navigate("NewLoginScreen")}
            >
              Login
            </Link>
          </View>
        </View>

        <BottomComponent navigation={navigation} />
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    height: screenHeight,
  },

  text1: {
    fontSize: 20,
    fontFamily: fonts.poppinsBold,
    lineHeight: 30,
  },
  text2: {
    fontSize: 14,
    lineHeight: 22.4,
    fontFamily: fonts.poppinsRegular,
    textAlign: "center",
  },
  components: {
    width: "90%",
    marginTop: 24,
    alignSelf: "center",
  },
  pinInput: {
    borderWidth: 1,
    borderColor: colors.stroke,
    borderRadius: 8,
    width: 56,
    height: 56,
    marginBottom: 18,
  },
  pinTextInput: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
    width: "100%",
    height: 56,
  },
  con: {
    flexDirection: "row",
    width: "100%",
    justifyContent: "center",
    gap: 8,
  },
});
