import React, { useRef, useState, useEffect, useContext } from "react";
import {
  Dimensions,
  Keyboard,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import Div from "../components/Div";
import { colors } from "../config/colors";
import P from "../components/P";
import { fonts } from "../config/Fonts";
import Button from "../components/Button";
import Link from "../components/Link";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import Input from "../components/Input";
import BottomComponent from "../components/BottomComponent";
import SignupHeader from "../components/SignupHeader";

import { CreateUser } from "../RequestHandlers/Authentication";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "../RequestHandlers/CredentailsContext";
import { useToast } from "../context/ToastContext";
import { encryptPIN } from "../Utils/encrypt";
import AuthHeader from "../components/AuthHeader";
import H4 from "../components/H4";

const screenHeight = Dimensions.get("window").height;
export default function TransactionPinScreen({ navigation, route }) {
  const { values1, values2, countryCode, username } = route?.params;
  const { handleToast } = useToast();
  const [loading, setLoading] = useState(false);
  const ref_input1 = useRef();
  const ref_input2 = useRef();
  const ref_input3 = useRef();
  const ref_input4 = useRef();
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);
  const refs = [ref_input1, ref_input2, ref_input3, ref_input4];
  const [fields, setFields] = useState(["", "", "", ""]);
  const [show, setshow] = useState(true);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [pin, setPin] = useState("");
  const focusNextField = (nextField: any) => {
    nextField.current.focus();
  };
  const handleKeyPress = (index: any, event: any) => {
    const { key, nativeEvent } = event;

    if (nativeEvent.key === "Backspace" || nativeEvent.key === "Delete") {
      if (fields[index] === "") {
        const prevIndex = index - 1;
        if (prevIndex >= 0) {
          setFields((prevFields) => {
            const updatedFields = [...prevFields];
            updatedFields[prevIndex] = "";
            return updatedFields;
          });
          focusNextField(refs[prevIndex]);
        }
      } else {
        setFields((prevFields) => {
          const updatedFields = [...prevFields];
          updatedFields[index] = "";
          return updatedFields;
        });
      }
    }
  };

  const handleChangeText = (index: any, text: any) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;
      if (text !== "") {
        const nextIndex = index + 1;
        if (nextIndex < updatedFields.length) {
          focusNextField(refs[nextIndex]);
        } else {
          Keyboard.dismiss();
        }
      }
      return updatedFields;
    });
  };
  const persistLogin = (credentail: any, message?: any, status?: any) => {
    AsyncStorage.setItem("cookies", JSON.stringify(credentail))
      .then(async () => {
        // @ts-ignore
        setStoredCredentails(credentail);
      })
      .catch((err) => {});
  };

  const createUser = async () => {
    // Build the body object conditionally
    const body: any = {
      email: values1.email.trim(),
      password: values1.password.trim(),
      confirmPassword: values1.confirmPassword.trim(),
      referralCode: values1.referralCode.trim(),
      firstName: values2.firstName.trim(),
      lastName: values2.lastName.trim(),
      dob: values2.dob.trim(),
      phoneNumber: `${countryCode}${values2.phoneNumber
        .trim()
        .replace(/^0/, "")}`,
      homeCountry: values2.homeCountry.trim(),
      residentAddress: values2.residentAddress.trim(),
      username: username.username.trim(),
      pin: await encryptPIN(String(fields.join(""))),
    };
    // Only include middleName if it exists and is not empty
    if (values2.middleName && values2.middleName.trim() !== "") {
      body.middleName = values2.middleName.trim();
    }
    try {
      const createUser = await CreateUser(body);
      // console.log(body);
      // console.log(createUser);

      if (createUser.newAccount === true) {
        await AsyncStorage.setItem("newUser", "true");
      }
      if (createUser.token) {
        setLoading(false);
        setTimeout(() => {
          persistLogin(createUser);
        }, 500);
      } else {
        setLoading(false);
        handleToast(createUser.message, "error");
      }
    } catch (error) {
      setLoading(false);
      handleToast(error.message, "error");
    }
  };
  const handleSubmit = () => {
    setIsSubmitted(true);
    setLoading(true);
    if (fields.every((field) => field !== "")) {
      createUser();
    } else {
      setIsSubmitted(false);
      setLoading(false);
    }
  };

  const getInputBorderStyle = (index) => {
    // Red border if field is empty and the form has been submitted
    return {
      borderColor: fields[index] === "" && isSubmitted ? colors.red : "#E6E5E5",
    };
  };

  const removeFlag = async () => {
    const res = await AsyncStorage.removeItem("hasLoggedInBefore");
    console.log("hdhdhdhdhhd", res);
  };

  useEffect(() => {
    removeFlag();
  }, []);

  return (
    <View style={styles.container}>
      <Div>
        <AuthHeader style={{ width: "90%" }} navigation={navigation} />

        <View style={styles.header}>
          <H4 style={styles.text1}>Create your transaction PIN</H4>
          <P style={styles.text2}>
            Create a transaction PIN to secure {"\n"}your money
          </P>
        </View>
        <View style={styles.components}>
          <View style={styles.con}>
            {refs.map((ref, index) => (
              <View
                style={[styles.pinInput, getInputBorderStyle(index)]}
                key={index}
              >
                <TextInput
                  style={styles.pinTextInput}
                  placeholderTextColor="#000"
                  keyboardType="numeric"
                  ref={ref}
                  onChangeText={(text) => handleChangeText(index, text)}
                  onKeyPress={(event) => handleKeyPress(index, event)}
                  value={fields[index]}
                  secureTextEntry={show}
                  maxLength={1}
                />
              </View>
            ))}
            <TouchableOpacity
              style={{ height: 44, justifyContent: "center" }}
              onPress={() => setshow(!show)}
            >
              <SvgXml xml={svg.bigeye} />
            </TouchableOpacity>
          </View>

          <Button btnText="Next" onPress={handleSubmit} loading={loading} />

          <View style={styles.footer}>
            <P style={styles.footerText}>Do you have an account? </P>
            <Link
              style={styles.footerLink}
              onPress={() => navigation.navigate("NewLoginScreen")}
            >
              Login
            </Link>
          </View>
        </View>

        <BottomComponent navigation={navigation} />
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    height: screenHeight,
  },
  header: {
    width: "85%",
    justifyContent: "center",
    alignSelf: "center",
    marginTop: 8,
    alignItems: "center",
  },
  text1: {
    fontSize: 20,
    fontFamily: fonts.poppinsBold,
    lineHeight: 30,
  },
  text2: {
    fontSize: 14,
    lineHeight: 22.4,
    fontFamily: fonts.poppinsRegular,
    textAlign: "center",
  },
  components: {
    width: "85%",
    marginTop: 24,
    alignSelf: "center",
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    width: 56,
    height: 56,
    alignItems: "center",
    justifyContent: "center",
  },
  pinTextInput: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
    width: "100%",
  },
  con: {
    flexDirection: "row",
    justifyContent: "center",
    width: "100%",
    gap: 10,
    marginBottom: 32,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 32,
  },
  footerText: {
    fontSize: 12,
    lineHeight: 22.4,
    fontFamily: fonts.poppinsRegular,
  },
  footerLink: {
    fontSize: 12,
    lineHeight: 21,
    textDecorationLine: "underline",
  },
});
