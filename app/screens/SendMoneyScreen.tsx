import React from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../config/Fonts";
import Div from "../components/Div";
import AuthenticationHedear from "../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "../components/P";
import MicroBtn from "../components/MicroBtn";
import { colors } from "../config/colors";
import DetailCard from "../components/DetailCard";
import Button from "../components/Button";
import Content from "../components/Content";

const { width, height } = Dimensions.get("window");

export default function SendMoneyScreen({ navigation }) {
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Send Money" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <Content
                svg1={svg.smalllogo}
                header="SFx wallet"
                body="Instantly send money to an SFx username"
                onPress={() => navigation.navigate("AccountDetailsEntry")}
              />
              <Content
                svg1={svg.bankGreen}
                header="Local Bank account"
                body="Send money to local bank accounts in 14 African countries. Arrives in 5–15 minutes."
                onPress={() => navigation.navigate("BankMobileMoneyScreen2")}
              />
              <Content
                svg1={svg.mobile}
                header="Mobile money"
                body="Send money to a mobile money wallet in 14 African countries. Arrives in 5–15 minutes."
                onPress={() => navigation.navigate("MobileMoneyScreen2")}
              />
              <Content
                svg1={svg.p2p}
                header="Crypto wallet"
                body="Send money to your blockchain address. Instant arrival."
                bottomBorder={false}
                onPress={() => navigation.navigate("P2pScreen")}
              />
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    minHeight: 304,
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent:"center",
    alignItems: "center",
  },

  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
});
