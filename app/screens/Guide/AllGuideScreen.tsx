import React, { useContext, useState } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Linking,
  TextInput
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHeader from "../../components/AuthenticationHedear";
import { fonts } from "../../config/Fonts";
import P from "../../components/P";
import { ScrollView } from "react-native-gesture-handler";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import Link from "../../components/Link";
import { object } from "yup";
import { svg2 } from "../../config/Svg2";
import { CredentailsContext } from "../../RequestHandlers/CredentailsContext";

const { width, height } = Dimensions.get("window");
const groupByDate = (items) => {
  if (!items || !Array.isArray(items)) {
    return {};
  }
  return items.reduce((acc, item) => {
    const date = item.date || "Unknown Date";
    acc[date] = acc[date] || [];
    acc[date].push(item);
    return acc;
  }, {});
};
export default function AllGuideScreen({ navigation }) {
  const tabs = ["All", "Add & send money", "Verification"];
  const [activeTab, setActiveTab] = useState("All");
  const [searchQuery, setSearchQuery] = useState("")
  const {storedCredentails} = useContext(CredentailsContext);
  const forYouData = [
    {
      title: "How to share SFxmoney app",
      url: "https://www.youtube.com/shorts/ZrYQSXzFuQs",
      date: "Today",
    },
    {
      title: "How to send money with SFx",
      url: "https://www.youtube.com/shorts/ZrYQSXzFuQs",
      date: "Today",
    },
    {
      title: "How to verify your money app account",
      url: "https://www.youtube.com/shorts/ZrYQSXzFuQs",
      date: "Yesterday",
    },
    {
      title: "How to add money with SFx",
      url: "https://www.youtube.com/shorts/ZrYQSXzFuQs",
      date: "Yesterday",
    },
  ];
  const safeForYouData = forYouData || [];
  const groupedForYou = groupByDate(safeForYouData) || {};
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHeader text="For You" navigation={navigation}/>
        <View style={styles.tabs}>
          <ScrollView contentContainerStyle={{ width: "100%" }} horizontal>
            {tabs.map((item, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  setActiveTab(item);
                }}
              >
                <View
                  style={[
                    styles.tabBtn,
                    {
                      backgroundColor:
                        activeTab === item ? colors.white : "transparent",
                    },
                  ]}
                >
                  <P
                    //@ts-ignore
                    style={[
                      styles.tabBtnP,
                      {
                        color:
                          activeTab === item ? colors.primary : colors.gray,
                      },
                    ]}
                  >
                    {item}
                  </P>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
        {forYouData?.length == 0 ? <></> :      <View style={styles.customInput}>
                <SvgXml xml={svg.search} style={{ marginRight: 8 }} />
                <TextInput
                  style={styles.input}
                  placeholder="Search"
                  cursorColor={colors.black}
                  value={searchQuery}
                  onChangeText={(text) => setSearchQuery(text)}
                />
              </View>}
        <ScrollView style={{ width }}>
          {forYouData.length === 0 ? (
            <View
              style={{
                width: "100%",
                alignItems: "center",
                marginTop: 80,
              }}
            >
              <SvgXml xml={svg.bookOpen} />
              <P style={{ marginTop: 16 }}>No for you!</P>
              <P
                style={{
                  color: colors.dGray,
                  fontFamily: fonts.poppinsRegular,
                  marginTop: 4,
                }}
              >
                There’s no for you to view now
              </P>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  width: "100%",
                  justifyContent: "center",
                  marginTop: (2.5 * height) / 100,
                }}
              >
                <P
                  style={{
                    lineHeight: 22.4,
                    color: colors.gray,
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  Do you need help?{" "}
                </P>
                <Link
                  style={{ textDecorationLine: "underline" }}
                  onPress={() => {
                    const username = storedCredentails?.user?.username || "Not provided";
                    const email = storedCredentails?.user?.email || "Not provided";
                    const message = `Hi, Support, I have an issue that requires resolving.\nMy Username is ${username} and My email is ${email}`;
                    const encodedMessage = encodeURIComponent(message);
                    Linking.openURL(
                      `https://wa.me/905338563416?text=${encodedMessage}`
                    );
                  }}
                >
                  Chat SFx team
                </Link>
              </View>
            </View>
          ) : (
            <View style={{ width: "90%", alignSelf: "center" }}>
              {Object.keys(groupedForYou).map((date, index) => (
                <View key={index} style={{ marginTop: 24 }}>
                  <P style={styles.datCat}>{date}</P>
                  <View>
                    {groupedForYou[date].map((item, index) => (
                      <TouchableOpacity
                        onPress={() => {
                          Linking.openURL(item.url);
                        }}
                      >
                        <View
                          style={{ alignItems: "center", marginTop: 12 }}
                        ></View>
                        <View
                          style={[
                            styles.card,
                            {
                              width: "100%",
                            },
                          ]}
                        >
                          <View style={styles.recommendation}>
                            <View style={styles.recommendationText}>
                              <P
                                // @ts-ignore
                                style={[
                                  styles.recommendationMessage,
                                  { width: 160 },
                                ]}
                              >
                                {" "}
                                {item.title}
                              </P>
                            </View>
                            <SvgXml
                              xml={
                                item.title.includes("share")
                                  ? svg.megaPhone
                                  : item.title.includes("add money")
                                  ? svg2.megaWallet
                                  : item?.title?.includes("send money")
                                  ? svg2.sMoney
                                  : item?.title?.includes("verify")
                                  ? svg.megaLaw
                                  : svg.megaPhone
                              }
                            />
                          </View>
                        </View>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              ))}
            </View>
          )}
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  tabs: {
    width: (100 * width) / 100,
    flexDirection: "row",
    alignItems: "center",
    borderBottomWidth: 1,
    borderTopWidth: 1,
    borderColor: colors.stroke,
    paddingLeft: "5%",
    paddingRight: "5%",
    marginTop: 12,
    paddingBottom: 16,
    paddingTop: 12,
  },
  tabBtn: {
    height: 24,
    borderRadius: 24,
    alignItems: "center",
    justifyContent: "center",
    paddingRight: 24,
    paddingLeft: 24,
  },
  tabBtnP: {
    fontSize: 11,
    lineHeight: 18,
    fontFamily: fonts.poppinsMedium,
  },
  datCat: {
    fontSize: 12,
  },
  card: {
    // marginRight: "5%",
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 18,
    marginBottom: 8,
    height: 106,
    flexDirection: "row",
    alignItems: "center",
  },
  recommendation: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  recommendationText: {},
  recommendationMessage: {
    fontSize: 14,
    color: colors.white,
    width: 218,
    fontFamily: fonts.poppinsSemibold,
  },
  customInput: {
    width: "90%",
    height: 44,
    backgroundColor: colors.white,
    alignSelf: "center",
    borderRadius: 12,
    marginTop: 16,
    paddingLeft: 16,
    paddingRight: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  input: {
    width: "80%",
    height: 44,
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
  },
});

