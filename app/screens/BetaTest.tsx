import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  ScrollView,
  Text,
  Linking,
  TouchableOpacity,
} from "react-native";
import { colors } from "../config/colors";
import Div from "../components/Div";
import P from "../components/P";
import { fonts } from "../config/Fonts";
import { svg } from "../config/Svg";
import Svg, { SvgXml } from "react-native-svg";
import { infos } from "../Utils/BetaTestInfo";
import Button from "../components/Button";
import AsyncStorage from "@react-native-async-storage/async-storage";

const { width, height } = Dimensions.get("window");
export default function BetaTest({ navigation, route }) {
  const [isTermAgreed, setIsTermAgreed] = useState(false);
  const { nextRoute } = route?.params || "";
  const data = [
    {
      title: "Exclusive early access to new features",
      body: "Be among the first to experience the cutting-edge features of the SFx  money app",
      icon: svg.gridBox,
    },
    {
      title: "Provide valuable feedback to improve SFx",
      body: "Your participation and feedback will directly impact the SFx money app",
      icon: svg.announce,
    },
    {
      title: "Shape the future of the SFx experience",
      body: "Join a community of builders helping to provide better financial accessibility",
      icon: svg.greenPhone,
    },
  ];

  const handleAgreed = async () => {
    AsyncStorage.setItem("aggrr###", "true")
      .then(() => {
        navigation.navigate(nextRoute);
      })
      .catch((err) => {
    
      });
  };
  return (
    <View style={styles.body}>
      <Div>
        <ScrollView
          nestedScrollEnabled={true}
          contentContainerStyle={{ paddingBottom: 100 }}
        >
          <Image
            source={require("../assets/Bt-Image.png")}
            style={{
              width: (90 * width) / 100,
              height: 350,
              objectFit: "fill",
              marginTop: 48,
              alignSelf: "center",
            }}
          />
          <View style={{ alignSelf: "center", width: "90%" }}>
            <P
              style={{
                fontSize: 20,
                lineHeight: 28,
                textAlign: "center",
                fontFamily: fonts.poppinsSemibold,
              }}
            >
              Join the SFx beta test program
            </P>
            <P
              style={{
                textAlign: "center",
                fontFamily: fonts.poppinsRegular,
                color: colors.gray,
                marginTop: 8,
              }}
            >
              An international money experience
            </P>
            <View
              style={{
                alignSelf: "center",
                marginTop: 24,
                width: (85 * width) / 100,
              }}
            >
              {data.map((item, index) => (
                <View key={index} style={styles.listItem}>
                  <SvgXml xml={item.icon} />
                  <View>
                    <P
                      style={{
                        fontSize: 12,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      {item.title}
                    </P>
                    <P
                      style={{
                        fontSize: 12,
                        fontFamily: fonts.poppinsRegular,
                        color: colors.gray,
                        paddingRight: 30,
                      }}
                    >
                      {item.body}
                    </P>
                  </View>
                </View>
              ))}
            </View>
            <View style={styles.scrollBox}>
              <ScrollView
                nestedScrollEnabled={true}
                keyboardShouldPersistTaps="handled"
              >
                <P>Beta test program agreement</P>
                <View style={{ marginTop: 16 }}>
                  {infos.map((item, index) => (
                    <P
                      key={index}
                      style={{
                        fontSize: 12,
                        marginBottom: 16,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      {item}
                    </P>
                  ))}
                </View>
              </ScrollView>
            </View>
            <View
              style={{
                width: (85 * width) / 100,
                alignSelf: "center",
                flexDirection: "row",
                marginTop: 16,
                marginBottom: 32,
                alignItems: "center",
                gap: 8,
              }}
            >
              <TouchableOpacity
                onPress={() => {
                  setIsTermAgreed(!isTermAgreed);
                }}
              >
                <SvgXml
                  pointerEvents="none"
                  xml={isTermAgreed ? svg.checkedBox : svg.checkBox}
                />
              </TouchableOpacity>
              <P
                style={{
                  fontSize: 12,
                  lineHeight: 19.2,
                  color: "#A5A1A1",

                  //   textAlign: "center",
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Yes, I agree to SFx’s beta test program{"\n"}agreement{" "}
                <Text
                  style={{
                    textDecorationColor: "#A5A1A1",
                    textDecorationLine: "underline",
                  }}
                  onPress={() => {
                    Linking.openURL("https://www.sfxchange.co/en/privacy");
                  }}
                >
                  terms of service.
                </Text>
              </P>
            </View>
            <View style={{ width: (70 * width) / 100, alignSelf: "center" }}>
              <Button
                onPress={() => {
                  handleAgreed();
                }}
                disabled={!isTermAgreed}
                btnText="I agree to SFx’s terms of service"
              />
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
  listItem: {
    width: "100%",
    marginBottom: 24,
    flexDirection: "row",
    gap: 8,
  },
  scrollBox: {
    width: (85 * width) / 100,
    alignSelf: "center",
    height: 150,
    borderRadius: 12,
    padding: 16,
    paddingBottom: 0,
    backgroundColor: colors.secBackground,
    // position: "absolute",
  },
});
