import React, { useEffect, useRef } from "react";
import { View, StyleSheet, Animated, Dimensions, Easing } from "react-native";
import { colors } from "../config/colors";
import { svg } from "../config/Svg";
import { SvgXml } from "react-native-svg";
import P from "../components/P";
import { fonts } from "../config/Fonts";

const { width } = Dimensions.get("window");

export default function SplashScreen({ onFinish }) {
  // Animated value for scaling the app logo
  const scaleValue = useRef(new Animated.Value(0.5)).current; // Start at normal size (1)

  useEffect(() => {
    const animateSplash = () => {
      Animated.sequence([
        // Step 1: Delay before animation starts
        Animated.delay(100), // 100ms delay
        // Step 2: Smooth Scale up to 1.5
        Animated.timing(scaleValue, {
          toValue: 1,
          duration: 700, // Increase duration for smoothness
          easing: Easing.out(Easing.ease), // Smooth easing function
          useNativeDriver: true,
        }),
        // Step 3: Smooth Scale back to 1
        // Animated.timing(scaleValue, {
        //   toValue: 0.5,
        //   duration: 700,
        //   easing: Easing.out(Easing.ease), // Same smooth easing for consistency
        //   useNativeDriver: true,
        // }),
      ]).start(() => {
        // Trigger onFinish after the animation completes
        const timer = setTimeout(() => {
          onFinish();
        }, 500);
        return () => clearTimeout(timer);
      });
    };

    animateSplash();
  }, [scaleValue, onFinish]);

  return (
    <View style={styles.splashbody}>
      <View style={styles.wrap}>
        {/* Animated Scaling of the App Logo */}
        <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
          <SvgXml xml={svg.splashLogo} />
        </Animated.View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  splashbody: {
    flex: 1,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
  },
  wrap: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    position: "relative",
  },
});
