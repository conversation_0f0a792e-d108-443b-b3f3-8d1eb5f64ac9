import React, { useEffect, useRef, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  ScrollView,
  Text,
} from "react-native";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import Button from "../../components/Button";
import P from "../../components/P";
import Keyboard from "../../components/Keyboard";
import { fonts } from "../../config/Fonts";

const baseHeight = 802;
const { width, height } = Dimensions.get("window");

export default function FaceId({ navigation, onSuccess }) {
  const [fields, setFields] = useState(["", "", "", ""]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [showDots, setShowDots] = useState([false, false, false, false]);
  const [error, setError] = useState(false);
  const refs = [useRef(), useRef(), useRef(), useRef()];

  // Simulated PIN value for validation
  const correctPin = "1234";

  const handleKeyPress = (key) => {
    if (key === "←") {
      if (activeIndex > 0 || fields[activeIndex] !== "") {
        handleChangeText(activeIndex, "");
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[activeIndex] = false;
          return updatedDots;
        });
        if (activeIndex > 0) {
          setActiveIndex(activeIndex - 1);
        }
      }
    } else if (key === "Enter") {
      // Trigger PIN submission when Enter is pressed
      handleSubmit();
    } else {
      handleChangeText(activeIndex, key);
      if (activeIndex < 3) {
        setActiveIndex(activeIndex + 1);
      }
    }
  };

  const handleChangeText = (index, text) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;

      if (text !== "") {
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[index] = false;
          return updatedDots;
        });

        setTimeout(() => {
          setShowDots((prevShowDots) => {
            const updatedDots = [...prevShowDots];
            updatedDots[index] = true;
            return updatedDots;
          });
        }, 500);
      }
      return updatedFields;
    });
  };

  const handleSubmit = () => {
    const enteredPin = fields.join("");
    if (enteredPin === correctPin) {
      setError(false);
      onSuccess(); // Call the onSuccess callback to enable Face ID or perform other logic
    } else {
      setError(true);
      console.log("Incorrect PIN");
    }
  };

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Face Id" navigation={navigation} />
        <ScrollView contentContainerStyle={{ flexGrow: 1, paddingBottom: "35%" }}>
          <View style={styles.contentBody}>
            <View style={styles.inputCardWrap}>
              <P style={styles.pinPromptText}>Enter transaction PIN</P>
              <View style={styles.con}>
                {refs.map((ref, index) => (
                  <View
                    key={index}
                    style={[
                      styles.pinInput,
                      {
                        marginRight: index === refs.length - 1 ? 0 : 16,
                        borderColor: activeIndex === index ? colors.primary : "#E6E5E5",
                      },
                    ]}
                  >
                    <View style={styles.pinView}>
                      {showDots[index] ? (
                        <View style={styles.dot} />
                      ) : (
                        <Text style={styles.pinText}>{fields[index]}</Text>
                      )}
                    </View>
                  </View>
                ))}
              </View>
              {error && <Text style={styles.errorText}>Incorrect PIN. Please try again.</Text>}
            </View>

            <View style={{ width: "90%" }}>
              <Keyboard onKeyPress={handleKeyPress} />
            </View>
            <View style={{ width: "80%", marginTop: 32 / baseHeight * height }}>
              <Button btnText="Enter PIN" onPress={handleSubmit} />
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute"
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    marginTop: -16,
    alignItems: "center",
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
    paddingTop: 24,
    backgroundColor: "#fff",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: (166 / baseHeight) * height,
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    width: 48,
    height: 48,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 18,
  },
  pinView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  pinText: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
  },
  dot: {
    width: 16,
    height: 16,
    backgroundColor: "#000",
    borderRadius: 12,
  },
  con: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: "75%",
    marginBottom: 32,
  },
  pinPromptText: {
    color: colors.gray,
    textAlign: "center",
    marginBottom: (16 / baseHeight) * height,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
  },
  errorText: {
    color: "red",
    fontSize: 14,
    marginTop: 10,
  },
});
