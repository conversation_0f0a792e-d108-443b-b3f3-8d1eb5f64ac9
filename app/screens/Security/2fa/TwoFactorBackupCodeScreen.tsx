import React, { useEffect, useState } from "react";
import {
  Dimensions,
  Image,
  Platform,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
  Alert,
} from "react-native";
import AuthenticationHeader from "../../../components/AuthenticationHedear";
import Div from "../../../components/Div";
import { colors } from "../../../config/colors";
import P from "../../../components/P";
import { fonts } from "../../../config/Fonts";
import { Get2FaToken, GetBackupCode } from "../../../RequestHandlers/User";
import i18n from "../../../../i18n";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";
import Button from "../../../components/Button";
import Toast, { handleToast } from "../../../components/Toast";
import * as Clipboard from "expo-clipboard";
import { useToast } from "../../../context/ToastContext";
import QRCode from "react-native-qrcode-svg";
import Dash from "../../../components/Dash";
import * as FileSystem from "expo-file-system";
import * as Sharing from "expo-sharing";
import * as Print from "expo-print";
import * as MediaLibrary from "expo-media-library";
import RNFS from "react-native-fs";

const { width, height } = Dimensions.get("window");
export default function TwofactorBackupCodeScreen({ navigation, route }) {
  const { handleToast } = useToast();
  const { backupCode } = route.params || {};
  const [bCode, setBCode] = useState<any>([
    "911234",
    "608295",
    "391895",
    "285321",
    "693221",
    "976565",
    "222082",
    "589368",
    "902186",
    "350012",
    "114027",
    "266532",
    "308142",
    "205980",
    "585029",
    "376313",
  ]);

  const copyRefNum = async () => {
    const numbersString = bCode.join(", "); // Join array elements into a single string
    console.log(numbersString);

    await Clipboard.setStringAsync(numbersString); // Copy to clipboard
    handleToast("Codes copied", "success");
  };

  const generatePDF = async () => {
    try {
      // 1. Generate PDF
      const htmlContent = `
        <html>
          <head>
            <style>
              body { font-family: Arial, sans-serif; padding: 20px; }
              h1 { text-align: center; }
              ul { list-style-type: none; padding: 0; }
              li { font-size: 16px; margin-bottom: 5px; }
            </style>
          </head>
          <body>
            <h1>Your 2FA Backup Codes</h1>
            <ul>
              ${bCode.map((code) => `<li>${code}</li>`).join("")}
            </ul>
          </body>
        </html>
      `;

      const { uri } = await Print.printToFileAsync({ html: htmlContent });

      // 2. Create proper filename
      const fileName = `2FA_Backup_Codes_${Date.now()}.pdf`;
      const newUri = `${FileSystem.documentDirectory}${fileName}`;

      // 3. Move and save the file
      await FileSystem.moveAsync({
        from: uri,
        to: newUri,
      });

      // 4. Platform-specific handling
      if (Platform.OS === "android") {
        // Request permissions for Android
        const downloadsPath = RNFS.DownloadDirectoryPath + `/${fileName}`;
        await RNFS.moveFile(newUri, downloadsPath);
        handleToast("PDF saved to Downloads folder", "success");
      } else {
        // For iOS, use share dialog to save to Files app
        await Sharing.shareAsync(newUri, {
          mimeType: "application/pdf",
          dialogTitle: "Save 2FA Backup Codes",
          UTI: "com.adobe.pdf",
        });
        handleToast("PDF saved to Downloads folder", "success");
      }
    } catch (error) {
      handleToast(`Failed to save PDF: ${error.message}`, "error");
      console.error(error);
    }
  };

  useEffect(() => {
    if (backupCode) {
      setBCode(backupCode);
    }
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHeader
          text="2FA authenticator"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.content}>
            <P style={{ textAlign: "center", marginTop: 4 }}>Backup code</P>
            <P
              style={{
                fontSize: 12,
                fontFamily: fonts.poppinsRegular,
                color: colors.dGray,
                textAlign: "center",
              }}
            >
              If you lose access to your AUTH app, you can only use your
              recovery key to reset it
            </P>
            <View
              style={{
                width: "100%",
                padding: 16,
                backgroundColor: "#F7F4FF",
                borderRadius: 8,
                marginTop: 24,
                marginBottom: 24,
                alignItems: "center",
              }}
            >
              <View
                style={{
                  width: "100%",
                  flexDirection: "row",
                  flexWrap: "wrap",
                }}
              >
                {bCode.map((item) => (
                  <View
                    key={item}
                    style={{
                      minWidth: "25%",
                      width: "auto",
                      alignItems: "center",
                      justifyContent: "center",
                      marginBottom: 16,
                    }}
                  >
                    <P style={{ fontSize: 12 }}>{item}</P>
                  </View>
                ))}
              </View>
              <TouchableOpacity
                style={{
                  borderColor: colors.primary,
                  borderWidth: 1,
                  borderRadius: 100,
                  paddingTop: 5,
                  paddingBottom: 5,
                  paddingLeft: 13,
                  paddingRight: 13,
                  flexDirection: "row",
                  gap: 4,
                  marginTop: 8,
                  alignItems: "center",
                }}
                onPress={() => {
                  copyRefNum();
                }}
              >
                <P style={{ fontSize: 12, color: colors.primary }}>Copy</P>
                <SvgXml xml={svg.copy} />
              </TouchableOpacity>
            </View>
            {Platform.OS === "ios" ? (
              <Dash />
            ) : (
              <View style={styles.dash}></View>
            )}

            <View
              style={{
                width: "100%",
                marginTop: 26,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <View
                style={{ flexDirection: "row", gap: 8, alignItems: "center" }}
              >
                <SvgXml xml={svg.pdf} />
                <View>
                  <P style={{ fontSize: 12 }}>Backup_key</P>
                  <P style={{ fontSize: 12, fontFamily: fonts.poppinsRegular }}>
                    4.2 MB
                  </P>
                </View>
              </View>
              <TouchableOpacity
                onPress={() => {
                  generatePDF();
                }}
                style={{
                  paddingHorizontal: 10,
                  paddingVertical: 4,
                  borderRadius: 100,
                  borderWidth: 1,
                  borderColor: colors.primary,
                  flexDirection: "row",
                  gap: 4,
                }}
              >
                <P style={{ color: colors.primary, fontSize: 10 }}>Download</P>
                <SvgXml xml={svg.downloadIcon} />
              </TouchableOpacity>
            </View>
          </View>

          <View style={{ width: "75%", alignSelf: "center", marginTop: 32 }}>
            <Button
              btnText="Continue"
              onPress={() => {
                navigation.navigate("SecurityIndex");
              }}
            />
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    width: "100%",
    backgroundColor: colors.secBackground,
  },
  content: {
    width: "90%",
    alignSelf: "center",
    marginTop: 16,
    backgroundColor: colors.white,
    padding: 24,
    borderRadius: 12,
    alignItems: "center",
  },
  line: {
    width: "45%",
    height: 1,
    backgroundColor: colors.stroke,
  },
  separator: {
    width: "100%",
    lineHeight: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 24,
    marginBottom: 24,
    // marginTop: (4.6 * height) / 100,
  },
  copyCont: {
    // width: "90%",
    backgroundColor: colors.secBackground,
    alignSelf: "center",
    borderWidth: 1,
    borderColor: colors.stroke,
    borderRadius: 8,
    marginTop: 6,
    paddingTop: 10,
    paddingBottom: 10,
    paddingLeft: 14,
    paddingRight: 14,
    alignItems: "center",
  },
  dash: {
    width: "100%",
    borderTopWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
});
