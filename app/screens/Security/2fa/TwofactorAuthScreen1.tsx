import React, { useEffect, useState } from "react";
import {
  Dimensions,
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import AuthenticationHeader from "../../../components/AuthenticationHedear";
import Div from "../../../components/Div";
import { colors } from "../../../config/colors";
import P from "../../../components/P";
import { fonts } from "../../../config/Fonts";
import { Get2FaToken } from "../../../RequestHandlers/User";
import i18n from "../../../../i18n";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";
import Button from "../../../components/Button";
import Toast, { handleToast } from "../../../components/Toast";
import * as Clipboard from "expo-clipboard";
import { useToast } from "../../../context/ToastContext";
import QRCode from "react-native-qrcode-svg";

const { width, height } = Dimensions.get("window");
export default function TwofacttoAuthScreen({ navigation, route }) {
  const [qrCode, setQrCode] = useState("data:image/png;base64,iVBORw0KGgo");
  const [authkey, setAuthKey] = useState("");
  const { type } = route?.params || "";
  const { ActivityFunction } = route?.params || "";
  const { handleToast } = useToast();

  const copyRefNum = async () => {
    const copiedText = await Clipboard.setStringAsync(authkey);
    if (copiedText === true) {
      handleToast("Code copied", "success");
    } else {
      handleToast("Error copying code", "error");
    }
  };
  const get2FaToken = async () => {
    try {
      const res = await Get2FaToken();      
      if (res.error) {
        handleToast(res.message);
      } else {
        setQrCode(res?.uri);
        setAuthKey(res?.secret);
      }
    } catch (error) {}
  };
  function addHyphens(input) {
    const formattedString = input?.match(/.{1,4}/g)?.join("-");
    return formattedString;
  }
  useEffect(() => {
    get2FaToken();
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHeader
          text="2FA authenticator"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.content}>
            <P
              style={{
                fontSize: 12,
                fontFamily: fonts.poppinsRegular,
                color: colors.dGray,
                textAlign: "center",
              }}
            >
              2FA authenticator
            </P>
            <P style={{ textAlign: "center", marginTop: 4 }}>
              Scan the image below with{"\n"}your 2FA authenticator
            </P>
            {/* <Image
              source={{ uri: qrCode }}
              style={{ width: (50 * width) / 100, height: (50 * width) / 100 }}
            /> */}
            <View style={{marginTop: 16}}>
             <QRCode
                value={qrCode}
                size={(50 * width) / 100}
                color="black"
                logo={require("../../../assets/qrSfx.png")}
                logoSize={40}
                logoBorderRadius={5}
                backgroundColor="white"
              />
            </View>
            <View style={styles.separator}>
              <View style={styles.line}></View>
              <P
                style={{
                  fontSize: 12,
                  fontFamily: fonts.poppinsRegular,
                  color: colors.gray,
                }}
              >
                {i18n.t("login.or")}
              </P>
              <View style={styles.line}></View>
            </View>
            <P
              style={{
                fontSize: 12,
                fontFamily: fonts.poppinsRegular,
                color: colors.dGray,
                textAlign: "center",
              }}
            >
              Manually enter the code below
            </P>
            <View style={styles.copyCont}>
              <P style={{ fontSize: 12 }}>{addHyphens(authkey)}</P>
              <TouchableOpacity
                style={{
                  backgroundColor: colors.primary,
                  borderRadius: 100,
                  paddingTop: 5,
                  paddingBottom: 5,
                  paddingLeft: 13,
                  paddingRight: 13,
                  flexDirection: "row",
                  gap: 4,
                  marginTop: 8,
                  alignItems: "center",
                }}
                onPress={() => {
                  copyRefNum();
                }}
              >
                <P style={{ fontSize: 12, color: colors.white }}>Copy</P>
                <SvgXml xml={svg.copy_pr} />
              </TouchableOpacity>
            </View>
          </View>

          <View style={{ width: "75%", alignSelf: "center", marginTop: 32 }}>
            <Button
              btnText="Continue"
              onPress={() => {
                navigation.navigate("TwofactorAuthScreen2", {
                  type: type,
                  ActivityFunction: ActivityFunction,
                });
              }}
            />
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    width: "100%",
    backgroundColor: colors.secBackground,
  },
  content: {
    width: "90%",
    alignSelf: "center",
    marginTop: 16,
    backgroundColor: colors.white,
    padding: 24,
    borderRadius: 12,
    alignItems: "center",
  },
  line: {
    width: "45%",
    height: 1,
    backgroundColor: colors.stroke,
  },
  separator: {
    width: "100%",
    lineHeight: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 24,
    marginBottom: 24,
    // marginTop: (4.6 * height) / 100,
  },
  copyCont: {
    // width: "90%",
    backgroundColor: colors.secBackground,
    alignSelf: "center",
    borderWidth: 1,
    borderColor: colors.stroke,
    borderRadius: 8,
    marginTop: 6,
    paddingTop: 10,
    paddingBottom: 10,
    paddingLeft: 14,
    paddingRight: 14,
    alignItems: "center",
  },
});
