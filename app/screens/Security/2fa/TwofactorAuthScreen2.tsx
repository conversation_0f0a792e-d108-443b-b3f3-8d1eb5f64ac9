import React, { useEffect, useRef, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  ScrollView,
  Text,
  TouchableOpacity,
} from "react-native";
import { fonts } from "../../../config/Fonts";
import Div from "../../../components/Div";
import AuthenticationHedear from "../../../components/AuthenticationHedear";
import { colors } from "../../../config/colors";
import Button from "../../../components/Button";
import P from "../../../components/P";
import Keyboard from "../../../components/Keyboard";
import NoteComponent2 from "../../../components/NoteComponent2";
import { SendOtp } from "../../../RequestHandlers/Authentication";
import {
  Enable2Fa,
  GetBackupCode,
  GetUserDetails,
  Verify2FaToken,
  VerifyBackupCode,
} from "../../../RequestHandlers/User";
import { VerifyOtp } from "../../../RequestHandlers/Authentication";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useToast } from "../../../context/ToastContext";
import Link from "../../../components/Link";
import Loader from "../../../components/ActivityIndicator";
import { withApiErrorToast } from "../../../Utils/withApiErrorToast";

const baseHeight = 802;
const { width, height } = Dimensions.get("window");

export default function TwofacttoAuthScreen2({ navigation, route }) {
  const { handleToast } = useToast();
  const [fields, setFields] = useState(["", "", "", "", "", ""]);
  const [activeIndex, setActiveIndex] = useState(0);
  const { tkn } = route.params || "";
  const [showDots, setShowDots] = useState([
    false,
    false,
    false,
    false,
    false,
    false,
  ]);
  const [error, setError] = useState(false);
  const [secondsLeft, setSecondsLeft] = useState(60);
  const [isCountdownActive, setIsCountdownActive] = useState(true);
  const refs = [useRef(), useRef(), useRef(), useRef(), useRef(), useRef];
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [code, setCode] = useState("");
  const { activityId } = route?.params || "";
  const { type } = route?.params || "";
  const { ActivityFunction } = route?.params || "";
  const [is2faEnabled, setIs2faEnabled] = useState(false);
  const [isBackUpCode, setIsBackUpCode] = useState(false);
  const [bCode, setBCode] = useState([]);
  const [loader, setLoader] = useState(false);

  const handleKeyPress = (key) => {
    if (key === "←") {
      if (activeIndex > 0 || fields[activeIndex] !== "") {
        handleChangeText(activeIndex, "");
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[activeIndex] = false;
          return updatedDots;
        });
        if (activeIndex > 0) {
          setActiveIndex(activeIndex - 1);
        }
      }
    } else if (key === "Enter") {
      // Handle enter key press
    } else {
      handleChangeText(activeIndex, key);
      if (activeIndex < 5) {
        setActiveIndex(activeIndex + 1);
      }
    }
  };
  const handleChangeText = (index, text) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;
      if (text !== "") {
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[index] = false;
          return updatedDots;
        });
        setTimeout(() => {
          setShowDots((prevShowDots) => {
            const updatedDots = [...prevShowDots];
            updatedDots[index] = true;
            return updatedDots;
          });
        }, 500);
      }
      return updatedFields;
    });
  };
  const enable2fa = async () => {
    setLoading(true);
    try {
      const body = {
        token: code?.trim(),
      };
      const res = await withApiErrorToast(Enable2Fa(body), handleToast);
      if (res.error) {
        handleToast(res.message, "error");
      } else {
        handleToast(res.message, "success");
        getBackupCode();
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const getBackupCode = async () => {
    setLoading(true);
    try {
      const res = await withApiErrorToast(GetBackupCode(), handleToast);
      if (res.error) {
        handleToast(res.message, "error");
      } else {
        setBCode(res.recoveryKeys);
        if (res.recoveryKeys.length === 0) {
          navigation.navigate("SecurityIndex");
        } else {
          navigation.navigate("TwofactorBackupCodeScreen", {
            backupCode: res.recoveryKeys,
          });
        }
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const verify2fa = async () => {
    setLoading(true);
    try {
      const body = activityId
        ? { token: code?.trim(), activityId: activityId }
        : {
            token: code?.trim(),
          };
      const res = await withApiErrorToast(
        Verify2FaToken(body, tkn),
        handleToast
      );
      if (res.error) {
        handleToast(res.message, "error");
      } else {
        if (type === "login") {
          AsyncStorage.setItem("login2fa", "true").then(() => {});
        }
        ActivityFunction();
      }
    } catch (error) {
      console.log(error);
      handleToast("Unknown error", "error");
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 5000);
    }
  };

  const verifyBackupCode = async () => {
    setLoading(true);
    try {
      const body = {
        recoveryKey: code?.trim(),
      };
      const res = await withApiErrorToast(VerifyBackupCode(body), handleToast);
      if (res.error) {
        handleToast(res.message, "error");
      } else {
        if (type === "login") {
          AsyncStorage.setItem("login2fa", "true").then(() => {});
        }
        ActivityFunction();
      }
    } catch (error) {
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 5000);
    }
  };

  const handleSubmit = () => {
    // Check if any field is empty
    if (fields.some((field) => field === "")) {
      setError(true);
    } else {
      setError(false);
      if (type === "enable-2fa") {
        enable2fa();
      } else {
        if (isBackUpCode) {
          verifyBackupCode();
        } else {
          verify2fa();
        }
      }
    }
  };
  useEffect(() => {
    if (fields.every((field) => field !== "")) {
      setCode(String(fields.join("")));
    }
  }, [fields]);

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="2FA authenticator"
          navigation={navigation}
          showBackArrow={type === "login" ? false : true}
        />
        <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
          <View style={styles.contentBody}>
            <View style={styles.inputCardWrap}>
              <P
                style={{
                  color: colors.gray,
                  textAlign: "center",
                  marginBottom: 16,
                  fontSize: 12,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                {isBackUpCode
                  ? "Enter back up code"
                  : "Enter the 6-digit code in authenticator"}
              </P>
              <View style={styles.con}>
                {refs.map((ref, index) => (
                  <View
                    key={index}
                    style={[
                      styles.pinInput,
                      {
                        marginRight: index === refs.length - 1 ? 0 : 8,
                        borderColor:
                          activeIndex === index
                            ? colors.primary
                            : error && fields[index] === ""
                            ? "red"
                            : "#E6E5E5",
                      },
                    ]}
                  >
                    <View style={styles.pinView}>
                      {showDots[index] ? (
                        <View style={styles.dot} />
                      ) : (
                        <Text style={styles.pinText}>{fields[index]}</Text>
                      )}
                    </View>
                  </View>
                ))}
              </View>

              {/* Error message */}
              {error && (
                <Text style={styles.errorText}>Please fill in all fields.</Text>
              )}
            </View>
            {type === "login" && (
              <Link
                onPress={() => {
                  setIsBackUpCode(true);
                }}
                style={{ fontSize: 12, marginTop: 16 }}
              >
                Use back up code
              </Link>
            )}
            <View
              style={{ width: "90%", marginTop: (100 / baseHeight) * height }}
            >
              <Keyboard onKeyPress={handleKeyPress} />
            </View>
            <View style={{ width: "80%", marginTop: 32 }}>
              <Button
                btnText="Enter PIN"
                onPress={handleSubmit}
                loading={loading}
              />
            </View>
          </View>
        </ScrollView>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    marginTop: -16,
    alignItems: "center",
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
    padding: (24 / baseHeight) * height,
    backgroundColor: "#fff",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    width: 48,
    height: 48,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 18,
  },
  pinView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  pinText: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
  },
  dot: {
    width: 16, // Bigger size for the dot
    height: 16,
    backgroundColor: "#000",
    borderRadius: 12,
  },
  con: {
    flexDirection: "row",
    justifyContent: "space-around",
    // width: "80%",
    // backgroundColor: "red",
    // marginBottom: 32,
  },
  errorText: {
    color: colors.red,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    marginTop: 8,
    marginBottom: 10,
  },
});
