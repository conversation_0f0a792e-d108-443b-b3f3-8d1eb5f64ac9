import React, { useState } from "react";
import { View, StyleSheet, Dimensions, ScrollView } from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import Button from "../../components/Button";
import Input from "../../components/Input";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import { TouchableOpacity } from "react-native-gesture-handler";
import BottomSheet from "../../components/BottomSheet";
import LocalGovSelect from "../../components/LocalGovSelect";
import SecurityQuestionSelect from "../../components/SecurityQuestionSelect";

const { width, height } = Dimensions.get("window");

export default function SecurtityQuestion({ navigation }) {
  const [showCountries, setShowCountries] = useState(false);
  const [question, setQuestion] = useState("");

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Security question"
          navigation={navigation}
        />
        <ScrollView
          contentContainerStyle={{ flexGrow: 1, paddingBottom: "35%" }}
        >
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <Input
                label="Password"
                placeholder="*******"
                secureTextEntry={true}
                type="password"
                contStyle={{ marginBottom: 16 }}
                labelStyle={{ fontFamily: fonts.poppinsSemibold }}
              />
              <TouchableOpacity
              style={{width: '100%'}}
                onPress={() => {
                  setShowCountries(true);
                }}
              >
                <Input
                  value={question}
                  label="Question"
                  placeholder="What your pet name?"
                  inputStyle={{ width: "65%", color: "#161817" }}
                  contStyle={{ marginBottom: 16 }}
                  editable={false}
                  rightIcon={
                    <View
                      style={{
                        //   backgroundColor: "red",
                        width: "15%",
                        height: "100%",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <SvgXml xml={svg.dropDown} />
                    </View>
                  }
                />
              </TouchableOpacity>
              <Input
                label="Answer"
                placeholder="Bingo"
                labelStyle={{ fontFamily: fonts.poppinsSemibold }}
              />
            </View>
            <View style={{ width: "80%", marginTop: 32 }}>
              <Button btnText="Continue" onPress={() => navigation.pop()} />
            </View>
          </View>
        </ScrollView>
        <BottomSheet
          isVisible={showCountries}
          showBackArrow={false}
          backspaceText="Question"
          onClose={() => setShowCountries(false)}
          modalContentStyle={{ height: "45%" }}
          extraModalStyle={{ height: "43%" }}
          components={
            <SecurityQuestionSelect
              // onActiveCountryChange={handleActiveCountry}
              // onActiveFlag={handleActiveFlag}
              setQuestion={setQuestion}
              onPress={() => {
                setShowCountries(false);
              }}
            />
          }
        />
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    marginTop: -16,
    alignItems: "center",
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    // height: 284,
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent:"center",
    alignItems: "center",
    padding: 24,
  },
});
