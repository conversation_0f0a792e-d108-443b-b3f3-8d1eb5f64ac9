import React, { useEffect, useRef, useState } from "react";
import { View, StyleSheet, Dimensions, ScrollView, Text } from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import Button from "../../components/Button";
import P from "../../components/P";
import Keyboard from "../../components/Keyboard";

const { width, height } = Dimensions.get("window");
const baseHeight = 802;
export default function SecurtityChangeTransactionPin({ navigation }) {
  const [choose, setChoose] = useState(0);
  const [error, setError] = useState(false); // State to track errors
  const [errorMessage, setErrorMessage] = useState(""); // State to store error messages
  const [inputValue, setInputValue] = useState("0");
  const [fields, setFields] = useState(["", "", "", ""]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [showDots, setShowDots] = useState([false, false, false, false]);
  const refs = [useRef(), useRef(), useRef(), useRef()];
  const [newPin, setNewPin] = useState(null);

  const handleKeyPress = (key) => {
    if (key === "←") {
      if (activeIndex > 0 || fields[activeIndex] !== "") {
        handleChangeText(activeIndex, "");
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[activeIndex] = false;
          return updatedDots;
        });
        if (activeIndex > 0) {
          setActiveIndex(activeIndex - 1);
        }
      }
    } else if (key === "Enter") {
    } else {
      handleChangeText(activeIndex, key);
      if (activeIndex < 3) {
        setActiveIndex(activeIndex + 1);
      }
    }
  };
  const handleChangeText = (index, text) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;

      if (text !== "") {
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[index] = false;
          return updatedDots;
        });

        setTimeout(() => {
          setShowDots((prevShowDots) => {
            const updatedDots = [...prevShowDots];
            updatedDots[index] = true;
            return updatedDots;
          });
        }, 500);
      }
      return updatedFields;
    });
  };

  useEffect(() => {
    if (fields.every((field) => field !== "")) {
      setNewPin(fields.join(""));
    }
  }, [fields]);

  const validatePin = () => {
    if (fields.some((field) => field === "")) {
      setErrorMessage("Please fill in all PIN fields.");
      setError(true);
      return false;
    }
    if (fields.join("").length !== 4) {
      setErrorMessage("PIN must be 4 digits.");
      setError(true);
      return false;
    }
    setError(false);
    return true;
  };

  const handleSubmit = () => {
    if (validatePin()) {
      navigation.navigate("ConfirmNewPin", { newPin });
    }
  };

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Change transaction PIN"
          navigation={navigation}
        />
        <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
          <View style={styles.contentBody}>
            <View style={styles.inputCardWrap}>
              <P
                style={{
                  color: colors.gray,
                  textAlign: "center",
                  marginBottom: 16,
                  fontSize: 12,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Enter New PIN
              </P>

              <View style={styles.con}>
                {refs.map((ref, index) => (
                  <View
                    key={index}
                    style={[
                      styles.pinInput,
                      {
                        marginRight: index === refs.length - 1 ? 0 : 16,
                        borderColor:
                          activeIndex === index
                            ? colors.primary
                            : error && fields[index] === ""
                            ? colors.red
                            : "#E6E5E5",
                      },
                    ]}
                  >
                    <View style={styles.pinView}>
                      {showDots[index] ? (
                        <View style={styles.dot} />
                      ) : (
                        <Text style={styles.pinText}>{fields[index]}</Text>
                      )}
                    </View>
                  </View>
                ))}
              </View>

              {error && <Text style={styles.errorText}>{errorMessage}</Text>}
            </View>

            <View style={{ width: "90%" }}>
              <Keyboard onKeyPress={handleKeyPress} />
            </View>
            <View style={{ width: "80%", marginTop: 32 }}>
              <Button btnText="Enter PIN" onPress={handleSubmit} />
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    marginTop: -16,
    alignItems: "center",
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
    paddingTop: 24,
    backgroundColor: "#fff",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: (166 / baseHeight) * height,
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    width: 48,
    height: 48,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 18,
  },
  pinView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  pinText: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
  },
  dot: {
    width: 16,
    height: 16,
    backgroundColor: "#000",
    borderRadius: 12,
  },
  con: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: "75%",
    marginBottom: 16,
  },
  errorText: {
    color: colors.red,
    textAlign: "center",
    fontSize: 12,
    marginBottom: 16,
    fontFamily: fonts.poppinsRegular,
  },
});
