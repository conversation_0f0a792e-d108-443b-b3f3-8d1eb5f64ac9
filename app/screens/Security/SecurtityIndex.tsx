import React, { useState, useEffect, useCallback, useRef, useContext } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Platform,
  Text,
  Image,
  ImageBackground,
  BackHandler,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { colors } from "../../config/colors";
import CustomSwitch from "../../components/CustomSwitch";
import { Disable2fa, GetUserDetails } from "../../RequestHandlers/User";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useFocusEffect } from "@react-navigation/native";
import FaceId from "./FaceId";
import Keyboard from "../../components/Keyboard";
import Button from "../../components/Button";
import { ValidatePin } from "../../RequestHandlers/User";
import * as LocalAuthentication from "expo-local-authentication";
import { useToast } from "../../context/ToastContext";
import { encryptPIN } from "../../Utils/encrypt";
import * as SecureStore from "expo-secure-store";
import EnableBiomatricComponent from "../../components/EnableBiomatricComponent";
import { CredentailsContext } from "../../RequestHandlers/CredentailsContext";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";

const { width, height } = Dimensions.get("window");
const baseHeight = 802;
export default function SecurityIndex({ navigation }) {
  const [isSwitchEnabled, setIsSwitchEnabled] = useState(false);
  const [is2faEnabled, setIs2faEnabled] = useState(false);
  const [enterPin, setEnterPin] = useState(false);
  const [isPinVerified, setIsPinVerified] = useState(false); // Track if the PIN is verified
  const [pendingToggleState, setPendingToggleState] = useState(null); // Track the desired state
  const [fields, setFields] = useState(["", "", "", ""]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [showDots, setShowDots] = useState([false, false, false, false]);
  const [error, setError] = useState("");
  const refs = [useRef(), useRef(), useRef(), useRef()];
  const [loading, setLoading] = useState(false);
  const [bioMatricSupported, setIsBioMatricSupported] = useState(true);
  const [securityLevel, setSecurityLevel] = useState("Medium");
  const { handleToast } = useToast();
  const [pin, setPin] = useState(null);
  const [toggleState, setToggleState] = useState(false);
  const [showEnabler, setShowEnabler] = useState(false);
  const [id, setId] = useState("")
  const { storedCredentails } = useContext(CredentailsContext)

  const userDetails = async () => {
    try {
      const details = await withApiErrorToast(GetUserDetails(), handleToast);
      if (details?.email) {
        setIs2faEnabled(details?._2faEnabled);
        const res = await AsyncStorage.getItem(
          `fingerPrintStatus${details.id}`
        );
        setId(details?.id)
        setIsSwitchEnabled(res !== null);
      }
      if (details?._2faEnabled === true) {
        setSecurityLevel("Strong");
      } else {
        setSecurityLevel("Medium");
      }
    } catch (error) { }
  };
  const handleToggle = (state) => {
    if (!state) {
      setEnterPin(true);
      setPendingToggleState(state);

    } else {
      if (storedCredentails && storedCredentails.user && storedCredentails.user.hasPin === false) {
        navigation.navigate("AccountVerification3")
      } else {
        setShowEnabler(true)
        setPendingToggleState(state);
      }
    }
  };
  const disabled2fa = async () => {
    try {
      const res = await withApiErrorToast(Disable2fa(), handleToast);
      if (res.error) {
        handleToast(res.message, "error");
      } else {
        handleToast(res.message, "success");
        navigation.navigate("SecurityIndex");
      }
    } catch (error) { }
  };
  const on2faToggle = (state) => {
    if (state === true) {
      if (storedCredentails && storedCredentails.user && storedCredentails.user.hasPin === false) {
        navigation.navigate("AccountVerification3")
      } else {
        navigation.navigate("TwofactorAuthScreen1", { type: "enable-2fa" });
      }
    } else {
      navigation.navigate("VerifyActivityScreen", {
        activityType: "disable-2fa",
        ActivityFunction: disabled2fa,
      });
      setIs2faEnabled(is2faEnabled);
    }
  };
  useFocusEffect(
    useCallback(() => {
      userDetails();
    }, [])
  );
  useEffect(() => {
    if (isPinVerified) {
      handleFingerprintStatus(pendingToggleState);
      setIsPinVerified(false);
    }
  }, [isPinVerified]);

  const handleFingerprintStatus = async (state) => {
    try {
      const details = await GetUserDetails();
      const cookieKey = `fingerPrintStatus${details.id}`;
      if (state) {
      } else {
        await AsyncStorage.removeItem(cookieKey);
        await SecureStore.deleteItemAsync(`privateKey${details.id}`);
        handleToast("Biomatric disabled", "success");
      }
      userDetails();
    } catch (error) { }
  };

  const handleKeyPress = (key) => {
    if (key === "←") {
      if (activeIndex > 0 || fields[activeIndex] !== "") {
        handleChangeText(activeIndex, "");
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[activeIndex] = false;
          return updatedDots;
        });
        if (activeIndex > 0) {
          setActiveIndex(activeIndex - 1);
        }
      }
    } else if (key === "Enter") {
      // Trigger PIN submission when Enter is pressed
      // handleSubmit();
    } else {
      handleChangeText(activeIndex, key);
      if (activeIndex < 3) {
        setActiveIndex(activeIndex + 1);
      }
    }
  };
  const handleChangeText = (index, text) => {
    setError("");
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;
      if (text !== "") {
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[index] = false;
          return updatedDots;
        });

        setTimeout(() => {
          setShowDots((prevShowDots) => {
            const updatedDots = [...prevShowDots];
            updatedDots[index] = true;
            return updatedDots;
          });
        }, 500);
      }
      return updatedFields;
    });
  };

  const validatePIN = async (pin) => {
    try {
      const body = {
        pin: await encryptPIN(String(pin)),
        activityType:
          toggleState === true ? "register-biometrics" : "disable-biometrics",
      };
      const validate = await ValidatePin(body);
      if (validate.status === true) {
        setLoading(false);
        setError("");
        setIsPinVerified(true);
        setEnterPin(false);
        setFields(["", "", "", ""])
      } else {
        setLoading(false);
        handleToast(validate.message, "error");
        setError("Incorrect PIN");
      }
    } catch (error) { }
  };
  const storeKey = async () => {
    const privateKey = await encryptPIN(String(pin));
    await SecureStore.setItemAsync(`privateKey${id}`, privateKey);
    handleToast("Biomatric enabled", "success");
  }

  const persistBiomatric = () => {
    AsyncStorage.setItem(`fingerPrintStatus${id}`, "true")
      .then(() => {
        // @ts-ignore
        setStoredFingerPrintStatus("true");
        storeKey()
      })
      .catch((err) => { });
  };

  const handleSubmit = () => {
    setLoading(true);
    const enteredPin = fields.join("");
    if (!enteredPin) {
      setError("Please enter your PIN");
    } else {
      validatePIN(fields.join(""));
      setPin(fields.join(""));
    }
  };
  useEffect(() => {
    async () => {
      async () => {
        const isCompatible = await LocalAuthentication.hasHardwareAsync();
        if (!isCompatible) {
          setIsBioMatricSupported(false);
          throw new Error("Your device isn't compatible.");
        }
        const isEnrolled = await LocalAuthentication.isEnrolledAsync();
        if (!isEnrolled) {
          setIsBioMatricSupported(false);
          throw new Error("No Faces / Fingers found.");
        }
      };
    };
  }, []);

  useEffect(() => {
    userDetails();
  }, [is2faEnabled]);




  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Security"
          navigation={navigation}
          goToScreen="Settings"
        />
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.contentBody}>
            <View style={styles.SubHCont}>
              <SvgXml xml={svg.megaLock} />
              <View style={{ width: "70%" }}>
                <View
                  style={{ flexDirection: "row", alignItems: "center", gap: 8 }}
                >
                  <P style={{ fontSize: 12 }}>Security level</P>
                  <View
                    style={{
                      paddingHorizontal: 8,
                      paddingVertical: 2,
                      borderRadius: 100,
                      flexDirection: "row",
                      alignItems: "center",
                      gap: 4,
                      backgroundColor:
                        securityLevel === "Medium"
                          ? colors.lowOpOrange
                          : colors.lowOpSuccess,
                    }}
                  >
                    <SvgXml
                      xml={
                        securityLevel === "Medium"
                          ? svg.signalMedium
                          : svg.signalFull
                      }
                    />
                    <P
                      style={{
                        fontSize: 10,
                        color:
                          securityLevel === "Medium"
                            ? colors.orange
                            : colors.green,
                      }}
                    >
                      {securityLevel}
                    </P>
                  </View>
                </View>
                <P
                  style={{
                    fontSize: 12,
                    marginTop: 2,
                    fontFamily: fonts.poppinsRegular,
                    color: colors.dGray,
                  }}
                >
                  Enable multiple authentication method to enhance your security
                </P>
              </View>
            </View>
            <View style={[styles.detailWrap, { marginBottom: 16 }]}>
              <View style={styles.listItem}>
                <View>
                  <P style={{ fontSize: 12 }}>Password & OTP</P>
                  <P style={styles.subText}>Login with your Password and OTP</P>
                </View>
                <View
                  style={{
                    paddingHorizontal: 8,
                    paddingVertical: 2,
                    borderRadius: 100,
                    flexDirection: "row",
                    alignItems: "center",
                    gap: 4,
                    backgroundColor: colors.lowOpSuccess,
                  }}
                >
                  <P
                    style={{
                      fontSize: 10,
                      color: colors.green,
                    }}
                  >
                    Default
                  </P>
                </View>
              </View>
              <View
                style={[
                  styles.listItem,
                  { marginBottom: 0, borderBottomWidth: 0, paddingBottom: 0 },
                ]}
              >
                <View>
                  <P style={{ fontSize: 12 }}>2FA authenticator</P>
                  <P style={styles.subText}>Time-based one-time code</P>
                </View>
                <CustomSwitch onToggle={on2faToggle} isOn={is2faEnabled} />
              </View>
            </View>
            <View style={styles.detailWrap}>
              <TouchableOpacity
                onPress={() => navigation.navigate("SecurtityChangePassword")}
              >
                <View style={styles.listItem}>
                  <View>
                    <P style={{ fontSize: 12 }}>Password</P>
                    <P style={styles.subText}>Manage your login password</P>
                  </View>
                  <SvgXml xml={svg.arrowBlack} />
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  if (storedCredentails && storedCredentails.user && storedCredentails.user.hasPin === false) {
                    navigation.navigate("AccountVerification3")
                  } else {
                    navigation.navigate("SecurtityEmailVerification")
                  }
                }}
              >
                <View style={styles.listItem}>
                  <View>
                    <P style={{ fontSize: 12 }}>Transaction PIN</P>
                    <P style={styles.subText}>Change your transaction PIN</P>
                  </View>
                  <SvgXml xml={svg.arrowBlack} />
                </View>
              </TouchableOpacity>
              {bioMatricSupported && (
                <View
                  style={[
                    styles.listItem,
                    { marginBottom: 0, borderBottomWidth: 0, paddingBottom: 0 },
                  ]}
                >
                  <View>
                    <P style={{ fontSize: 12 }}>
                      {Platform.OS === "ios" ? "Face ID" : "Fingerprint"}
                    </P>
                    <P style={styles.subText}>
                      Manage your{" "}
                      {Platform.OS === "ios" ? "Face ID" : "Fingerprint"}
                    </P>
                  </View>
                  <CustomSwitch
                    onToggle={handleToggle}
                    isOn={isSwitchEnabled}
                  />
                </View>
              )}
            </View>
            <View
              style={{
                width: "85%",
                alignItems: "center",
                justifyContent: "center",
                alignSelf: "center",
              }}
            >
              <ImageBackground
                source={require("../../../app/assets/securityBanner.png")}
                resizeMode="cover"
                borderRadius={12}
                style={{
                  width: "100%",
                  height: 106,
                  alignSelf: "center",
                  marginTop: 48,
                  alignItems: "center",
                  flexDirection: "row",
                  justifyContent: "space-between",
                  gap: 8,
                }}
              >
                <P
                  style={{
                    marginLeft: 24,
                    width: "60%",
                    color: colors.white,
                    fontFamily: fonts.poppinsSemibold,
                  }}
                >
                  Your account security is our priority
                </P>
                <SvgXml xml={svg.plusGuild} style={{ marginRight: 16 }} />
              </ImageBackground>
            </View>
          </View>
        </ScrollView>
      </Div>
      {showEnabler && (
        <EnableBiomatricComponent
          visible={showEnabler}
          onClose={() => {
            setShowEnabler(false);
          }}
          secondaryFunction={() => {
            persistBiomatric();
            setTimeout(() => {
              setShowEnabler(false)
            }, 2000);
          }}
        />
      )}
      {enterPin && (
        <>
          <View
            style={{
              width: "100%",
              height: (100 * height) / 100,
              backgroundColor: "rgba(247, 244, 255, 1)",
            }}
          >
            <View style={styles.nav}>
              <TouchableOpacity
                onPress={() => {
                  userDetails();
                  setEnterPin(false);
                }}
              >
                <SvgXml xml={svg.goBackIcon} style={{ marginRight: 12 }} />
              </TouchableOpacity>
              <P style={styles.navText}>Face ID</P>
            </View>
            <View style={styles.inputCardWrap}>
              <P style={styles.pinPromptText}>Enter transaction PIN</P>
              <View style={styles.con}>
                {refs.map((ref, index) => (
                  <View
                    key={index}
                    style={[
                      styles.pinInput,
                      {
                        marginRight: index === refs.length - 1 ? 0 : 16,
                        borderColor: error
                          ? colors.red
                          : activeIndex === index
                            ? colors.primary
                            : "#E6E5E5",
                      },
                    ]}
                  >
                    <View style={styles.pinView}>
                      {showDots[index] ? (
                        <View style={styles.dot} />
                      ) : (
                        <Text style={styles.pinText}>{fields[index]}</Text>
                      )}
                    </View>
                  </View>
                ))}
              </View>
              {error && <Text style={styles.errorText}>{error}</Text>}
            </View>
            <View
              style={{
                alignSelf: "center",
                width: "100%",
                alignItems: "center",
              }}
            >
              <View style={{ width: "90%" }}>
                <Keyboard onKeyPress={handleKeyPress} />
              </View>
              <View
                style={{ width: "80%", marginTop: (32 / baseHeight) * height }}
              >
                <Button
                  btnText="Enter PIN"
                  onPress={handleSubmit}
                  loading={loading}
                />
              </View>
            </View>
          </View>
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    marginTop: -16,
  },
  detailWrap: {
    width: "85%",
    alignSelf: "center",
    paddingBottom: 32,
    paddingTop: 32,
    backgroundColor: "white",
    borderRadius: 12,
    paddingLeft: 24,
    paddingRight: 24,
  },
  listItem: {
    flexDirection: "row",
    width: "100%",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    paddingBottom: 16,
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
    paddingTop: 24,
    backgroundColor: "#fff",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 24,
    paddingBottom: 24,
    marginBottom: (166 / baseHeight) * height,
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    width: 48,
    height: 48,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 18,
  },
  pinView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  pinText: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
  },
  dot: {
    width: 16,
    height: 16,
    backgroundColor: "#000",
    borderRadius: 12,
  },
  con: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: "75%",
    // marginBottom: 32,
  },
  pinPromptText: {
    color: colors.gray,
    textAlign: "center",
    marginBottom: (16 / baseHeight) * height,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
  },
  errorText: {
    color: colors.red,
    fontSize: 12,
    marginTop: 4,
    fontFamily: fonts.poppinsRegular,
  },
  nav: {
    width: "90%",
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "center",
    // justifyContent: "space-between",
    paddingTop: 2,
    marginTop: 24,
  },
  navText: {
    // fontSize: (4.5 * width) / 100,
    color: "#000",
    fontFamily: fonts.poppinsMedium,
  },
  subText: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.dGray,
  },
  SubHCont: {
    width: "90%",
    alignSelf: "center",
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
});
