import React, { useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  ScrollView,
  Keyboard,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import Button from "../../components/Button";
import Input from "../../components/Input";
import { Formik } from "formik";
import * as yup from "yup";
import P from "../../components/P";
import { UpdatePassword } from "../../RequestHandlers/User";
import { useToast } from "../../context/ToastContext";
import PasswordValidation from "../../components/PasswordValidation";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";

const { width, height } = Dimensions.get("window");
export default function SecurtityChangePassword({ navigation }) {
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();
  const pwdChangeScheme = yup.object().shape({
    oldPassword: yup.string().required("Your current password is required"),
    password: yup
      .string()
      .required("Password is required")
      .matches(
        /^(?=.*[0-9])(?=.*[*?!#$%&@^()\-_=+\\|[\]{};:/?.>])(?=.*[a-z])(?=.*[A-Z])[a-zA-Z0-9*?!#$%&@^()\-_=+\\|[\]{};:/?.>]{8,}$/,
        "Password must contain at least 8 characters, including letters, numbers, and special characters, with at least one uppercase letter"
      ),
    confirmpassword: yup
      .string()
      .required("Confirm password is required")
      .oneOf([yup.ref("password"), null], "Passwords must match"),
  });
  return (
    <>
      <View style={styles.body}>
        <Div>
          <AuthenticationHedear
            text="Change password"
            navigation={navigation}
          />
          <ScrollView
            contentContainerStyle={{ flexGrow: 1, paddingBottom: "10%" }}
          >
            <Formik
              // innerRef={ref}
              // enableReinitialize={true}
              initialValues={{
                oldPassword: "",
                password: "",
                confirmpassword: "",
              }}
              validationSchema={pwdChangeScheme}
              onSubmit={async (values, actions) => {
                Keyboard.dismiss();
                setLoading(true);
                try {
                  const updatePwd = await withApiErrorToast(UpdatePassword(values), handleToast);
                  if (updatePwd?.message?.includes("Successfully")) {
                    setLoading(false);
                    handleToast(updatePwd.message, "success");
                    setTimeout(() => {
                      navigation.reset({
                        index: 0,
                        routes: [{ name: "SecurityIndex" }],
                      });
                    }, 500);
                  } else {
                    setLoading(false);
                    handleToast(updatePwd.message, "error");
                  }
                } catch (error) {}finally{
                  setLoading(false)
                }
              }}
            >
              {(formikProps) => (
                <View style={styles.contentBody}>
                  <View style={styles.detailWrap}>
                    <Input
                      label="Current password"
                      placeholder="*******"
                      secureTextEntry={true}
                      type="password"
                      labelStyle={{ fontFamily: fonts.poppinsSemibold }}
                      onChangeText={formikProps.handleChange("oldPassword")}
                      value={formikProps.values.oldPassword}
                      onBlur={formikProps.handleBlur("oldPassword")}
                      error={
                        formikProps.errors.oldPassword &&
                        formikProps.touched.oldPassword
                      }
                    />
                    {formikProps.errors.oldPassword &&
                      formikProps.touched.oldPassword && (
                        <P style={styles.errorText}>
                          {formikProps.errors.oldPassword}
                        </P>
                      )}
                    <Input
                      label="New password"
                      placeholder="*******"
                      secureTextEntry={true}
                      type="password"
                      contStyle={{ marginTop: 16 }}
                      labelStyle={{ fontFamily: fonts.poppinsSemibold }}
                      onChangeText={formikProps.handleChange("password")}
                      value={formikProps.values.password}
                      onBlur={formikProps.handleBlur("password")}
                      error={
                        formikProps.errors.password &&
                        formikProps.touched.password
                      }
                      showPasswordStrength={true}
                    />
                    <PasswordValidation password={formikProps.values.password} />
                    {formikProps.errors.password &&
                      formikProps.touched.password && (
                        <P style={styles.errorText}>
                          {formikProps.errors.password}
                        </P>
                      )}
                    <Input
                      label="Confirm new password"
                      placeholder="*******"
                      secureTextEntry={true}
                      contStyle={{ marginTop: 16 }}
                      type="password"
                      labelStyle={{ fontFamily: fonts.poppinsSemibold }}
                      onChangeText={formikProps.handleChange("confirmpassword")}
                      value={formikProps.values.confirmpassword}
                      onBlur={formikProps.handleBlur("confirmpassword")}
                      error={
                        formikProps.errors.confirmpassword &&
                        formikProps.touched.confirmpassword
                      }
                    />
                    {formikProps.errors.confirmpassword &&
                      formikProps.touched.confirmpassword && (
                        <P style={styles.errorText}>
                          {formikProps.errors.confirmpassword}
                        </P>
                      )}
                  </View>
                  <View style={{ width: "80%", marginTop: 32 }}>
                    <Button
                      loading={loading}
                      btnText="Change password"
                      onPress={formikProps.handleSubmit}
                    />
                  </View>
                </View>
              )}
            </Formik>
          </ScrollView>
        </Div>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    marginTop: -16,
    alignItems: "center",
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    // height: 284,
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent:"center",
    alignItems: "center",
    paddingLeft: 24,
    paddingRight: 24,
    paddingTop: 24,
    paddingBottom: 24,
  },
  errorText: {
    width: "100%",
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
});
