import React, { useEffect, useRef, useState } from "react";
import { View, StyleSheet, Dimensions, ScrollView, Text } from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import Button from "../../components/Button";
import P from "../../components/P";
import Keyboard from "../../components/Keyboard";
import { GetUserDetails, ResetPin } from "../../RequestHandlers/User";
import { encryptPIN } from "../../Utils/encrypt";
import { useToast } from "../../context/ToastContext";
import * as SecureStore from "expo-secure-store";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
const { width, height } = Dimensions.get("window");
const baseHeight = 802;

export default function ConfirmNewPin({ navigation, route }) {
  const { newPin } = route.params;
  const { handleToast } = useToast();
  const [fields, setFields] = useState(["", "", "", ""]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [showDots, setShowDots] = useState([false, false, false, false]);
  const refs = [useRef(), useRef(), useRef(), useRef()];
  const [confirmPin, setConfirmPin] = useState("");
  const [error, setError] = useState(false); // State to track errors
  const [errorMessage, setErrorMessage] = useState(""); // Error message state
  const [loading, setLoading] = useState(false);
  const [encryptedPin, setEncryptedPin] = useState("");
  const [id, setId] = useState("");

  const handleKeyPress = (key) => {
    if (key === "←") {
      if (activeIndex > 0 || fields[activeIndex] !== "") {
        handleChangeText(activeIndex, "");
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[activeIndex] = false;
          return updatedDots;
        });
        if (activeIndex > 0) {
          setActiveIndex(activeIndex - 1);
        }
      }
    } else if (key === "Enter") {
      // Handle enter key press
    } else {
      handleChangeText(activeIndex, key);
      if (activeIndex < 3) {
        setActiveIndex(activeIndex + 1);
      }
    }
  };

  const handleChangeText = (index, text) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;

      if (text !== "") {
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[index] = false;
          return updatedDots;
        });

        setTimeout(() => {
          setShowDots((prevShowDots) => {
            const updatedDots = [...prevShowDots];
            updatedDots[index] = true;
            return updatedDots;
          });
        }, 500);
      }

      return updatedFields;
    });
  };

  const encp = async (pin) => {
    try {
      const res = await encryptPIN(pin);
      setEncryptedPin(res);
      return res;
    } catch (error) {}
  };
  useEffect(() => {
    if (fields.every((field) => field !== "")) {
      setConfirmPin(fields.join(""));
      encp(String(fields?.join("")));
    }
  }, [fields]);

  const handleSubmit = async () => {
    // Validate that confirmPin matches newPin
    if (fields.some((field) => field === "")) {
      setErrorMessage("Please fill in all PIN fields.");
      setError(true);
    } else if (confirmPin !== newPin) {
      setErrorMessage("PINs do not match. Please try again.");
      setError(true);
      // Optionally, reset the fields
      setFields(["", "", "", ""]);
      setActiveIndex(0);
      setShowDots([false, false, false, false]);
    } else {
      setError(false);
      setLoading(true);
      try {
        const body = {
          newPin: await encp(confirmPin),
        };
        const validate = await withApiErrorToast(ResetPin(body), handleToast);
        if (validate.status === true) {
          setLoading(false);
          handleToast(validate.message, "success");
          navigation.navigate("SecurityIndex");
          const privateKey = await encryptPIN(String(fields.join("")));
          await SecureStore.setItemAsync(`privateKey${id}`, privateKey);
          // @ts-ignore
          if (setStoredPrivateKey) setStoredPrivateKey(privateKey);
        } else {
          setLoading(false);
          handleToast(validate.message, "error");
        }
      } catch (error) {}finally{
        setLoading(false)
      }
    }
  };
  const getUserId = async () => {
    try {
      const res = await GetUserDetails();
      if (res.id) {
        setId(res.id);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getUserId();
  }, []);

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Change transaction PIN"
          navigation={navigation}
        />
        <ScrollView
          contentContainerStyle={{ flexGrow: 1, paddingBottom: "35%" }}
        >
          <View style={styles.contentBody}>
            <View style={styles.inputCardWrap}>
              <P
                style={{
                  color: colors.gray,
                  textAlign: "center",
                  marginBottom: 16,
                  fontSize: 12,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Confirm New PIN
              </P>

              <View style={styles.con}>
                {refs.map((ref, index) => (
                  <View
                    key={index}
                    style={[
                      styles.pinInput,
                      {
                        marginRight: index === refs.length - 1 ? 0 : 16,
                        borderColor:
                          activeIndex === index
                            ? colors.primary
                            : error && fields[index] === ""
                            ? "red"
                            : "#E6E5E5",
                      },
                    ]}
                  >
                    <View style={styles.pinView}>
                      {showDots[index] ? (
                        <View style={styles.dot} />
                      ) : (
                        <Text style={styles.pinText}>{fields[index]}</Text>
                      )}
                    </View>
                  </View>
                ))}
              </View>
              {error && <Text style={styles.errorText}>{errorMessage}</Text>}
            </View>

            <View style={{ width: "90%" }}>
              <Keyboard onKeyPress={handleKeyPress} />
            </View>
            <View style={{ width: "80%", marginTop: 32 }}>
              <Button
                btnText="Enter PIN"
                onPress={handleSubmit}
                loading={loading}
              />
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    marginTop: -16,
    alignItems: "center",
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
    paddingTop: 24,
    backgroundColor: "#fff",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: (166 / baseHeight) * height,
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    width: 48,
    height: 48,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 18,
  },
  pinView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  pinText: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
  },
  dot: {
    width: 16,
    height: 16,
    backgroundColor: "#000",
    borderRadius: 12,
  },
  con: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: "75%",
    marginBottom: 16,
  },
  errorText: {
    color: colors.red,
    fontSize: 12,
    textAlign: "center",
    marginBottom: 16,
    fontFamily: fonts.poppinsRegular,
  },
});
