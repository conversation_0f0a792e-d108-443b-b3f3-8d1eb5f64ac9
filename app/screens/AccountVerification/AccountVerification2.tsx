import React, { useState, useEffect, useContext } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  Keyboard,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Input from "../../components/Input";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import * as Clipboard from "expo-clipboard";
import Link from "../../components/Link";
import { Formik } from "formik";
import * as yup from "yup";
import { CheckUsername } from "../../RequestHandlers/Authentication";
import { UpdateUser } from "../../RequestHandlers/User";
import { useToast } from "../../context/ToastContext";
import { updateCredentials } from "../../Utils/credentialsUtils";
import { CredentailsContext } from "../../RequestHandlers/CredentailsContext";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";

const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function AccountVerification2({ navigation }) {
  const { handleToast } = useToast();
  const { setStoredCredentails } = useContext(CredentailsContext);
  const [userName, setUserName] = useState("");
  const [loading, setLoading] = useState(false);
  const usernameScheme = yup.object().shape({
    username: yup.string().required("Username is required"),
  });

  const updateUserName = async (body) => {
    try {
      const updateUser = await withApiErrorToast(UpdateUser(body), handleToast);
      if (updateUser.message === "User Updated Successfully!") {
        // Update credentials in storage and context
        await updateCredentials(setStoredCredentails, {
          username: body.username,
        });
        navigation.navigate("AccountVerification3");
        setLoading(false);
        handleToast("Username updated", "success");
      } else {
        setLoading(false);
        handleToast(updateUser.message, "error");
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  // Separate submit function for AuthenticationHeader that navigates to BottomTabNavigator
  const headerSubmitFunction = async (formikProps) => {
    // Validate the form first
    const errors = await formikProps.validateForm();
    if (Object.keys(errors).length > 0) {
      formikProps.setTouched({
        username: true,
      });
      return;
    }

    Keyboard.dismiss();
    setLoading(true);
    const trimmedUsername = formikProps.values.username.trim();
    if (/\s/.test(trimmedUsername)) {
      console.log("Username contains white spaces!");
      formikProps.setFieldError("username", "Username cannot contain spaces");
      setLoading(false);
      return;
    }
    try {
      const checkUserName = await withApiErrorToast(
        CheckUsername({
          username: trimmedUsername,
        }),
        handleToast
      );
      if (checkUserName.status === true) {
        // Update username and navigate to BottomTabNavigator
        const updateUser = await withApiErrorToast(UpdateUser({ username: trimmedUsername }), handleToast);
        if (updateUser.message === "User Updated Successfully!") {
          // Update credentials in storage and context
          await updateCredentials(setStoredCredentails, {
            username: trimmedUsername,
          });
          navigation.navigate("BottomTabNavigator");
          setLoading(false);
          handleToast("Username updated", "success");
        } else {
          setLoading(false);
          handleToast(updateUser.message, "error");
        }
      } else {
        setLoading(false);
        handleToast(checkUserName.message, "error");
      }
    } catch (error) {
      setLoading(false);
      handleToast("Network error", "error");
    } finally {
      setLoading(false);
    }
  };

  // const [isAccVerified, setAccVerified] = useState(false);
  return (
    <View style={styles.body}>
      <Formik
        initialValues={{
          username: "",
        }}
        validationSchema={usernameScheme}
        onSubmit={async (values, actions) => {
          Keyboard.dismiss();
          setLoading(true);
          const trimmedUsername = values.username.trim();
          if (/\s/.test(trimmedUsername)) {
            console.log("Username contains white spaces!");
            actions.setFieldError("username", "Username cannot contain spaces");
            setLoading(false);
            return;
          }
          try {
            const checkUserName = await withApiErrorToast(
              CheckUsername({
                username: trimmedUsername,
              }),
              handleToast
            );
            if (checkUserName.status === true) {
              // setLoading(false);
              // navigation.navigate("TransactionPinScreen",);
              updateUserName({ username: trimmedUsername });
            } else {
              setLoading(false);
              handleToast(checkUserName.message, "error");
            }
          } catch (error) {
            setLoading(false);
            handleToast("Network error", "error");
          } finally {
            setLoading(false);
          }
          // Proceed with API call if no spaces
        }}
      >
        {(formikProps) => (
          <Div>
            <AuthenticationHedear
              type="KYC"
              text="Account verification"
              navigation={navigation}
              currentStep={2}
              submitFunction={() => headerSubmitFunction(formikProps)}
              loading={loading}
            />
            <ScrollView>
              <View style={styles.contentCard}>
                <View style={styles.section1Wrap}>
                  <H4 style={styles.amt}>Money app username</H4>
                  <H4
                    // @ts-ignore
                    style={[
                      styles.amt,
                      {
                        fontSize: 14,
                        lineHeight: 18,
                        color: colors.dark500,
                        textAlign: "center",
                        fontFamily: fonts.poppinsRegular,
                      },
                    ]}
                  >
                    Enter a username that you like to receive money from friends
                    and family
                  </H4>
                  <View style={styles.section3Wrap}>
                    <Input
                      label="Username"
                      placeholder="John"
                      inputStyle={{ width: "85%" }}
                      // contStyle={{ marginBottom: (16 / baseHeight) * height }}
                      onChangeText={formikProps.handleChange("username")}
                      value={formikProps.values.username}
                      onBlur={formikProps.handleBlur("username")}
                      maxLenght={20}
                      autoCapitalize="none"
                      error={
                        formikProps.errors.username &&
                        formikProps.touched.username
                      }
                    />
                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                      }}
                    >
                      {formikProps.errors.username &&
                      formikProps.touched.username ? (
                        <P style={styles.errorText}>
                          {formikProps.errors.username}
                        </P>
                      ) : (
                        <P>{""}</P>
                      )}
                      <P
                        style={{
                          // position: "absolute",
                          bottom: 0,
                          right: 0,
                          fontSize: 12,
                          fontFamily: fonts.poppinsRegular,
                          marginTop: 4,
                        }}
                      >
                        Max characters 20
                      </P>
                    </View>
                  </View>
                </View>
              </View>

              <View style={styles.btnCont}>
                <Button
                  btnText={"Continue"}
                  onPress={formikProps.handleSubmit}
                  loading={loading}
                />
                {/* <Link
                  style={{
                    textAlign: "center",
                    marginTop: (16 / baseHeight) * height,
                  }}
                  onPress={() => navigation.navigate("Home")}
                >
                  Submit & return home
                </Link> */}
              </View>
            </ScrollView>
          </Div>
        )}
      </Formik>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentCard: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 16,
    paddingRight: 16,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  holder: {
    fontSize: 12,
    lineHeight: (18 / baseHeight) * height,
    color: colors.gray,
    marginBottom: (4 / baseHeight) * height,
  },
  value: {
    fontSize: 12,
    lineHeight: (18 / baseHeight) * height,
    color: colors.black,
  },
  copyBtn: {
    paddingTop: (4 / baseHeight) * height,
    paddingBottom: (4 / baseHeight) * height,
    padding: (13 / baseWidth) * width,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
    marginBottom: 16,
  },
  amt: {
    fontSize: 20,
    // fontSize: 32,
    // lineHeight: 48,
    fontFamily: fonts.poppinsSemibold,
  },
  amtCur: {
    lineHeight: 24,
    fontFamily: fonts.poppinsMedium,
  },
  statusText: {
    fontSize: 10,
    lineHeight: 16,
    fontFamily: fonts.poppinsRegular,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginTop: (24 / baseHeight) * height,
    // paddingTop: (2.7 * height) / 100,
    borderTopWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  section3Wrap: {
    width: "100%",
    marginTop: (24 / baseHeight) * height,
    borderColor: colors.stroke,
    // paddingLeft: 16,
  },
  progressDesCont: {
    flexDirection: "row",
  },
  progTextHead: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  progTextBody: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
    marginBottom: 64,
    // marginTop: 42,
    // alignItems: 'center',
    // justifyContent: 'center',
  },
  pinInput: {
    width: "40%",
    height: "100%",
    alignItems: "center",
    flexDirection: "row",
    borderRightColor: "#E6E5E5",
    borderRightWidth: 1,
  },
  pinTextInput: {
    fontSize: 14,
    textAlign: "center",
    color: "#161817",
    fontFamily: fonts.poppinsMedium,
    marginLeft: 16,
    marginRight: 8,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
});
