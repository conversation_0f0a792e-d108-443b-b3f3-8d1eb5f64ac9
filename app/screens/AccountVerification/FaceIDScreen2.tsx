import React, { useState, useEffect, useContext } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import { GetUserDetails } from "../../RequestHandlers/User";
import { CredentailsContext } from "../../RequestHandlers/CredentailsContext";
import AsyncStorage from "@react-native-async-storage/async-storage";
import VLoader from "../../components/VLoader";
import { useToast } from "../../context/ToastContext";
import Constants from "expo-constants";
import SmileIDVerification, {
  VerificationType,
} from "../../components/SmileIDVerification";
import Loader from "../../components/ActivityIndicator";
import NoteComponent2 from "../../components/NoteComponent2";
import Button from "../../components/Button";

// Define the structure of the result data to match what SmileIDVerification returns
interface SmileIDResult {
  type?: string;
  images?: string[];
  data?: any;
  rawData?: any;
  allImages?: string[];
}

const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
const STAGING_BASE_URL = Constants.expoConfig.extra.STAGING_BASE_URL;
const PROD_BASE_URL = Constants.expoConfig.extra.PROD_BASE_URL;
export default function FaceIdScreen2({ navigation, route }) {
  const [uniqueId, setuniqueId] = useState("");
  const { data } = route?.params || {};
  const [step, setStep] = useState(1);
  const [loader, setLoader] = useState(false);
  const { storedCredentails } = useContext(CredentailsContext);
  const [tkn, setTkn] = useState("");
  const [kycFailed, setKyCFialed] = useState(false);
  const [countdown, setCountdown] = useState(10);
  const [isCounting, setIsCounting] = useState(false);
  const [isProd, setIsProd] = useState(true);
  const handleNextStep = () => {
    setStep((prevStep) => prevStep + 1);
  };
  const { handleToast } = useToast();
  // const [isAccVerified, setAccVerified] = useState(false);
  const [base64Image, setBase64Image] = useState<
    { image: string; image_type_id: number | null }[]
  >([]);

  useEffect(() => {
    async () => {
      const res = await AsyncStorage.getItem("uniqueID");
      setuniqueId(res);
    };
  }, []);
  const checkStatus = async () => {
    try {
      const res = await GetUserDetails();
      if (res.verified == "true") {
        navigation.navigate("AllSetScreen");
      } else if (res.verified == "pending") {
        navigation.navigate("AccountVerificationPending");
      } else {
        setKyCFialed(true);
        setCountdown(10);
        setIsCounting(true);
      }
    } catch (error) {
    } finally {
      setLoader(false);
    }
  };

  const tier = 1;
  const idType = "PASSPORT";
  const baseUrl = isProd ? PROD_BASE_URL : STAGING_BASE_URL;
  const verifyBVN = async (details: any) => {
    setLoader(true);
    const myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    myHeaders.append("Authorization", `Bearer ${tkn}`);
    myHeaders.append("x-device-id", uniqueId || "");
    const raw = JSON.stringify({
      id_number: data?.idNum,
      dob: data?.dob,
      phone_number: data?.phone,
      detail: details,
    });
    // const requestSize = new Blob([raw]).size;
    console.log({
      id_number: data?.idNum,
      dob: data?.dob,
      phone_number: data?.phone.replace(/\s/g, ""),
    });
    const requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: raw,
      redirect: "follow",
    };
    // @ts-ignore
    fetch(`${baseUrl}kyc/verify-bvn`, requestOptions)
      .then((response) => {
        console.log("gg", response.status);
        if (response.status === 200) {
          console.log(200);
          checkStatus();
        }
        return response.json();
      })
      .then((result) => {
        if (result.error) {
          handleToast(result.message, "error");
          setKyCFialed(true);
          setCountdown(10);
          setIsCounting(true);
          setLoader(false);
        }
        // if (result.status === true) {
        //   checkStatus();
        // } else {
        //   handleToast(result.message, "error");
        //   setKyCFialed(true);
        //   setCountdown(10);
        //   setIsCounting(true);
        //   setLoader(false);
        // }
      })
      .catch((error) => {
        handleToast(error.message, "error");
        setKyCFialed(true);
        setCountdown(10);
        setIsCounting(true);
      })
      .finally(() => {
        setLoader(false);
      });
  };

  const verifyPassport = async (details: any) => {
    const myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    myHeaders.append("Authorization", `Bearer ${tkn}`);
    myHeaders.append("x-device-id", uniqueId || "");
    const raw = JSON.stringify({
      detail: details,
      id_info: {
        country: data?.alphaCode,
        id_type: idType,
      },
      tier: tier,
      document_id: data?.idNum,
      dob: data?.dob,
      phone_number: data?.phone.replace(/\s/g, ""),
    });
    // Calculate request size for logging purposes
    // const requestSize = new Blob([raw]).size;
    // console.log(requestSize);
    const requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: raw,
      redirect: "follow",
    };
    // @ts-ignore
    fetch(`${baseUrl}kyc/verify-docs`, requestOptions)
      .then((response) => {
        if (response.status === 200) {
          checkStatus();
        }
        return response.json();
      })
      .then((result) => {
        if (result.error) {
          handleToast(result.message, "error");
          setKyCFialed(true);
          setCountdown(10);
          setIsCounting(true);
          setLoader(false);
        }
        // console.log("jjjjj", result);
        // if (result.status === true) {
        //   checkStatus();
        // } else {
        //   handleToast(result.message, "error");
        //   setKyCFialed(true);
        //   setCountdown(10);
        //   setIsCounting(true);
        // }
      })
      .catch((error) => {
        handleToast(error.message, "error");
        setKyCFialed(true);
        setCountdown(10);
        setIsCounting(true);
      })
      .finally(() => {
        setLoader(false);
      });
  };

  useEffect(() => {
    // @ts-ignore
    if (storedCredentails != undefined && storedCredentails != null) {
      if (typeof storedCredentails === "string") {
        const newRes = JSON.parse(storedCredentails);
        setTkn(newRes.token);
      } else {
        const newRes = storedCredentails;
        // @ts-ignore
        setTkn(newRes.token);
      }
    }
  }, []);

  // Handle countdown timer
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (isCounting && countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    } else if (isCounting && countdown === 0) {
      // Navigate back when countdown reaches zero
      navigation.goBack();
    }

    // Cleanup function to clear the timer when component unmounts or dependencies change
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isCounting, countdown, navigation]);

  const reasons = [
    "The document uploaded was blurred or cropped",
    "All four corners of the document were not visible",
    "The details did not match your profile information",
    "The document was expired or invalid",
    "A screenshot or edited image was submitted instead of a clear photo",
  ];

  return (
    <View style={styles.body}>
      <Div>
        {kycFailed ? (
          <View
            style={{
              width: "90%",
              paddingVertical: 24,
              flexDirection: "row",
              justifyContent: "flex-end",
              alignSelf: "center",
            }}
          >
            <TouchableOpacity
              style={{
                paddingHorizontal: 12,
                paddingVertical: 7,
                borderRadius: 99,
                borderWidth: 1,
                borderColor: colors.stroke,
              }}
              onPress={() => {
                navigation.pop();
              }}
            >
              <P style={{ fontSize: 12 }}>Close</P>
            </TouchableOpacity>
          </View>
        ) : (
          <AuthenticationHedear
            type="KYC"
            text="Account verification"
            navigation={navigation}
            currentStep={4}
            showSubmit={false}
            // submitFunction={() => headerSubmitFunction(formikProps)}
          />
        )}

        <ScrollView>
          <View style={styles.contentCard}>
            <View style={styles.section1Wrap}>
              <View style={styles.section3Wrap}>
                <View
                  style={{
                    width: "100%",
                    height: (75 * height) / 100,
                  }}
                >
                  {kycFailed ? (
                    <>
                      <View
                        style={{
                          width: "100%",
                          alignItems: "center",
                          marginTop: (2 * height) / 100,
                        }}
                      >
                        <SvgXml xml={svg.kycFrame} />
                        <P style={styles.statusState}>
                          KYC verification failed
                        </P>
                        <P style={styles.stTx}>
                          This could be due to one or more of the following
                          reasons:
                        </P>
                        <View style={{ width: "85%", marginTop: 24 }}>
                          {reasons.map((item, index) => (
                            <View
                              key={index}
                              style={{
                                flexDirection: "row",
                                width: "100%",
                                gap: 8,
                                marginBottom: 12,
                              }}
                            >
                              <P style={{ flexShrink: 0 }}>•</P>
                              <P
                                style={{
                                  flex: 1,
                                  fontSize: 14,
                                  fontFamily: fonts.poppinsRegular,
                                }}
                              >
                                {item}
                              </P>
                            </View>
                          ))}

                          <View>
                            <NoteComponent2
                              type="red"
                              contStyle={{
                                backgroundColor: colors.redSubtle,
                                marginTop: 12,
                              }}
                              text={
                                "Please double-check your document and try again."
                              }
                            />
                          </View>
                          <View
                            style={{
                              width: "90%",
                              alignSelf: "center",
                              marginTop: 32,
                            }}
                          >
                            <Button
                              btnText="Try again"
                              onPress={() => {
                                navigation.pop();
                              }}
                            />
                          </View>
                        </View>
                        {/* <View style={{ width: "75%", marginTop: 32 }}>
                          <P
                            style={{
                              alignSelf: "center",
                              color: colors.primary,
                            }}
                          >
                            {countdown > 0
                              ? `You will be redirected back in: ${countdown}s`
                              : "Redirecting..."}
                          </P>
                        </View> */}
                      </View>
                    </>
                  ) : (
                    <>
                      {data?.idMethod === "BVN" ? (
                        // BVN Verification with SmartSelfie
                        <SmileIDVerification
                          verificationType="selfie"
                          onComplete={(result: any) => {
                            try {
                              setLoader(true);
                              verifyBVN(result.data);
                            } catch (error) {
                              console.error(
                                "Error processing SmileID result:",
                                error
                              );
                              handleToast(
                                "Error processing verification data",
                                "error"
                              );
                            }
                          }}
                        />
                      ) : (
                        // Passport Verification
                        <SmileIDVerification
                          verificationType={"document"}
                          onComplete={(result: any) => {
                            try {
                              console.log(
                                "SmileID Web Component completed:",
                                result.data
                              );
                              setLoader(true);
                              verifyPassport(result.data);
                            } catch (error) {
                              console.error(
                                "Error processing SmileID result:",
                                error
                              );
                              handleToast(
                                "Error processing verification data",
                                "error"
                              );
                            }
                          }}
                        />
                      )}
                    </>
                  )}
                </View>
              </View>
            </View>
          </View>

          {/* <P
            style={{
              alignSelf: "center",
              textAlign: "center",
              marginTop: (16 / baseHeight) * height,
              fontSize: 12,
              lineHeight: (18 / baseHeight) * height,
              width: "90%",
              color: colors.gray,
              fontFamily: fonts.poppinsRegular,
            }}
          >
            This process ensures that your account remains safe and secure.
          </P> */}
          {/* <View style={styles.btnCont}>
            <Button btnText={"Continue"} onPress={() => {}} />
          </View> */}
        </ScrollView>
      </Div>
      {loader && (
        <Loader
          message={`Account verification in progress\nthis will take a while`}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentCard: {
    width: "100%",
    alignSelf: "center",
    borderRadius: 12,
    paddingBottom: (24 / baseHeight) * height,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  holder: {
    fontSize: 12,
    lineHeight: (18 / baseHeight) * height,
    color: colors.gray,
    marginBottom: (4 / baseHeight) * height,
  },
  value: {
    fontSize: 12,
    lineHeight: (18 / baseHeight) * height,
    color: colors.black,
  },
  copyBtn: {
    paddingTop: (4 / baseHeight) * height,
    paddingBottom: (4 / baseHeight) * height,
    padding: (13 / baseWidth) * width,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: (32 / baseHeight) * height,
    marginBottom: (16 / baseHeight) * height,
  },
  amt: {
    fontSize: 16,
    // fontSize: 32,
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  amtCur: {
    lineHeight: (24 / baseHeight) * height,
    fontFamily: fonts.poppinsMedium,
  },
  statusText: {
    fontSize: 10,
    lineHeight: 16,
    fontFamily: fonts.poppinsRegular,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginTop: (24 / baseHeight) * height,
    borderTopWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  section3Wrap: {
    width: "100%",
    marginTop: (24 / baseHeight) * height,
    borderColor: colors.stroke,
    // paddingLeft: 16,
  },
  progressDesCont: {
    flexDirection: "row",
  },
  progTextHead: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  progTextBody: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: (32 / baseHeight) * height,
    marginBottom: (64 / baseHeight) * height,
  },
  pinInput: {
    width: "40%",
    height: "100%",
    alignItems: "center",
    flexDirection: "row",
    borderRightColor: "#E6E5E5",
    borderRightWidth: 1,
  },
  pinTextInput: {
    fontSize: 14,
    textAlign: "center",
    color: "#161817",
    fontFamily: fonts.poppinsMedium,
    marginLeft: 16,
    marginRight: 8,
  },
  con: {
    flexDirection: "row",
    justifyContent: "center",
    width: "100%",
    height: 240,
    marginBottom: 32,
  },
  cameraCont: {
    width: (240 / baseWidth) * width,
    height: (240 / baseWidth) * width,
    borderRadius: 200,
    overflow: "hidden",
    borderWidth: 5,
    borderColor: colors.primary,
  },
  camera: {
    width: "100%",
    height: "100%",
    // borderRadius: width / 2,
    // overflow:'hidden',
  },
  statusState: {
    fontSize: 20,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsSemibold,
  },
  stTx: {
    fontSize: 14,
    lineHeight: 18,
    // paddingLeft: 50,
    // paddingRight: 50,
    // backgroundColor: 'red',
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.dark500,
    paddingHorizontal: 20,
    marginTop: 4,
  },
});
