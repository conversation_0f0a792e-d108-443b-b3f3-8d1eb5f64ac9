import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  StatusBar,
  Modal,
  BackHandler,
} from "react-native";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Link from "../../components/Link";
import { useNavigation, useFocusEffect } from "@react-navigation/native";
import { GetTransationById } from "../../RequestHandlers/Wallet";
import Loader from "../../components/ActivityIndicator";
import { countries } from "../../components/counties";
import { GetRateById } from "../../RequestHandlers/Wallet";
import { GetRateByCountry } from "../../RequestHandlers/Wallet";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
interface PProps {
  okayPress?: any;
  viewDetailPress?: any;
  tranStat?: "failed" | "success" | "pending";
  visible?: true | false;
  requestClose?: any;
  from?: string;
}
const { width, height } = Dimensions.get("window");
export default function AccountVerificationPending({ navigation, route }) {
  const [stImg, setStImg] = useState(require("../../assets/alert-circle.png"));
  const [stText, setStText] = useState("Sent money is pending");
  const [tranStat, setTranState] = useState("pending");
  const { response } = route?.params || {};
  const { destination } = route?.params || "";
  const [tranDetails, setTranDetails] = useState<any>([]);
  const [yellowCardData, setYellowCardData] = useState<any>([]);
  const [loader, setLoader] = useState(false);
  const [localRate, setLocaleRate] = useState(0);
  const [curDetails, setCurDetails] = useState("");
  const [symbol, setSymbol] = useState("");
  const [code, setCode] = useState("");
  const [linkData, setLinkData] = useState<any>([]);

  return (
    <View style={styles.body}>
      <View style={styles.itemBox}>
        <SvgXml width={60} height={60} xml={svg.idPending} />
        <P style={styles.statusState}>Account verification</P>
        <P style={styles.stTx}>
          Your account identity verification{"\n"}is currently processing
        </P>
        <View style={{ width: "75%", marginTop: 32 }}>
          <Button
            btnText="Okay!"
            onPress={() => {
              navigation.navigate("BottomTabNavigator");
            }}
          />
        </View>
      </View>
      {/* {loader && <Loader />} */}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    width,
    height: (105 * height) / 100,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    bottom: 0,
    // top: 0,
    zIndex: 100,
  },
  itemBox: {
    width: "100%",
    alignItems: "center",
    // marginTop: (20*height)/100
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    fontSize: 12,
    lineHeight: 19.2,
    paddingLeft: 50,
    paddingRight: 50,
    // backgroundColor: 'red',
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
});
