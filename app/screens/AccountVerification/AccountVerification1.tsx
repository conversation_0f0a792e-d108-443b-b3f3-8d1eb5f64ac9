import React, { useState, useEffect, useCallback, useContext } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Linking,
  Image,
  Platform,
  Keyboard,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Input from "../../components/Input";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import * as Clipboard from "expo-clipboard";
import Link from "../../components/Link";
import NoteComponent2 from "../../components/NoteComponent2";
import BottomSheet from "../../components/BottomSheet";
import OnboardingCountrySelect from "../../components/OnboardingCountrySelect";
import CountryCodeSelect from "../../components/CountryCodeSelect";
import { GetUserDetails, UpdateUser } from "../../RequestHandlers/User";
import { Formik } from "formik";
import * as yup from "yup";
import DateOfBirthPicker from "../../components/DatePicker";
import { useFocusEffect } from "@react-navigation/native";
import { useToast } from "../../context/ToastContext";
import { validateDateOfBirthForFinancialServices } from "../../Utils/ageValidation";
import { updateCredentials } from "../../Utils/credentialsUtils";
import { CredentailsContext } from "../../RequestHandlers/CredentailsContext";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";

const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function AccountVerification1({ navigation }) {
  const { handleToast } = useToast();
  const { setStoredCredentails } = useContext(CredentailsContext);
  const [countryCode, setCountryCode] = useState("+90");
  const [flag, setFlag] = useState(require("../../assets/turkey.png"));
  const [flag2, setFlag2] = useState(require("../../assets/turkey.png"));
  const [show, setShow] = useState(false);
  const [show2, setShow2] = useState(false);
  const [firstName, setFirstName] = useState("");
  const [middleName, setMiddleName] = useState("");
  const [lastName, setLastName] = useState("");
  const [loading, setLoading] = useState(false);
  const [showdatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState("");
  // const [isAccVerified, setAccVerified] = useState(false);
  const registerSchema2 = yup.object().shape({
    firstName: yup.string().required("First name is required"),
    middleName: yup.string(), // Optional field - no .required()
    lastName: yup.string().required("Last name is required"),
    phoneNumber: yup
      .string()
      .required("Phone number is required")
      .min(4, "Invalid mobile number")
      .matches(
        /^[0-9]+$/,
        "Phone number should not include letters or white spaces"
      ),
    dob: yup
      .string()
      .nullable()
      .required("Date of birth is required")
      .matches(
        /^(19|20)\d\d-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/,
        "Invalid date format (YYYY-MM-DD)"
      )
      .test("valid-date", "Invalid date of birth", function (value) {
        if (!value) return true;
        const date = new Date(value);
        return !isNaN(date.getTime());
      })
      .test(
        "age-validation",
        "Minimum age requirement is 17 years old",
        function (value) {
          if (!value) return true;
          const ageValidation = validateDateOfBirthForFinancialServices(value);
          return ageValidation.isValid;
        }
      ),
    homeCountry: yup.string().required("Home country is required"),
    residentAddress: yup.string().required("Residential address is required"),
    referralCode: yup.string(),
  });

  const getUserDetails = async () => {
    try {
      const userDetails = await GetUserDetails();
      setFirstName(userDetails?.firstName);
      setMiddleName(userDetails?.middleName || "");
      userDetails.lastName ? setLastName(userDetails?.lastName) : "";
    } catch (error) {}
  };

  useEffect(() => {
    getUserDetails();
  }, []);

  // Separate submit function for AuthenticationHeader that navigates to BottomTabNavigator
  const headerSubmitFunction = async (formikProps) => {
    // Validate the form first
    const errors = await formikProps.validateForm();
    if (Object.keys(errors).length > 0) {
      formikProps.setTouched({
        firstName: true,
        lastName: true,
        phoneNumber: true,
        dob: true,
        homeCountry: true,
        residentAddress: true,
      });
      return;
    }

    Keyboard.dismiss();
    setLoading(true);
    try {
      const values = formikProps.values;
      const body: any = {
        firstName: values.firstName.trim(),
        lastName: values.lastName.trim(),
        phoneNumber: `${countryCode}${values.phoneNumber
          .trim()
          .replace(/^0/, "")}`,
        dob: values.dob.trim(),
        homeCountry: values.homeCountry.trim(),
        residentAddress: values.residentAddress.trim(),
      };
      if (values.referralCode && values.referralCode.trim() !== "") {
        body.referralCode = values.referralCode.trim();
      }
      if (values.middleName && values.middleName.trim() !== "") {
        body.middleName = values.middleName.trim();
      }
      const updateUser = await withApiErrorToast(
        UpdateUser(body),
        handleToast
      );
      if (updateUser.message === "User Updated Successfully!") {
        // Update credentials in storage and context
        await updateCredentials(setStoredCredentails, {
          firstName: values.firstName.trim(),
          lastName: values.lastName.trim(),
          middleName: values.middleName?.trim(),
          phoneNumber: `${countryCode}${values.phoneNumber
            .trim()
            .replace(/^0/, "")}`,
          dob: values.dob.trim(),
          homeCountry: values.homeCountry.trim(),
          residentAddress: values.residentAddress.trim(),
        });

        setLoading(false);
        navigation.navigate("BottomTabNavigator");
        handleToast("Successful", "success");
      } else {
        setLoading(false);
        handleToast(updateUser.message, "error");
      }
    } catch (error) {
      setLoading(false);
      handleToast("Unknown error try again", "error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.body}>
      <Div>
        <Formik
          initialValues={{
            firstName: firstName || "",
            middleName: middleName || "",
            lastName: lastName || "",
            phoneNumber: "",
            dob: "",
            homeCountry: "",
            residentAddress: "",
            referralCode: "",
          }}
          // enableReinitialize
          validationSchema={registerSchema2}
          onSubmit={async (values) => {
            Keyboard.dismiss();
            setLoading(true);
            try {
              const body: any = {
                firstName: values.firstName.trim(),
                lastName: values.lastName.trim(),
                phoneNumber: `${countryCode}${values.phoneNumber
                  .trim()
                  .replace(/^0/, "")}`,
                dob: values.dob.trim(),
                homeCountry: values.homeCountry.trim(),
                residentAddress: values.residentAddress.trim(),
              };
              if (values.referralCode && values.referralCode.trim() !== "") {
                body.referralCode = values.referralCode.trim();
              }
              if (values.middleName && values.middleName.trim() !== "") {
                body.middleName = values.middleName.trim();
              }
              const updateUser = await withApiErrorToast(
                UpdateUser(body),
                handleToast
              );
              if (updateUser.message === "User Updated Successfully!") {
                // Update credentials in storage and context
                await updateCredentials(setStoredCredentails, {
                  firstName: values.firstName.trim(),
                  lastName: values.lastName.trim(),
                  middleName: values.middleName?.trim(),
                  phoneNumber: `${countryCode}${values.phoneNumber
                    .trim()
                    .replace(/^0/, "")}`,
                  dob: values.dob.trim(),
                  homeCountry: values.homeCountry.trim(),
                  residentAddress: values.residentAddress.trim(),
                });

                setLoading(false);
                navigation.navigate("AccountVerification2");
                handleToast("Successful", "success");
              } else {
                setLoading(false);
                handleToast(updateUser.message, "error");
              }
            } catch (error) {
              setLoading(false);
              handleToast("Unknown error try again", "error");
            } finally {
              setLoading(false);
            }
          }}
        >
          {(formikProps) => (
            <>
              <AuthenticationHedear
                type="KYC"
                text="Account verification"
                navigation={navigation}
                currentStep={1}
                submitFunction={() => headerSubmitFunction(formikProps)}
                loading={loading}
              />
              <ScrollView
                contentContainerStyle={{
                  paddingBottom: 100,
                }}
                automaticallyAdjustKeyboardInsets={true}
              >
                <View style={styles.contentCard}>
                  <View style={styles.section1Wrap}>
                    <H4 style={styles.amt}>Personal information</H4>
                    <H4
                      // @ts-ignore
                      style={[
                        styles.amt,
                        {
                          fontSize: 14,
                          lineHeight: 18,
                          color: colors.dark500,
                          textAlign: "center",
                          fontFamily: fonts.poppinsRegular,
                        },
                      ]}
                    >
                      Enter your personal information as it is on your
                      government document
                    </H4>

                    <View style={styles.section2Wrap}></View>
                    <View style={styles.section3Wrap}>
                      <Input
                        label="First name"
                        placeholder="John"
                        inputStyle={{ width: "85%" }}
                        // contStyle={{ marginBottom: (16 / baseHeight) * height }}
                        // value={firstName}
                        onChangeText={formikProps.handleChange("firstName")}
                        value={formikProps.values.firstName}
                        onBlur={formikProps.handleBlur("firstName")}
                        autoCapitalize="none"
                        error={
                          formikProps.errors.firstName &&
                          formikProps.touched.firstName
                        }
                      />
                      {formikProps.errors.firstName &&
                        formikProps.touched.firstName && (
                          <P style={styles.errorText}>
                            {formikProps.errors.firstName}
                          </P>
                        )}
                      <Input
                        label="Middle name (optional)"
                        placeholder="Michael"
                        inputStyle={{ width: "85%" }}
                        contStyle={{ marginTop: (16 / baseHeight) * height }}
                        onChangeText={formikProps.handleChange("middleName")}
                        value={formikProps.values.middleName}
                        onBlur={formikProps.handleBlur("middleName")}
                        autoCapitalize="none"
                        error={
                          formikProps.errors.middleName &&
                          formikProps.touched.middleName
                        }
                      />
                      {formikProps.errors.middleName &&
                        formikProps.touched.middleName && (
                          <P style={styles.errorText}>
                            {formikProps.errors.middleName}
                          </P>
                        )}
                      <Input
                        label="Last name"
                        placeholder="Doe"
                        inputStyle={{ width: "85%" }}
                        contStyle={{ marginTop: (16 / baseHeight) * height }}
                        onChangeText={formikProps.handleChange("lastName")}
                        value={formikProps.values.lastName}
                        onBlur={formikProps.handleBlur("lastName")}
                        autoCapitalize="none"
                        error={
                          formikProps.errors.lastName &&
                          formikProps.touched.lastName
                        }
                      />
                      {formikProps.errors.lastName &&
                        formikProps.touched.lastName && (
                          <P style={styles.errorText}>
                            {formikProps.errors.lastName}
                          </P>
                        )}
                      <Input
                        label="Phone Number"
                        placeholder="8144855058"
                        inputStyle={{ width: "80%" }}
                        contStyle={{ marginTop: (16 / baseHeight) * height }}
                        onChangeText={(text) => {
                          // Remove all white spaces and non-numeric characters
                          const cleanedText = text
                            .replace(/\s/g, "")
                            .replace(/[^0-9]/g, "");
                          formikProps.setFieldValue("phoneNumber", cleanedText);
                        }}
                        value={formikProps.values.phoneNumber}
                        keyboardType={"numeric"}
                        onBlur={formikProps.handleBlur("phoneNumber")}
                        autoCapitalize="none"
                        error={
                          formikProps.errors.phoneNumber &&
                          formikProps.touched.phoneNumber
                        }
                        leftIcon={
                          <TouchableOpacity
                            style={[styles.pinInput]}
                            onPress={() => {
                              setShow(true);
                            }}
                          >
                            <Image
                              source={flag}
                              style={{
                                width: (24 / baseWidth) * width,
                                height: (24 / baseWidth) * width,
                                marginLeft: (16 / baseWidth) * width,
                                borderRadius: 100,
                                objectFit:
                                  countryCode === "+234" ? "fill" : "cover",
                              }}
                            />
                            <P style={styles.pinTextInput}>{countryCode}</P>
                            <SvgXml xml={svg.dropDown} />
                          </TouchableOpacity>
                        }
                      />
                      {formikProps.errors.phoneNumber &&
                        formikProps.touched.phoneNumber && (
                          <P style={styles.errorText}>
                            {formikProps.errors.phoneNumber}
                          </P>
                        )}
                      <TouchableOpacity
                        onPress={() => {
                          setShowDatePicker(true);
                        }}
                      >
                        <Input
                          label="Date of birth"
                          placeholder="06-01-2000"
                          inputStyle={{ width: "85%" }}
                          contStyle={{ marginTop: (16 / baseHeight) * height }}
                          onChangeText={formikProps.handleChange("dob")}
                          value={formikProps.values.dob}
                          onBlur={formikProps.handleBlur("dob")}
                          autoCapitalize="none"
                          editable={false}
                          error={
                            formikProps.errors.dob && formikProps.touched.dob
                          }
                          rightIcon={
                            <View
                              style={{
                                //   backgroundColor: "red",
                                width: "15%",
                                height: "100%",
                                justifyContent: "center",
                                alignItems: "center",
                              }}
                            >
                              <SvgXml xml={svg.dropDown} />
                            </View>
                          }
                        />
                        {formikProps.errors.dob && formikProps.touched.dob && (
                          <P style={styles.errorText}>
                            {formikProps.errors.dob}
                          </P>
                        )}
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => {
                          setShow2(true);
                        }}
                      >
                        <Input
                          label="Home Country"
                          placeholder="Turkey"
                          inputStyle={{ width: "85%", color: colors.black }}
                          contStyle={{ marginTop: (16 / baseHeight) * height }}
                          onChangeText={formikProps.handleChange("homeCountry")}
                          value={formikProps.values.homeCountry}
                          onBlur={formikProps.handleBlur("homeCountry")}
                          autoCapitalize="none"
                          error={
                            formikProps.errors.homeCountry &&
                            formikProps.touched.homeCountry
                          }
                          leftIcon={
                            <Image
                              source={flag2}
                              style={{
                                width: (24 / baseWidth) * width,
                                height: (24 / baseWidth) * width,
                                marginLeft: (16 / baseWidth) * width,
                                borderRadius: 100,
                                objectFit:
                                  formikProps.values.homeCountry === "Nigeria"
                                    ? "fill"
                                    : "cover",
                              }}
                            />
                            // <P style={styles.pinTextInput}>{flag2}</P>
                          }
                          editable={false}
                          rightIcon={
                            <View
                              style={{
                                //   backgroundColor: "red",
                                width: "15%",
                                height: "100%",
                                justifyContent: "center",
                                alignItems: "center",
                              }}
                            >
                              <SvgXml xml={svg.dropDown} />
                            </View>
                          }
                        />
                        {formikProps.errors.homeCountry &&
                          formikProps.touched.homeCountry && (
                            <P style={styles.errorText}>
                              {formikProps.errors.homeCountry}
                            </P>
                          )}
                      </TouchableOpacity>
                      <Input
                        label="Resident address"
                        placeholder="Turkey"
                        inputStyle={{ width: "85%" }}
                        contStyle={{ marginTop: (16 / baseHeight) * height }}
                        onChangeText={formikProps.handleChange(
                          "residentAddress"
                        )}
                        value={formikProps.values.residentAddress}
                        onBlur={formikProps.handleBlur("residentAddress")}
                        autoCapitalize="none"
                        error={
                          formikProps.errors.residentAddress &&
                          formikProps.touched.residentAddress
                        }
                      />
                      {formikProps.errors.residentAddress &&
                        formikProps.touched.residentAddress && (
                          <P style={styles.errorText}>
                            {formikProps.errors.residentAddress}
                          </P>
                        )}
                      <Input
                        placeholder="Enter referral code"
                        label="Referral Code (optional)"
                        inputStyle={{ width: "85%" }}
                        value={formikProps.values.referralCode}
                        contStyle={{ marginTop: (16 / baseHeight) * height }}
                        onChangeText={formikProps.handleChange("referralCode")}
                      />
                    </View>
                  </View>
                </View>
                <View style={styles.btnCont}>
                  <Button
                    btnText={"Continue"}
                    onPress={formikProps.handleSubmit}
                    loading={loading}
                  />
                  {/* <Link
              style={{
                textAlign: "center",
                marginTop: (16 / baseHeight) * height,
              }}
              onPress={() => navigation.navigate("Home")}
            >
              Submit & return home
            </Link> */}
                </View>

                <BottomSheet
                  isVisible={show2}
                  backspaceText={"Home country"}
                  onClose={() => setShow2(false)}
                  showBackArrow={false}
                  components={
                    <OnboardingCountrySelect
                      onPress={(country) => {
                        const previousCountry = formikProps.values.homeCountry;
                        // Set flag using the country code
                        setFlag2({
                          uri: `https://flagcdn.com/w2560/${country.code.toLowerCase()}.png`,
                        });
                        // Only clear resident address if the country actually changed
                        if (
                          previousCountry !== country.name &&
                          previousCountry !== ""
                        ) {
                          formikProps.setFieldValue("residentAddress", "");
                        }
                        // Set home country value
                        formikProps.setFieldValue("homeCountry", country.name);
                        // Close the modal
                        setShow2(false);
                      }}
                    />
                  }
                  modalContentStyle={{ height: "80%" }}
                  extraModalStyle={{ height: "77%" }}
                  componentHolderStyle={{ flex: 1 }}
                />

                <BottomSheet
                  isVisible={show}
                  backspaceText={"Phone number"}
                  onClose={() => setShow(false)}
                  showBackArrow={false}
                  components={
                    <CountryCodeSelect
                      onPress={(country) => {
                        setShow(false);
                        setCountryCode(country.tel_code);
                        setFlag({
                          uri: `https://flagcdn.com/w2560/${country.code.toLowerCase()}.png`,
                        });
                      }}
                    />
                  }
                  modalContentStyle={{ height: "80%" }}
                  extraModalStyle={{ height: "77%" }}
                  componentHolderStyle={{ flex: 1 }}
                />
                <BottomSheet
                  isVisible={showdatePicker}
                  backspaceText="Date of birth"
                  onClose={() => setShowDatePicker(false)}
                  showBackArrow={false}
                  components={
                    <DateOfBirthPicker
                      selectedDate={selectedDate}
                      onDateChange={(date) => {
                        setSelectedDate(date);
                        formikProps.setFieldValue("dob", date);
                      }}
                      closeModal={() => {
                        setShowDatePicker(false);
                      }}
                    />
                  }
                  modalContentStyle={{ height: "72%" }}
                  extraModalStyle={{ height: "70%" }}
                />
              </ScrollView>
            </>
          )}
        </Formik>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentCard: {
    width: "95%",
    alignSelf: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    // marginTop: 24,
    // marginTop: 24,
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 16,
    paddingRight: 16,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  holder: {
    fontSize: 12,
    lineHeight: (18 / baseHeight) * height,
    color: colors.gray,
    marginBottom: (4 / baseHeight) * height,
  },
  value: {
    fontSize: 12,
    lineHeight: (18 / baseHeight) * height,
    color: colors.black,
  },
  copyBtn: {
    paddingTop: (4 / baseHeight) * height,
    paddingBottom: (4 / baseHeight) * height,
    padding: (13 / baseWidth) * width,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
    marginBottom: 16,
  },
  amt: {
    fontSize: 20,
    // fontSize: 32,
    // lineHeight: 48,
    fontFamily: fonts.poppinsSemibold,
  },
  amtCur: {
    lineHeight: 24,
    fontFamily: fonts.poppinsMedium,
  },
  statusText: {
    fontSize: 10,
    lineHeight: 16,
    fontFamily: fonts.poppinsRegular,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginTop: 24,
    // paddingTop: (2.7 * height) / 100,
    borderTopWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  section3Wrap: {
    width: "100%",
    marginTop: 16,
    borderColor: colors.stroke,
    // paddingLeft: 16,
  },
  progressDesCont: {
    flexDirection: "row",
  },
  progTextHead: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  progTextBody: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: (32 / baseHeight) * height,
    marginBottom: (64 / baseHeight) * height,
    // marginTop: 42,
    // alignItems: 'center',
    // justifyContent: 'center',
  },
  pinInput: {
    width: "40%",
    height: "100%",
    alignItems: "center",
    flexDirection: "row",
    borderRightColor: "#E6E5E5",
    borderRightWidth: 1,
  },
  pinTextInput: {
    fontSize: 14,
    textAlign: "center",
    color: "#161817",
    fontFamily: fonts.poppinsMedium,
    marginLeft: 16,
    marginRight: 8,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
});
