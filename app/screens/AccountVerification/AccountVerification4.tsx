import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  BackHandler,
  Image,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Link from "../../components/Link";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import Input from "../../components/Input";
import BottomSheet from "../../components/BottomSheet";
import IdentificationSelect from "../../components/IdentificationSelect";
import { Identification } from "../../components/Identification";
import * as ImagePicker from "expo-image-picker";
import { CameraView } from "expo-camera";
import OnboardingCountrySelect from "../../components/OnboardingCountrySelect";
import { useFocusEffect } from "@react-navigation/native";
import DateOfBirthPicker from "../../components/DatePicker";
import { GetUserDetails } from "../../RequestHandlers/User";
import NoteComponent2 from "../../components/NoteComponent2";
import { useToast } from "../../context/ToastContext";
import { validateDateOfBirthForFinancialServices } from "../../Utils/ageValidation";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";

const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");

export default function AccountVerification4({ navigation }) {
  const [image, setimage] = useState("");
  const [show, setShow] = useState(false);
  const [show2, setShow2] = useState(false);
  const [show3, setShow3] = useState(false);
  const [show4, setShow4] = useState(false);
  const [show5, setShow5] = useState(false);
  const mobileCodeRef = useRef<String | null>(null);
  const alphaCodeRef = useRef<String | null>(null);
  const apphaCode2Ref = useRef<String | null>(null);
  const [alphaCode, setAlphaCode] = useState("");
  const [countryCode2, setCountryCode2] = useState("");
  const [isFrontCam, setIsFrontCam] = useState(false);
  const [flag2, setFlag2] = useState(require("../../assets/nigeria.png"));
  const [country, setCountry] = useState("");
  const [snappedPicture, setSnappedPicture] = useState("");
  const [idType, setIdType] = useState(null);
  const cameraRef = useRef(null);
  const [errorMessage, setErrorMessage] = useState("");
  const [idValue, setIdValue] = useState("");
  const [showdatePicker, setShowDatePicker] = useState(false);
  const [dobError, setDobError] = useState(false);
  const [selectedDate, setSelectedDate] = useState("");
  const [result, setResult] = useState([]);
  const [mobileCode, setMobileCode] = useState("");
  const [phone, setPhone] = useState("");
  const [error, setError] = useState(false);
  const [loading, setLoading] = useState(false);
  const [userId, setUserId] = useState("");
  const [alpha2Code, setAlpha2Code] = useState("");
  const [flag, setFlag] = useState("");
  const { handleToast } = useToast();

  const selectProfileImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 1,
    });

    if (!result.canceled) {
      setimage(result.assets[0].fileName);
      setShow3(false);
    }
  };

  const takePicture = async () => {
    if (cameraRef.current) {
      const options = { quality: 0.5, base64: true };
      const data = await cameraRef.current.takePictureAsync(options);
      setSnappedPicture(data.uri);
      setimage(data.uri.split("Camera")[1].replace("/", ""));
    }
  };

  useEffect(() => {
    mobileCodeRef.current = mobileCode;
  }, [mobileCode]);
  useEffect(() => {
    alphaCodeRef.current = alphaCode;
  }, [alphaCode]);
  useEffect(() => {
    apphaCode2Ref.current = alpha2Code;
  }, [alpha2Code]);
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        // Reset navigation stack to avoid loading issues
        // @ts-ignore
        navigation.reset({
          index: 0,
          routes: [{ name: "BottomTabNavigator" }],
        });
        return true;
      };
      // Disable iOS swipe back gesture
      navigation.setOptions({
        gestureEnabled: false,
      });
      // Handle Android back button
      BackHandler.addEventListener("hardwareBackPress", onBackPress);

      return () => {
        BackHandler.removeEventListener("hardwareBackPress", onBackPress);
      };
    }, [navigation])
  );

  const getUserDetetails = async () => {
    try {
      const res = await withApiErrorToast(GetUserDetails(), handleToast);
      setUserId(res.id);
    } catch (error) {}
  };

  useEffect(() => {
    getUserDetetails();
  }, []);
  const validateID = (value: string) => {
    let minDigits = 0;
    // Set minimum digits based on the ID type
    switch (countryCode2) {
      case "BVN":
        minDigits = 11;
        break;
      case "NIN":
        minDigits = 11;
        break;
      default:
        minDigits = 4; // For other ID types
    }
    // Check if the input length is less than the required minimum
    if (value.length < minDigits) {
      setErrorMessage(
        `The ${countryCode2} number must be at least ${minDigits} digits long.`
      );
    } else {
      setErrorMessage(""); // Clear error message if valid
    }

    setIdValue(value); // Set the value
  };

  const submit = () => {
    let errorhandling = 1;
    if (selectedDate === "") {
      setDobError(true);
      errorhandling = 0;
    } else {
      // Validate age requirement
      const ageValidation =
        validateDateOfBirthForFinancialServices(selectedDate);
      if (!ageValidation.isValid) {
        setDobError(true);
        errorhandling = 0;
      } else {
        setDobError(false);
      }
    }
    if (phone.length < 7) {
      setError(true);
      errorhandling = 0;
    } else {
      setError(false);
    }
    if (idValue.length === 0) {
      setErrorMessage(`Your ${countryCode2} number is required`);
      errorhandling = 0;
    } else if (errorMessage !== "") {
      errorhandling = 0;
    } else {
      setErrorMessage("");
    }
    if (errorhandling === 1) {
      const data = {
        country: country,
        idMethod: countryCode2,
        dob: selectedDate,
        idNum: idValue,
        phone: `${mobileCode}${phone.trim().replace(/^0/, "")}`,
        alphaCode: alpha2Code,
        userId: userId,
      };
      navigation.navigate("FaceIDScreen2", { data });
    }
  };

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          type="KYC"
          text="Account verification"
          navigation={navigation}
          currentStep={4}
          showSubmit={false}
          // submitFunction={() => headerSubmitFunction(formikProps)}
          loading={loading}
        />
        <ScrollView automaticallyAdjustKeyboardInsets={true}>
          <View style={styles.contentCard}>
            <View style={styles.section1Wrap}>
              <H4 style={styles.amt}>Identity verification</H4>
              <H4
                // @ts-ignore
                style={[
                  styles.amt,
                  {
                    fontSize: 14,
                    lineHeight: 18,
                    color: colors.dark500,
                    textAlign: "center",
                    fontFamily: fonts.poppinsRegular,
                  },
                ]}
              >
                Provide a means of identification{"\n"}to verify your account
              </H4>
              <View style={styles.section3Wrap}>
                <TouchableOpacity
                  onPress={() => {
                    setShow5(true);
                  }}
                >
                  <Input
                    value={country}
                    label="Country"
                    placeholder="Nigeria"
                    leftIcon={
                      <Image
                        source={flag2}
                        style={{
                          width: 24,
                          height: 24,
                          marginLeft: 16,
                          borderRadius: 100,
                          objectFit: country === "Nigeria" ? "fill" : "cover",
                        }}
                      />
                      // <P style={styles.pinTextInput}>{flag2}</P>
                    }
                    inputStyle={{
                      width: "85%",
                      color: colors.black,
                      fontFamily: fonts.poppinsMedium,
                    }}
                    contStyle={{ marginBottom: 16 }}
                    editable={false}
                    rightIcon={
                      <View
                        style={{
                          //   backgroundColor: "red",
                          width: "15%",
                          height: "100%",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <SvgXml xml={svg.dropDown} />
                      </View>
                    }
                  />
                </TouchableOpacity>
                {country === "" ? (
                  <></>
                ) : (
                  <TouchableOpacity
                    onPress={() => {
                      setShow2(true);
                    }}
                  >
                    <Input
                      value={countryCode2}
                      label="Identification method"
                      placeholder="Choose identification method"
                      inputStyle={{
                        width: "85%",
                        color: colors.black,
                        fontFamily: fonts.poppinsMedium,
                      }}
                      // contStyle={{ marginBottom: 16 }}
                      editable={false}
                      rightIcon={
                        <View
                          style={{
                            //   backgroundColor: "red",
                            width: "15%",
                            height: "100%",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <SvgXml xml={svg.dropDown} />
                        </View>
                      }
                    />
                  </TouchableOpacity>
                )}

                <>
                  {countryCode2 != "" && (
                    <TouchableOpacity
                      onPress={() => {
                        setShowDatePicker(true);
                      }}
                    >
                      <Input
                        label="Date of birth"
                        placeholder="06-01-2000"
                        inputStyle={{ width: "85%" }}
                        contStyle={{ marginTop: (16 / baseHeight) * height }}
                        autoCapitalize="none"
                        editable={false}
                        value={selectedDate}
                        error={dobError}
                        rightIcon={
                          <View
                            style={{
                              //   backgroundColor: "red",
                              width: "15%",
                              height: "100%",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <SvgXml xml={svg.dropDown} />
                          </View>
                        }
                      />
                      {dobError && (
                        <P style={styles.errorText}>
                          {selectedDate === ""
                            ? "Select your date of birth"
                            : "Invalid Date of Birth"}
                        </P>
                      )}
                    </TouchableOpacity>
                  )}
                </>
                {countryCode2 != "" && (
                  <>
                    <Input
                      label={`Phone number`}
                      placeholder="8144855058"
                      inputStyle={{ width: "80%" }}
                      contStyle={{ marginTop: 16 }}
                      value={phone}
                      onChangeText={(e) => {
                        setPhone(e);
                        setError(false);
                      }}
                      keyboardType={"numeric"}
                      error={error}
                      leftIcon={
                        <TouchableOpacity
                          style={[styles.pinInput]}
                          // onPress={() => {
                          //   setShow(true);
                          // }}
                        >
                          <Image
                            source={flag2}
                            style={{
                              width: 24,
                              height: 24,
                              marginLeft: (14 / baseWidth) * width,
                              marginRight: (10 / baseWidth) * width,
                              borderRadius: 100,
                              objectFit:
                                country === "Nigeria" ? "fill" : "cover",
                            }}
                          />
                          {/* @ts-ignore */}
                          <P style={[styles.pinTextInput, { fontSize: 12 }]}>
                            {mobileCode}
                          </P>
                        </TouchableOpacity>
                      }
                    />
                    {error && (
                      <P style={styles.errorText}>
                        Number should not be less than 7 digit
                      </P>
                    )}
                  </>
                )}
                {countryCode2 === "" ? (
                  <></>
                ) : (
                  <>
                    <Input
                      label={
                        countryCode2 === "BVN"
                          ? "BVN number"
                          : countryCode2 === "NIN"
                          ? "NIN number"
                          : "Your International Passport number"
                      }
                      placeholder="389330332345"
                      value={idValue}
                      inputStyle={{ width: "85%" }}
                      keyboardType={
                        countryCode2 !== "BVN" ? "default" : "numeric"
                      }
                      contStyle={{
                        marginTop: 16,
                      }}
                      onChangeText={(text) => validateID(text)}
                      error={errorMessage !== "" ? true : false}
                    />
                    {errorMessage && (
                      <P style={styles.errorText}>{errorMessage}</P>
                    )}
                    <TouchableOpacity
                      style={{ flexDirection: "row", marginTop: 16 }}
                      onPress={() => setShow(true)}
                    >
                      <SvgXml xml={svg.help} />
                      <P
                        style={{
                          fontFamily: fonts.poppinsRegular,
                          fontSize: 14,
                          textDecorationLine: "underline",
                          textDecorationColor: "#161817",
                          marginLeft: 8,
                        }}
                      >
                        Guidance
                      </P>
                    </TouchableOpacity>
                    {/* {countryCode2 != "" && countryCode2 != "BVN" && (
                      <TouchableOpacity
                        onPress={() => setShow3(true)}
                        style={{
                          flexDirection: "row",
                          width: "100%",
                          backgroundColor: "#F1EBFF",
                          height: 44,
                          borderColor: "#A5A1A1",
                          borderWidth: 1,
                          borderStyle: "dashed",
                          // marginTop: 16,
                          marginTop: 16,
                          justifyContent: "center",
                          alignItems: "center",
                          borderRadius: 8,
                        }}
                      >
                        {image == "" ? (
                          <SvgXml xml={svg.upup} />
                        ) : (
                          <SvgXml xml={svg.image} />
                        )}
                        <P
                          numberOfLines={1}
                          style={{
                            fontFamily: fonts.poppinsRegular,
                            fontSize: 14,
                            width: "80%",
                            textDecorationLine: "underline",
                            textDecorationColor: "#161817",
                            color: "#161817",
                            marginLeft: 8,
                          }}
                        >
                          Click to capture imgae
                        </P>
                      </TouchableOpacity>
                    )} */}
                  </>
                )}
              </View>
            </View>
          </View>
          <View style={styles.btnCont}>
            <Button
              loading={loading}
              btnText={"Continue"}
              onPress={() => {
                if (country === "") {
                  handleToast(
                    "Select the country you want to verify with",
                    "error"
                  );
                }
                submit();
              }}
            />
          </View>
        </ScrollView>
      </Div>
      <BottomSheet
        isVisible={show2}
        backspaceText={"Identification method"}
        onClose={() => setShow2(false)}
        showBackArrow={false}
        components={
          <>
            <P
              style={{
                marginTop: 24,
                fontSize: 12,
                color: colors.gray,
                fontFamily: fonts.poppinsRegular,
              }}
            >
              Select your identification method
            </P>
            <View>
              {country === "Nigeria" ? (
                <>
                  <TouchableOpacity
                    style={styles.idSelect}
                    onPress={() => {
                      setCountryCode2("BVN");
                      setShow2(false);
                      setPhone("");
                      setIdValue("");
                      setSelectedDate("");
                    }}
                  >
                    <P
                      // @ts-ignore
                      style={[
                        styles.vText,
                        {
                          backgroundColor:
                            countryCode2 === "BVN"
                              ? colors.secBackground
                              : "transparent",
                        },
                      ]}
                    >
                      Bank Verification Number (BVN)
                    </P>
                  </TouchableOpacity>
                  {/* <TouchableOpacity
                    style={styles.idSelect}
                    onPress={() => {
                      setCountryCode2("NIN");
                      setShow2(false);
                    }}
                  >
                    <P
                      // @ts-ignore
                      style={[
                        styles.vText,
                        {
                          backgroundColor:
                            countryCode2 === "NIN"
                              ? colors.secBackground
                              : "transparent",
                        },
                      ]}
                    >
                      National Identification Number(NIN)
                    </P>
                  </TouchableOpacity> */}
                  <TouchableOpacity
                    style={styles.idSelect}
                    onPress={() => {
                      setCountryCode2("Passport");
                      setShow2(false);
                      setPhone("");
                      setIdValue("");
                      setSelectedDate("");
                    }}
                  >
                    <P
                      // @ts-ignore
                      style={[
                        styles.vText,
                        {
                          backgroundColor:
                            countryCode2 === "Passport"
                              ? colors.secBackground
                              : "transparent",
                        },
                      ]}
                    >
                      Passport
                    </P>
                  </TouchableOpacity>
                </>
              ) : (
                <>
                  <TouchableOpacity
                    style={styles.idSelect}
                    onPress={() => {
                      setCountryCode2("Passport");
                      setShow2(false);
                    }}
                  >
                    <P>Passport</P>
                  </TouchableOpacity>
                </>
              )}
            </View>
          </>
        }
        modalContentStyle={{ height: "40%" }}
        extraModalStyle={{ height: "38%" }}
      />
      {show3 && (
        <View
          style={{
            position: "absolute",
            width,
            height,
            backgroundColor: "red",
            top: 0,
          }}
        ></View>
      )}
      <BottomSheet
        isVisible={show}
        backspaceText={"Guidance"}
        onClose={() => setShow(false)}
        showBackArrow={false}
        components={
          <ScrollView
            contentContainerStyle={{ paddingBottom: 200 }}
            showsVerticalScrollIndicator={false}
          >
            <P
              style={{
                marginTop: (24 / baseHeight) * height,
                fontFamily: fonts.poppinsMedium,
              }}
            >
              What is identity verification?
            </P>
            <P
              style={{
                color: colors.gray,
                fontFamily: fonts.poppinsRegular,
                marginTop: (16 / baseHeight) * height,
              }}
            >
              It's a simple process that helps us confirm you are who you say
              you are. This helps prevent fraud and keeps your hard-earned money
              safe.
            </P>
            <P
              style={{
                marginTop: (24 / baseHeight) * height,
                fontFamily: fonts.poppinsMedium,
              }}
            >
              Here's what you'll need:
            </P>
            <P
              style={{
                color: colors.gray,
                fontFamily: fonts.poppinsRegular,
                marginTop: (16 / baseHeight) * height,
              }}
            >
              Your International Passport
            </P>
            <P
              style={{
                marginTop: (24 / baseHeight) * height,
                fontFamily: fonts.poppinsMedium,
              }}
            >
              The process is quick and easy:
            </P>
            <View
              style={{
                marginTop: (16 / baseHeight) * height,
                flexDirection: "row",
              }}
            >
              <P style={{ color: colors.gray, fontSize: 16 }}>{"\u2022 "}</P>
              <P
                style={{ color: colors.gray, fontFamily: fonts.poppinsRegular }}
              >
                Snap a picture: Take a clear photo of your government ID
              </P>
            </View>
            <View
              style={{
                marginTop: (6 / baseHeight) * height,
                flexDirection: "row",
              }}
            >
              <P style={{ color: colors.gray, fontSize: 16 }}>{"\u2022 "}</P>
              <P
                style={{
                  color: colors.gray,
                  fontFamily: fonts.poppinsRegular,
                  marginRight: (15 / baseWidth) * width,
                }}
              >
                Selfie time: Capture a quick selfie to match your ID photo.
              </P>
            </View>
            <P
              style={{
                marginTop: (24 / baseHeight) * height,
                fontFamily: fonts.poppinsMedium,
              }}
            >
              The process is quick and easy:
            </P>
            <P
              style={{
                color: colors.gray,
                fontFamily: fonts.poppinsRegular,
                marginTop: (16 / baseHeight) * height,
              }}
            >
              our information is securely stored and used only for
              verification purposes
            </P>
            <View
              style={{
                marginTop: 24,
              }}
            >
              <NoteComponent2
                text={"Image guide"}
                textStyle={{ fontFamily: fonts.poppinsMedium }}
                contStyle={{ flexDirection: "column" }}
                component2={
                  <View style={{ paddingLeft: 24 }}>
                    <Image
                      source={require("../../assets/sampleImg.png")}
                      style={{
                        height: 140,
                        width: "100%",
                        marginTop: 16,
                        // objectFit: "contain",
                      }}
                    />
                  </View>
                }
              />
            </View>
            <View style={{ marginTop: 24 }}>
              <NoteComponent2
                textStyle={{ fontFamily: fonts.poppinsMedium }}
                text={"We value your privacy"}
                contStyle={{ flexDirection: "column" }}
                component2={
                  <P
                    style={{
                      marginTop: 8,
                      paddingLeft: 24,
                      fontSize: 10,
                      fontFamily: fonts.poppinsRegular,
                      lineHeight: 18,
                    }}
                  >
                    Your information is securely stored and used only for
                    verification purposes.
                  </P>
                }
              />
            </View>
          </ScrollView>
        }
        modalContentStyle={{ height: "98%" }}
        extraModalStyle={{ height: "96%" }}
      />
      <BottomSheet
        isVisible={show4}
        onClose={() => setShow4(false)}
        showBackArrow={false}
        // statusBarTranslucent={false}
        components={
          // <FaceIdScreen
          //   navigation={navigation}
          //   next={() => setShow4(false)}
          //   skip={() => setShow4(false)}
          // />
          snappedPicture === "" ? (
            <View style={styles.pictureView}>
              <CameraView
                style={styles.camera}
                facing={isFrontCam ? "front" : "back"}
                mode="picture"
                ref={cameraRef}
              ></CameraView>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  marginTop: (48 / baseHeight) * height,
                }}
              >
                <TouchableOpacity onPress={takePicture}>
                  <View
                    style={[
                      styles.innerBtn,
                      {
                        width: 48,
                        height: 48,
                        marginRight: 16,
                      },
                    ]}
                  ></View>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.outerBtn}
                  onPress={() => {
                    setIsFrontCam(!isFrontCam);
                  }}
                >
                  <View style={styles.innerBtn}></View>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View style={styles.pictureView}>
              <Image src={snappedPicture} style={styles.camera} />
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  marginTop: 48,
                  width: "80%",
                  alignSelf: "center",
                  justifyContent: "space-around",
                }}
              >
                <TouchableOpacity onPress={() => setSnappedPicture("")}>
                  <P style={{ color: colors.white }}>Retry</P>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    setShow4(false);
                    setShow3(false);
                  }}
                >
                  <P style={{ color: "#fff" }}>Okay</P>
                </TouchableOpacity>
              </View>
            </View>
          )
        }
        headerStyle={{ borderBottomWidth: 0 }}
        componentHolderStyle={{ paddingLeft: 0, paddingRight: 0 }}
        modalContentStyle={{
          height: "100%",
          backgroundColor: "#000",
          borderRadius: 0,
          padding: 0,
        }}
        extraModalStyle={{ height: "53%" }}
      />
      <BottomSheet
        isVisible={show5}
        backspaceText={"Home country"}
        onClose={() => setShow5(false)}
        showBackArrow={false}
        components={
          <OnboardingCountrySelect
            onPress={(country) => {
              // Set flag using the country code
              setFlag2({
                uri: `https://flagcdn.com/w2560/${country.code.toLowerCase()}.png`,
              });
              // Set country and mobile code
              setCountry(country.name);
              setMobileCode(country.tel_code);
              setAlphaCode(country.alpha_code3);
              setAlpha2Code(country.code);
              // Clear ID method when country changes
              setCountryCode2("");

              // Close the modal
              setShow5(false);
            }}
          />
        }
        modalContentStyle={{ height: "80%" }}
        extraModalStyle={{ height: "77%" }}
        componentHolderStyle={{ flex: 1 }}
      />
      <BottomSheet
        isVisible={showdatePicker}
        backspaceText="Date of birth"
        onClose={() => setShowDatePicker(false)}
        showBackArrow={false}
        components={
          <DateOfBirthPicker
            selectedDate={selectedDate}
            onDateChange={(date) => {
              setSelectedDate(date);
            }}
            closeModal={() => {
              setShowDatePicker(false);
            }}
          />
        }
        modalContentStyle={{ height: "65%" }}
        extraModalStyle={{ height: "63%" }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentCard: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    marginTop: 24,
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 8,
    paddingRight: 8,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  amt: {
    fontSize: 20,
    // lineHeight: 48,
    fontFamily: fonts.poppinsSemibold,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginTop: 24,
    borderTopWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  section3Wrap: {
    width: "100%",
    marginTop: 24,
    borderColor: colors.stroke,
  },
  con: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: 264,
    alignSelf: "center",
    marginBottom: 32,
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: 16,
    marginBottom: 64,
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
    height: height,
  },
  headerText: {
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
    fontSize: 16,
  },
  text: {
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
    fontSize: 14,
    lineHeight: 22,
  },
  card: {
    width: 148 + 16,
    height: 85 + 16,
    borderColor: "#E6E5E5",
    borderWidth: 2,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 8,
  },
  complete: {
    marginTop: "5%",
    width: "80%",
    height: 50,
    backgroundColor: "#8B52FF",
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
  },
  pictureView: { width: "100%", height: "100%", alignItems: "center" },
  outerBtn: {
    width: 48,
    height: 48,
    borderRadius: 1000,
    // backgroundColor: "#FFFFFF55",
    borderWidth: 1,
    borderColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
  },
  innerBtn: {
    width: 24,
    height: 24,
    borderRadius: 1000,
    backgroundColor: colors.white,
  },
  camera: {
    width: "100%",
    height: "70%",
    // borderRadius: 100,
    // overflow:'hidden'
  },
  vText: {
    fontSize: 14,
    paddingTop: 10,
    paddingBottom: 10,
    paddingLeft: 16,
    paddingRight: 16,
    borderRadius: 8,
  },
  idSelect: {
    marginTop: 16,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  pinInput: {
    width: "35%",
    height: "100%",
    alignItems: "center",
    flexDirection: "row",
    borderRightColor: "#E6E5E5",
    borderRightWidth: 1,
  },
});
