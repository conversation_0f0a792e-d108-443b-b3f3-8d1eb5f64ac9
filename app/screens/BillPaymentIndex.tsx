import React from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../config/Fonts";
import Div from "../components/Div";
import AuthenticationHedear from "../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "../components/P";
import MicroBtn from "../components/MicroBtn";
import { colors } from "../config/colors";
import DetailCard from "../components/DetailCard";
import Button from "../components/Button";
import Content from "../components/Content";

const { width, height } = Dimensions.get("window");

export default function BillPaymentIndex({ navigation }) {
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Bill payment" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <Content
                svg1={svg.droplet}
                header="Water"
                body="SPay your water bills"
                onPress={() => navigation.navigate("WaterIndex")}
                />
              <Content
                svg1={svg.electricity}
                header="Electricity"
                body="Pay your electricity bills"
                onPress={() => navigation.navigate("ElecricityIndex")}
              />
              
              <Content
                svg1={svg.phone}
                header="Airtime"
                body="Pay your airtime bills"
                onPress={()=>navigation.navigate('AirtimeIndex')}
              />
              <Content
                svg1={svg.internet}
                header="Internet"
                body="Pay your internet bills"
                onPress={()=>navigation.navigate('InternetIndex')}
              />
              <Content
                svg1={svg.purple_school}
                header="Tuition"
                body="Coming Soon"
                bottomBorder={false}
                onPress={()=>console.log("coming soon")}
              />
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    height: 376,
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent:"center",
    alignItems: "center",
  },

  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
});
