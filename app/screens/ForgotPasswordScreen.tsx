import React, { useRef, useState, useEffect } from "react";
import { StyleSheet, View, Keyboard } from "react-native";
import Div from "../components/Div";
import { colors } from "../config/colors";
import P from "../components/P";
import { fonts } from "../config/Fonts";
import Button from "../components/Button";
import Link from "../components/Link";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import Input from "../components/Input";
import BottomComponent from "../components/BottomComponent";
import { Formik } from "formik";
import * as yup from "yup";
import { ForgotPassword } from "../RequestHandlers/Authentication";

import i18n from "../../i18n";
import { useToast } from "../context/ToastContext";
import H4 from "../components/H4";
import AuthHeader from "../components/AuthHeader";
import { withApiErrorToast } from "../Utils/withApiErrorToast";

export default function ForgotPasswordScreen({ navigation }) {
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();
  const EmailSchema = yup.object().shape({
    email: yup
      .string()
      .email(i18n.t("login.iea"))
      .required(i18n.t("login.eir")),
  });
  return (
    <View style={styles.container}>
      <Div>
        <AuthHeader style={{ width: "95%" }} navigation={navigation} />
        <Formik
          initialValues={{
            email: "",
          }}
          validationSchema={EmailSchema}
          onSubmit={async (values, actions) => {
            Keyboard.dismiss();
            setLoading(true);
            try {
              const forgotP = await withApiErrorToast(ForgotPassword(values), handleToast);
              if (forgotP.error) {
                handleToast(forgotP.message, "error");
              } else {
                setLoading(false);
                handleToast(forgotP.message, "success");
                setTimeout(() => {
                  navigation.navigate("VerifyEmailScreen", { email: values });
                }, 2000);
              }
            } catch (error) {
              handleToast("Network error", "error");
            } finally {
              setLoading(false);
            }
          }}
        >
          {(formikProps) => (
            <>
              <View
                style={{
                  width: "90%",
                  justifyContent: "center",
                  alignSelf: "center",
                  alignItems:"center"
                }}
              >
                <H4 style={styles.text1}>{i18n.t("login.fp")}</H4>
                <P style={styles.text2}>
                  {i18n.t("fp.text2")}
                  {""}
                  {i18n.t("fp.ea")}
                </P>
              </View>
              <View style={styles.components}>
                <View>
                  <Input
                    placeholder="<EMAIL>"
                    label={i18n.t("login.email")}
                    onChangeText={formikProps.handleChange("email")}
                    value={formikProps.values.email}
                    onBlur={formikProps.handleBlur("email")}
                    autoCapitalize="none"
                    error={
                      formikProps.errors.email && formikProps.touched.email
                    }
                    inputStyle={{ width: "85%" }}
                    // contStyle={{ marginBottom: 32 }}
                  />
                  {formikProps.errors.email && formikProps.touched.email && (
                    <P style={styles.errorText}>{formikProps.errors.email}</P>
                  )}
                </View>
                <View style={{ marginTop: 32 }}>
                  <Button
                    btnText={i18n.t("fp.cont")}
                    onPress={formikProps.handleSubmit}
                    loading={loading}
                  />
                </View>

                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "center",
                    marginTop: 32,
                  }}
                >
                  <P style={{ fontSize: 12, lineHeight: 22.4 }}>
                    {i18n.t("rp")}{" "}
                  </P>
                  <Link
                    style={{
                      fontSize: 12,
                      lineHeight: 21,
                      textDecorationLine: "underline",
                    }}
                    onPress={() => {
                      navigation.navigate("NewLoginScreen");
                    }}
                  >
                    {i18n.t("login.login")}
                  </Link>
                </View>
              </View>
            </>
          )}
        </Formik>
        <BottomComponent navigation={navigation} />
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  text1: {
    fontSize: 20,
    fontFamily: fonts.poppinsBold,
    marginTop: 20,
    lineHeight: 30,
  },
  text2: {
    fontSize: 14,
    lineHeight: 22.4,
    fontFamily: fonts.poppinsRegular,
  },
  components: {
    width: "90%",
    marginTop: 24,
    alignSelf: "center",
    // backgroundColor:"red"
  },
  bottomCont: {
    position: "absolute",
    bottom: 32,
    width: "100%",
    flexDirection: "row",
    // backgroundColor:"red"
  },
  bottomIcons: {
    width: 104,
    height: 22,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  bottomIconsText: {
    fontSize: 12,
    lineHeight: 22,
    color: "#A5A1A1",
    marginLeft: 4,
    textDecorationLine: "underline",
    textDecorationColor: "#A5A1A1",
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
});
