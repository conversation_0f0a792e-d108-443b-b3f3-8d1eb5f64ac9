import React, { useEffect, useState } from "react";
import { StyleSheet, View, Image, Dimensions, StatusBar } from "react-native";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Link from "../../components/Link";
import NoteComponent2 from "../../components/NoteComponent2";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import Constants from "expo-constants";
import Discover from "../Discover";
import Div from "../../components/Div";

const baseHeight = 800;
const { width, height } = Dimensions.get("window");
export default function SwapScreen({ navigation }) {
  const [isWaitListJoined, setIsWaitListJoined] = useState(false);
  const [stImg, setStImg] = useState(require("../../assets/clock.png"));
  const [stText, setStText] = useState("Sent money is pending");

  return (
    <View style={styles.body}>
      <Div>
        <View>
          <AuthenticationHedear
            text="Swap"
            navigation={navigation}
            contStyle={{ marginTop: 16 }}
          />
        </View>
        <View style={styles.itemBox}>
          <Image source={stImg} style={{ width: 64, height: 64 }} />
          <P style={styles.statusState}>Swap is coming soon</P>

          <P style={styles.stTx}>
            We’re excited to announce that our swap features will be launching
            soon!
          </P>
          {/* @ts-ignore */}
          {/* {!isWaitListJoined && (
            <>
              <P
                style={[styles.stTx, { marginTop: (32 / baseHeight) * height }]}
              >
                By clicking “notify me” you agree to be notify by SFx when the
                swap is ready
              </P>
              <View style={{ width: "75%", marginTop: 32 }}>
                <Button
                  btnText="Notify me"
                  onPress={() => {
                    setIsWaitListJoined(true);
                  }}
                />
                <Link
                  style={{ textAlign: "center", marginTop: 16, fontSize: 12 }}
                  onPress={() => navigation.navigate("Home")}
                >
                  Go back home
                </Link>
              </View>
            </>
          )} */}
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    backgroundColor: colors.white,
    alignItems: "center",
    flex: 1,
  },
  itemBox: {
    width: (90 * width) / 100,
    alignSelf: "center",
    alignItems: "center",
    marginTop: 120,
    // backgroundColor: "red",
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    width: "80%",
    fontSize: 12,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
});
