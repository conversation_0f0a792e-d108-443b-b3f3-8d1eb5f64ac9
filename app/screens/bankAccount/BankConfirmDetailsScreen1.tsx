import React, { useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import Content2 from "../../components/Content2";
import { GetUserWallet } from "../../RequestHandlers/Wallet";
import Loader from "../../components/ActivityIndicator";
import { countries } from "../../components/counties";
import { GetYellowCardEstimatedFee } from "../../RequestHandlers/Wallet";
import BottomSheet from "../../components/BottomSheet";

const { width, height } = Dimensions.get("window");

export default function BankConfirmDetailScreen1({ navigation, route }) {
  const {
    symbol,
    provider,
    currencyCode,
    channelID,
    networkID,
    ngnRate,
    inputValue,
    country,
    accNum,
    accName,
    note,
    isUsdInput,
    aplhaCode2,
    isManualInput,
  } = route?.params;
  const [showSendStatus, setShowSendStatus] = useState(false);
  const [selectedAcc, setSelectedAcc] = useState(0);
  const [loading, setLoading] = useState(false);
  const [fee, setFee] = useState<any>([]);
  const [gateWayError, setGateWayError] = useState(false);
  const [accounts, setAccounts] = useState([
    // {
    //   id: 1,
    //   balance: 0,
    //   currency: "USDT",
    //   exchangeRate: "1 USDT ~ 1 USD",
    //   type: "Tether",
    // },
    {
      id: null,
      balance: 0,
      currency: "USDC",
      exchangeRate: "1 USDC ~ 1 USD",
      type: "USDC",
    },
  ]);
  const formatNumber = (value) => {
    value = value?.toString();
    return (
      value?.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0
    );
  };
  const getFee = async () => {
    try {
      const res = await GetYellowCardEstimatedFee(
        aplhaCode2,
        inputValue,
        "bank",
        "WITHDRAW",
        isUsdInput ? "USD" : currencyCode
      );
      if (res.error) {
        setGateWayError(true);
      } else {
        setGateWayError(false);
        setFee(res);
      }
    } catch (error) {}
  };
  const getUserWallet = async () => {
    setLoading(true);
    try {
      const userWallet = await GetUserWallet();
      setAccounts([
        // {
        //   id: userWallet.wallets[0].id,
        //   balance: userWallet?.wallets[0]?.balance?.toFixed(2),
        //   currency: "USDT",
        //   exchangeRate: "1 USDT ~ 1 USD",
        //   type: "USDT",
        // },
        {
          id: userWallet.wallets[0].id,
          balance: userWallet.wallets[0].balance,
          currency: "USDC",
          exchangeRate: "1 USDC ~ 1 USD",
          type: "USDC",
        },
      ]);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const getCode2ByCountryName = (countryName) => {
    const country = countries.find((item) => item.country === countryName);
    return country ? country.code2 : null;
  };
  useEffect(() => {
    getUserWallet();

    if (inputValue) {
      getFee();
    }
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Transaction details"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <DetailCard
                headText={"Money you’re sending"}
                amount={
                  <>
                    <P style={{ fontSize: 32, lineHeight: 48, marginRight: 2 }}>
                      $
                      {formatNumber((fee?.amountInUSD + fee.fee)?.toFixed(2)) ||
                        0}
                    </P>
                    <P style={{ marginTop: 5 }}>USD</P>
                  </>
                }
                convertedAmount={
                  <>
                    <P style={{ fontSize: 16, lineHeight: 24, marginRight: 2 }}>
                      {symbol}
                      {formatNumber(fee?.localAmount?.toFixed(2)) || 0}
                    </P>
                    <P style={{ marginTop: 2, fontSize: 12, lineHeight: 18 }}>
                      {currencyCode}
                    </P>
                  </>
                }
                bottomComponent={
                  <View style={styles.desCont}>
                    <View style={styles.items}>
                      <P style={styles.holder}>Account number</P>
                      <P style={styles.value}>{accNum}</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Bank</P>
                      <View style={{ width: "60%" }}>
                        <P
                          style={[
                            styles.value,
                            { width: "100%", textAlign: "right" },
                          ]}
                        >
                          {provider}
                        </P>
                      </View>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Account name</P>
                      <View style={{ width: "60%" }}>
                        <P
                          style={[
                            styles.value,
                            { width: "100%", textAlign: "right" },
                          ]}
                        >
                          {accName}
                        </P>
                      </View>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Note</P>
                      <View style={{ width: "60%" }}>
                        <P
                          style={[
                            styles.value,
                            { width: "100%", textAlign: "right" },
                          ]}
                        >
                          {note}
                        </P>
                      </View>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Fee</P>
                      <P style={styles.value}>
                        {fee?.fee}{" "}
                        <P
                          // @ts-ignore
                          style={[
                            styles.value,
                            { fontFamily: fonts.poppinsRegular },
                          ]}
                        >
                          USD
                        </P>
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Exchange rate</P>
                      <P style={styles.value}>
                        {" "}
                        1 USD ~{ngnRate} {currencyCode}
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Payment method</P>
                      <P style={styles.value}>Send money</P>
                    </View>
                    <View style={[styles.items]}>
                      <P style={styles.holder}>Type</P>
                      <P style={styles.value}>Bank transfer</P>
                    </View>
                    {/* <View style={[styles.items, { marginBottom: 0 }]}>
                      <P style={styles.holder}>Earn SFx point</P>
                      <P style={styles.value}>$2</P>
                      </View> */}
                    {/* <View style={styles.line}></View>
                    <P
                      style={{
                        color: colors.gray,
                        fontSize: 12,
                        textAlign: "center",
                        marginBottom: 6,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      Select payment method
                    </P> */}
                    {/* <View
                      style={{
                        width: "100%",
                        flexDirection: "row",
                        justifyContent: "space-between",
                        marginBottom: 16,
                      }}
                    >
                      <P style={{ color: "#A5A1A1", fontSize: 12 }}>
                        SFx point
                      </P>
                      <View style={{ flexDirection: "row" }}>
                        <P style={{ marginRight: 8 }}>$50</P>
                        <CustomSwitch />
                      </View>
                    </View> */}
                    {/* {accounts.map((item, index) => (
                      <Content2
                      key={index}
                        svgg={item.currency == "USDT" ? svg.tather : svg.usdCoin}
                        onclick={index === selectedAcc}
                        ClickedMe={() => {
                          if (selectedAcc === index) {
                            setSelectedAcc(null);
                          } else if (item.balance > 0) {
                            setSelectedAcc(index);
                          }
                        }}
                        header={
                          <>
                            <P
                              style={{
                                fontSize: 12,
                                lineHeight: 18,
                                fontFamily: fonts.poppinsMedium,
                              }}
                            >
                              {item.balance}
                            </P>{" "}
                            <P
                              style={{
                                fontSize: 12,
                                lineHeight: 18,
                                fontFamily: fonts.poppinsRegular,
                                color: colors.gray,
                              }}
                            >
                              {item.currency}
                            </P>
                          </>
                        }
                        body={item.exchangeRate}
                        containerStyle={{
                          justifyContent: "flex-start",
                          paddingLeft: 16,
                          backgroundColor:
                            index === selectedAcc
                              ? colors.secBackground
                              : "transparent",
                        }}
                        itemWrapper={{ marginLeft: 8 }}
                        headerStyle={{ marginBottom: 4 }}
                        textStyle={{
                          fontFamily: fonts.poppinsRegular,
                          fontSize: 12,
                        }}
                        rightComponent={
                          item.balance === 0 && (
                            <Button
                              btnText="Add money"
                              btnTextStyle={{ color: colors.primary }}
                              type="alt"
                              style={{ width: "80%", height: "50%" }}
                            />
                          )
                        }
                      />
                    ))} */}
                  </View>
                }
              />
              <View style={styles.buttonWrap}>
                <Button
                  btnText="Confirm"
                  onPress={() => {
                    navigation.navigate("BankTransactionPin", {
                      channelID: channelID,
                      networkID: networkID,
                      note: note,
                      amount: inputValue,
                      country:
                        country.length > 2
                          ? getCode2ByCountryName(country)
                          : country,
                      // wallet: selectedAcc == 0 ? accounts[0] : accounts[1],
                      accName: accName,
                      currencyCode: currencyCode,
                      ngnRate: ngnRate,
                      accNum: accNum,
                      provider: provider,
                      type: "bank transfer",
                      isManualInput,
                      amountCurrency: isUsdInput? "USD" : currencyCode
                    });
                    // setShowSendStatus(true);
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
      {loading && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    // height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    marginBottom: 70,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
    marginTop: 8,
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.dGray,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  line: {
    width: "100%",
    height: 1,
    // backgroundColor: 'red',
    borderWidth: 1,
    borderStyle: "dashed",
    borderColor: colors.stroke,
    marginTop: (3.5 * height) / 100,
    marginBottom: (3.5 * height) / 100,
  },
});
