import React, { useState, useRef, useEffect } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { svg } from "../../config/Svg";
import { SvgXml } from "react-native-svg";
import NoteComponent from "../../components/NoteComponent";
import Input from "../../components/Input";
import BottomSheet from "../../components/BottomSheet";
import CountrySelect from "../../components/CountrySelect";
import Button from "../../components/Button";
import { GetChannels } from "../../RequestHandlers/Wallet";
import { NotBaCountires } from "../../components/NotBaCountirs";
import Loader from "../../components/ActivityIndicator";
const { width, height } = Dimensions.get("window");

export default function BankMobileMoneyScreen1({ navigation }) {
  const [paymentType, setPaymentType] = useState(null);
  const [flag, setFlag] = useState(require("../../assets/nigeria.png"));
  const [country, setCountry] = useState("");
  const [showCountries, setShowCountries] = useState(false);
  const countryRef = useRef<String | null>(null);
  const activeFlagRef = useRef<any | null>(null);
  const yellowCardRef = useRef<String | null>(null);
  const currencyCodeRef = useRef<String | null>(null);
  const symbolRef = useRef<String | null>(null);
  const [yellowCard, setYellowCard] = useState("");
  const [currencyCode, setCurrencyCode] = useState("");
  const [networkID, setNetworkID] = useState("");
  const [symbol, setSymbol] = useState("");
  const [loading, setLoading] = useState(false);
  const paymentTypes = [
    { name: "USD(Tether)", rate: "1 USDT ~ 1 USD", icon: svg.tather },
    // { name: "USD coin", rate: "1 USD ~ 1 USD", icon: svg.usdCoin },
  ];
  const handleActiveCountry = (newActiveType: string | null) => {
    setCountry(newActiveType);
  };
  const handleActiveFlag = (newActiveType: any | null) => {
    if (newActiveType) {
      setFlag(newActiveType);
    }
  };
  const handleActiveYellowCard = (newActiveType: string | null) => {
    setYellowCard(newActiveType);
  };
  const handleActiveCurrencyCode = (newActiveType: string | null) => {
    setCurrencyCode(newActiveType);
  };
  const handleActiveSymbol = (newActiveType: string | null) => {
    setSymbol(newActiveType);
  };
  useEffect(() => {
    countryRef.current = country;

  }, [country]);

  useEffect(() => {
    yellowCardRef.current = yellowCard;
  }, [yellowCard]);

  useEffect(() => {
    currencyCodeRef.current = currencyCode;
  }, [currencyCode]);
  useEffect(() => {
    symbolRef.current = symbol;
  }, [symbol]);

  useEffect(() => {
    activeFlagRef.current = flag;
  }, [flag]);

  useEffect(() => {
    if (!country) {
      setFlag(require("../../assets/nigeria.png"));
    }
  }, [country]);

  const getChannels = async (code) => {
    setLoading(true);
    try {
      const channels = await GetChannels(code);
      const result = channels.find(
        (item) =>
          item.rampType === "deposit" &&
          (item.channelType === "p2p" || item.channelType === "bank")
      );
      setNetworkID(result.id);
      if (channels) {
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
      ;
    }
  };

  useEffect(() => {
    if (yellowCard) {
      getChannels(yellowCard);
    }
  }, [yellowCard]);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Bank account" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <P
                style={{
                  fontSize: 12,
                  fontFamily: fonts.poppinsRegular,
                  marginBottom: 6,
                }}
              >
                Select account
              </P>
              {paymentTypes.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => setPaymentType(item.name)}
                >
                  <View
                    style={[
                      styles.pyItem,
                      {
                        borderColor:
                          paymentType === item.name
                            ? colors.primary
                            : colors.stroke,
                      },
                    ]}
                  >
                    <SvgXml xml={item.icon} />
                    <View style={{ marginLeft: 8 }}>
                      <P style={styles.pyName}>{item.name}</P>
                      <P style={styles.rate}>{item.rate}</P>
                    </View>
                    {paymentType === item.name && (
                      <SvgXml
                        xml={svg.ppTick}
                        style={{ position: "absolute", right: 16 }}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
              <NoteComponent text="Please note that your asset will be send as currency to payment bank" />
              {paymentType != null && (
                <TouchableOpacity
                  onPress={() => {
                    setShowCountries(true);
                  }}
                >
                  <Input
                    value={country}
                    label="Country"
                    placeholder="Turkey"
                    inputStyle={{ width: "65%", color: "#161817" }}
                    contStyle={{ marginTop: 16 }}
                    editable={false}
                    leftIcon={
                      <Image
                        source={flag}
                        style={{
                          width: 24,
                          height: 24,
                          marginLeft: 14,
                          objectFit: "cover",
                        }}
                      />
                      //   <View>
                      //   </View>
                    }
                    rightIcon={
                      <View
                        style={{
                          //   backgroundColor: "red",
                          width: "15%",
                          height: "100%",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <SvgXml xml={svg.dropDown} />
                      </View>
                    }
                  />
                </TouchableOpacity>
              )}
            </View>
            <View style={{ width: "80%", alignSelf: "center", marginTop: 32 }}>
              <Button
                btnText="Next"
                onPress={() => navigation.navigate("BankMobileMoneyScreen2")}
                disabled={paymentType === null || country === ""}
              />
            </View>
          </View>
        </ScrollView>
      </Div>
      <BottomSheet
        isVisible={showCountries}
        showBackArrow={false}
        backspaceText="Select country"
        onClose={() => setShowCountries(false)}
        modalContentStyle={{ height: "65%" }}
        extraModalStyle={{ height: "63%" }}
        components={
          <CountrySelect
            excludedCountries={NotBaCountires}
            onActiveCountryChange={handleActiveCountry}
            onActiveFlag={handleActiveFlag}
            onActiveYellowCard={handleActiveYellowCard}
            onActiveCurrencyCode={handleActiveCurrencyCode}
            onSymbolChange={handleActiveSymbol}
            onPress={() => {
              setShowCountries(false);
            }}
          />
        }
      />
      <Loader loading={true} visible={loading} />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: "100%",
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    padding: 24,
  },
  pyItem: {
    width: "100%",
    borderRadius: 6,
    borderWidth: 1,
    marginBottom: 16,
    alignItems: "center",
    flexDirection: "row",
    padding: 16,
  },
  pyName: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
  },
  rate: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
  },
  noteCont: {
    width: "100%",
    padding: 16,
    paddingTop: 8,
    paddingBottom: 8,
    borderRadius: 8,
  },
});
