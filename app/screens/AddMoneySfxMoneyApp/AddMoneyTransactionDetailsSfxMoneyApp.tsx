import React, { useState, useEffect, useRef } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Alert,
  Share,
} from "react-native";
import { colors } from "../../config/colors";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import QRCode from "react-native-qrcode-svg";
import NoteComponent2 from "../../components/NoteComponent2";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import * as Clipboard from "expo-clipboard";
import Button from "../../components/Button";
import Link from "../../components/Link";
import ViewShot from "react-native-view-shot";
import * as FileSystem from 'expo-file-system';

const { width, height } = Dimensions.get("window");

export default function AddMoneyTransactionDetailsSfxMoneyApp({
  navigation,
  route,
}) {
  const [isRefCopied, setIsRefCopied] = useState(false);
  const { accName, accNum } = route?.params;
  const viewShotRef = useRef(null);

  const copyAccNum = async () => {
    await Clipboard.setStringAsync(accNum);
    setIsRefCopied(true);
    setTimeout(() => {
      setIsRefCopied(false);
    }, 4000);
  };
  
  const captureAndDownload = async () => {
    if (!viewShotRef.current) {
      Alert.alert("Error", "Unable to capture receipt.");
      return;
    }
    try {
      // Capture the view
      const uri = await viewShotRef.current.capture();
  
      // Define file path in app's document directory
      const fileUri = FileSystem.documentDirectory + 'receipt.png';
  
      // Copy the captured image to the file system
      await FileSystem.copyAsync({
        from: uri,
        to: fileUri,
      });
  
      // Share the file using React Native's Share API
      await Share.share({
        message: 'Here is your receipt!',
        url: fileUri, // Pass the file URI
        title: 'Download Receipt',
      });
  
      // handleToast("Details downloaded", "success");
    } catch (error) {
      console.error("Error saving or sharing file", error);
      Alert.alert("Error", "Failed to save receipt.");
    }
  };
  
  
  const captureAndShare = async () => {
    if (!viewShotRef.current) {
      Alert.alert("Error", "Unable to capture receipt.");
      return;
    }
  
    try {
      // Capture the view
      const uri = await viewShotRef.current.capture();
  
      // Define file path in app's document directory
      const fileUri = FileSystem.documentDirectory + 'receipt.png';
  
      // Copy the captured image to the file system
      await FileSystem.copyAsync({
        from: uri,
        to: fileUri,
      });
      // Share using React Native's Share API
      await Share.share({
        message: 'Here are my transaction details!',
        url: fileUri, // Share the image using its URI
        title: 'Share Receipt',
      });
      // handleToast("Receipt captured and shared", "success");
    } catch (error) {
      console.error("Error capturing or sharing file", error);
      Alert.alert("Error", "Failed to capture or share receipt.");
    }
  };
  
  
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Transaction details"
          navigation={navigation}
        />
        <ScrollView
          contentContainerStyle={{ paddingBottom: "20%" }}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.body}>
            <ViewShot
              ref={viewShotRef}
              options={{ format: "jpg", quality: 0.9 }}
              style={{ width: "100%", alignItems: "center" }}
            >
              <View style={styles.detailWrap}>
                <P
                  style={{
                    fontSize: 12,
                    lineHeight: 19.5,
                    color: colors.gray,
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  Send money to SFx money app
                </P>
                <P style={{ marginBottom: 16 }}>Scan QR code to make payment</P>
                <QRCode
                  value={accNum}
                  size={(23 * height) / 100}
                  color="black"
                  logo={require("../../assets/qrSfx.png")}
                  logoSize={40}
                  logoBorderRadius={5}
                  backgroundColor="white"
                />
                <View style={styles.line}></View>
                <NoteComponent2 text="Others can scan with their SFx Money app to send you money" />
                <View
                  style={{ width: "100%", marginTop: (3.5 * height) / 100 }}
                >
                  <View style={styles.items}>
                    <SvgXml xml={svg.userSquare} style={{ marginRight: 8 }} />
                    <View>
                      <P style={styles.holder}>Account name</P>
                      <P style={styles.value}>{accName}</P>
                    </View>
                  </View>
                  <View style={styles.items}>
                    <SvgXml xml={svg.bank} style={{ marginRight: 8 }} />
                    <View>
                      <P style={styles.holder}>Bank</P>
                      <P style={styles.value}>SFx money app</P>
                    </View>
                  </View>
                  <View style={[styles.items, { marginBottom: 0 }]}>
                    <SvgXml xml={svg.userBrown} style={{ marginRight: 8 }} />
                    <View>
                      <P style={styles.holder}>Username</P>
                      <P style={styles.value}>{accNum}</P>
                    </View>
                  </View>
                </View>
              </View>
            </ViewShot>
            <View style={{ width: "90%", alignSelf: "center" }}>
              <View
                style={{
                  width: "80%",
                  alignSelf: "center",
                  marginTop: (4 * height) / 100,
                }}
              >
                <Button btnText="Share details" onPress={captureAndShare} />
                <View
                  style={{
                    flexDirection: "row",
                    width: "100%",
                    alignItems: "center",
                    justifyContent: "center",
                    marginTop: 26,
                  }}
                >
                  <SvgXml xml={svg.download} style={{ marginRight: 4 }} />
                  <Link
                    style={{ fontSize: 12, lineHeight: 24 }}
                    onPress={captureAndDownload}
                  >
                    Download receipt
                  </Link>
                </View>
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  detailWrap: {
    padding: (3.5 * height) / 100,
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  line: {
    width: "100%",
    marginTop: (3.5 * height) / 100,
    marginBottom: (3.5 * height) / 100,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    marginBottom: 4,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  items: {
    width: "100%",
    flexDirection: "row",
    padding: 8,
    alignItems: "center",
    marginBottom: 8,
  },
});
