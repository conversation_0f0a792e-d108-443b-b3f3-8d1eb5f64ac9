import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { colors } from "../../config/colors";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import QRCode from "react-native-qrcode-svg";
import NoteComponent from "../../components/NoteComponent";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import * as Clipboard from "expo-clipboard";
import Button from "../../components/Button";
import Link from "../../components/Link";
import NoteComponent2 from "../../components/NoteComponent2";
import { GetUserDetails } from "../../RequestHandlers/User";
import { useFocusEffect } from "@react-navigation/native";
import { WalletDetailsSkeleton } from "../../Skeletons/Skeletons";
import ViewShot from "react-native-view-shot";
import * as Sharing from "expo-sharing";
import { useToast } from "../../context/ToastContext";
import Loader from "../../components/ActivityIndicator";

const { width, height } = Dimensions.get("window");
const valueToEncode = "https://www.sfxchange.co/en";

export default function AddMoneyAccountScreen({ navigation }) {
  const [isRefCopied, setIsRefCopied] = useState(false);
  const [loading, setLoading] = useState(false);
  const [accNum, setAccNum] = useState("...");
  const [accName, setAccName] = useState("...");
  const viewShotRef = useRef(null);
  const { handleToast } = useToast();
  const [details, setDetails] = useState<any>([]);

  const accDetails = `${details.username}`;
  const copyAccNum = async () => {
    const copiedText = await Clipboard.setStringAsync(accDetails);
    if (copiedText === true) {
      setIsRefCopied(true);
      setTimeout(() => {
        setIsRefCopied(false);
      }, 4000);
    }
  };

  const getUserDetails = async () => {
    try {
      const userDetails = await GetUserDetails();
      if (userDetails.error) {
        handleToast(userDetails.message, "error");
      } else {
        setAccNum(userDetails.username);
        setAccName(`${userDetails?.firstName}${userDetails?.middleName ? ` ${userDetails?.middleName}` : ""} ${userDetails?.lastName}`);
        setDetails(userDetails);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      setLoading(true);
      getUserDetails();
    }, [])
  );
  const captureAndShare = async () => {
    try {
      // @ts-ignore
      const uri = await viewShotRef.current.capture();
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri);
      } else {
        alert("Sharing is not available on this device");
      }
    } catch (error) {
      console.error("Error capturing and sharing view:", error);
    }
  };
  // @t
  return (
    <View style={styles.body}>
      <Div>
        {loading ? (
          <Loader />
        ) : (
          <>
            <AuthenticationHedear
              text="SFx money app"
              navigation={navigation}
            />
            <ScrollView
              contentContainerStyle={{ paddingBottom: "15%" }}
              showsVerticalScrollIndicator={false}
            >
              <View style={styles.body}>
                <ViewShot
                  ref={viewShotRef}
                  options={{ format: "jpg", quality: 0.9 }}
                  style={styles.detailWrap}
                >
                  <P
                    style={{
                      fontSize: 12,
                      lineHeight: 19.5,
                      color: colors.gray,
                      fontFamily: fonts.poppinsRegular,
                    }}
                  >
                    Send money to SFx money app
                  </P>
                  <P style={{ marginBottom: 16 }}>
                    Scan QR code to make payment
                  </P>
                  <QRCode
                    value={accNum}
                    size={(23 * height) / 100}
                    color="black"
                    logo={require("../../assets/qrSfx.png")}
                    logoSize={40}
                    logoBorderRadius={5} // Ensures logo has rounded corners
                    backgroundColor="white"
                  />
                  <View style={styles.line}></View>
                  <NoteComponent2 text="Please note that you will receive USD in your USDC account." />
                  <View
                    style={{ width: "100%", marginTop: 16 }}
                  >
                    <View style={styles.items}>
                      <SvgXml xml={svg.userSquare} style={{ marginRight: 8 }} />
                      <View>
                        <P style={styles.holder}>Account name</P>
                        <P style={styles.value}>{accName}</P>
                      </View>
                    </View>
                    <View style={styles.items}>
                      <SvgXml xml={svg.bank} style={{ marginRight: 8 }} />
                      <View>
                        <P style={styles.holder}>Bank</P>
                        <P style={styles.value}>SFx money app</P>
                      </View>
                    </View>
                    <View style={[styles.items, { marginBottom: 0 }]}>
                      <SvgXml xml={svg.userBrown} style={{ marginRight: 8 }} />
                      <View>
                        <P style={styles.holder}>Username</P>
                        <TouchableOpacity
                          onPress={copyAccNum}
                        >
                          <P style={styles.value}>{accNum}</P>
                        </TouchableOpacity>
                      </View>
                      <TouchableOpacity
                        onPress={copyAccNum}
                        style={styles.copyBtn}
                      >
                        <View style={styles.copyBtn}>
                          <P style={styles.copyText}>
                            {isRefCopied ? "Copied" : "Copy"}
                          </P>

                          <SvgXml
                            xml={isRefCopied ? svg.circleSuccess : svg.copy}
                            style={{ width: 14, height: 14 }}
                          />
                        </View>
                      </TouchableOpacity>
                    </View>
                  </View>
                </ViewShot>
                <View
                  style={{
                    width: "90%",
                    alignSelf: "center",
                    paddingTop: (2 * height) / 100,
                  }}
                >
                  <P
                    style={{
                      fontSize: 12,
                      color: colors.gray,
                      lineHeight: 18,
                      fontFamily: fonts.poppinsRegular,
                      textAlign: "center",
                    }}
                  >
                    Don't disclosed until payment is made
                  </P>
                  <View
                    style={{
                      width: "80%",
                      alignSelf: "center",
                      marginTop: 24 ,
                    }}
                  >
                    <Button btnText="Done" onPress={() => navigation.pop(3)} />
                    <Link
                      style={{
                        textAlign: "center",
                        marginTop: (2 * height) / 100,
                        fontSize: 12,
                        lineHeight: 24,
                      }}
                      onPress={() => {
                        captureAndShare();
                      }}
                    >
                      Share details
                    </Link>
                  </View>
                </View>
              </View>
            </ScrollView>
          </>
        )}
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: (3.5 * height) / 100,
    paddingBottom: (3.5 * height) / 100,
    alignItems: "center",
  },
  detailWrap: {
    padding: (3.5 * height) / 100,
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  line: {
    width: "100%",
    marginTop: 16,
    marginBottom: 16,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    marginBottom: 4,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  items: {
    width: "100%",
    flexDirection: "row",
    padding: 8,
    alignItems: "center",
    // marginBottom: 8,
  },
  copyBtn: {
    paddingTop: 4,
    paddingBottom: 4,
    padding: 13,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
  },
});
