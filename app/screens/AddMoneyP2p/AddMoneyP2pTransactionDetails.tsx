import React, { useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { colors } from "../../config/colors";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import QRCode from "react-native-qrcode-svg";
import NoteComponent from "../../components/NoteComponent";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import * as Clipboard from "expo-clipboard";
import Button from "../../components/Button";
import Link from "../../components/Link";
import NoteComponent2 from "../../components/NoteComponent2";

const { width, height } = Dimensions.get("window");
const valueToEncode = "https://www.sfxchange.co/en";

export default function AddMoneyP2pTransactionDetails({ navigation }) {
  const [isRefCopied, setIsRefCopied] = useState(false);
  const [wallAdd, setWallAdd] = useState("0x435068*****C04F913");
  const copyAccNum = async () => {
    const copiedText = await Clipboard.setStringAsync(wallAdd);
  
    if (copiedText === true) {
      setIsRefCopied(true);
      setTimeout(() => {
        setIsRefCopied(false);
      }, 4000);
    }
  };
  // @t
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="P2P transfer" navigation={navigation} />
        <ScrollView>
          <View style={styles.body}>
            <View style={styles.detailWrap}>
              <P
                style={{
                  fontSize: 12,
                  lineHeight: 19.5,
                  color: colors.gray,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Send money to SFx money app
              </P>
              <P style={{ marginBottom: 16 }}>Scan QR code to make payment</P>
              <QRCode
                value={valueToEncode}
                size={(23 * height) / 100}
                color="black"
                logo={require("../../assets/tether.png")}
                logoSize={40}
                logoBorderRadius={5} // Ensures logo has rounded corners
                backgroundColor="white"
              />
              <P
                style={{
                  padding: 8,
                  paddingTop: 4,
                  paddingBottom: 4,
                  backgroundColor: colors.secBackground,
                  borderRadius: 99,
                  marginTop: (2 * height) / 100,
                  fontSize: 10,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Network arrival in
                <P
                  style={{
                    fontSize: 10,
                    color: colors.primary,
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  {" "}
                  3 minutes
                </P>
              </P>
              <View style={styles.line}></View>
              <NoteComponent2 text="Please note that minimum deposit is 1 USDT. The current address only supports adding USDT on Celo, adding other assets will result in loss" />
              <View style={{ width: "100%", marginTop: (3.5 * height) / 100 }}>
                <View style={[styles.items, { marginBottom: 0 }]}>
                  <SvgXml xml={svg.wallet2} style={{ marginRight: 8 }} />
                  <View>
                    <P style={styles.holder}>Wallet address</P>
                    <P style={styles.value}>{wallAdd}</P>
                  </View>
                  <TouchableOpacity onPress={copyAccNum} style={styles.copyBtn}>
                    <View style={styles.copyBtn}>
                      <P style={styles.copyText}>
                        {isRefCopied ? "Copied" : "Copy"}
                      </P>

                      <SvgXml
                        xml={isRefCopied ? svg.circleSuccess : svg.copy}
                        style={{ width: 14, height: 14 }}
                      />
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            <View
              style={{
                width: "90%",
                alignSelf: "center",
                paddingTop: (2 * height) / 100,
              }}
            >
              <View
                style={{
                  width: "80%",
                  alignSelf: "center",
                  marginTop: (2.5 * height) / 100,
                }}
              >
                <Button btnText="Share details" />
                <View
                  style={{
                    flexDirection: "row",
                    width: "100%",
                    alignItems: "center",
                    justifyContent: "center",
                    marginTop: 26,
                  }}
                >
                  <SvgXml xml={svg.download} style={{ marginRight: 4 }} />
                  <Link style={{ fontSize: 12, lineHeight: 24 }}>
                    Download receipt
                  </Link>
                </View>
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: (3.5 * height) / 100,
    paddingBottom: (3.5 * height) / 100,
    alignItems: "center",
  },
  detailWrap: {
    padding: (3.5 * height) / 100,
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  line: {
    width: "100%",
    marginTop: (3.5 * height) / 100,
    marginBottom: (3.5 * height) / 100,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    marginBottom: 4,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  items: {
    width: "100%",
    flexDirection: "row",
    padding: 8,
    alignItems: "center",
    marginBottom: 8,
  },
  copyBtn: {
    paddingTop: 4,
    paddingBottom: 4,
    padding: 13,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
  },
});
