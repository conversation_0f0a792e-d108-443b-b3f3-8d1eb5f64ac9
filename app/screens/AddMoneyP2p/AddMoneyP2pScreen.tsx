import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { svg } from "../../config/Svg";
import { SvgXml } from "react-native-svg";
import NoteComponent from "../../components/NoteComponent";
import Input from "../../components/Input";
import BottomSheet from "../../components/BottomSheet";
import CountrySelect from "../../components/CountrySelect";
import Button from "../../components/Button";
import NoteComponent2 from "../../components/NoteComponent2";
import { GetUserWallet, GetWalletById } from "../../RequestHandlers/Wallet";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../../components/ActivityIndicator";
import { useToast } from "../../context/ToastContext";
const { width, height } = Dimensions.get("window");

export default function AddMoneyP2pScreen({ navigation, route }) {
  const [paymentType, setPaymentType] = useState(null);
  const { asset } = route.params || "";
  const [flag, setFlag] = useState(require("../../assets/turkey.png"));
  const [country, setCountry] = useState("");
  const [showCountries, setShowCountries] = useState(false);
  const [optionDisabled, setOptionDi] = useState(false);
  const activeFlagRef = useRef<any | null>(null);
  const countryRef = useRef<String | null>(null);
  const [activeNetwork, setActiveNetwork] = useState(null);
  const [displayArrivalTime, setDisplayArrivalTime] = useState(null);
  const [wallets, setWallets] = useState([]);
  const [network, setNework] = useState([]);
  const [loader, setLoader] = useState(false);
  const { handleToast } = useToast();
  const paymentTypes = [
    { name: "Tether", rate: "1 USDT ~ 1 USD", icon: svg.tather },
    { name: "USD coin", rate: "1 USD ~ 1 USD", icon: svg.usdCoin },
    { name: "Bitcoin", rate: "Coming soon..", icon: svg.bitCoin },
  ];
  const networks = [
    { name: "Celo", arrival: "Money arrival in 3 minutes " },
    { name: "Tron-TRC 20", arrival: "Money arrival in 5 minutes" },
    { name: "Polygon", arrival: "Coming soon" },
    { name: "BSC", arrival: "Coming soon" },
  ];
  const handleActiveCountry = (newActiveType: string | null) => {
    setCountry(newActiveType);
  };
  const handleActiveFlag = (newActiveType: any | null) => {
    if (newActiveType) {
      setFlag(newActiveType);
    }
  };
  useEffect(() => {
    countryRef.current = country;
  }, [country]);

  useEffect(() => {
    activeFlagRef.current = flag;
  }, [flag]);
  useEffect(() => {
    if (!country) {
      setFlag(require("../../assets/turkey.png"));
    }
  }, [country]);
  const getWallet = async () => {
    setLoader(true);
    try {
      const wallets = await GetUserWallet();
      if (wallets.wallets) {
        setWallets(wallets.wallets);
        setPaymentType(wallets.wallets[0].id)
      } else {
        handleToast("Error fetching details", "error");
      }
    } catch (error) {}finally{
      setLoader(false);
    }
  };

  const getWalletBId = async () => {
    try {
      const wall = await GetWalletById(paymentType);
      setNework(wall.wallet.assets);
    } catch (error) {}
  };

  useEffect(() => {
    getWalletBId();
  }, [paymentType]);

  useFocusEffect(
    useCallback(() => {
      getWallet();
      if (asset != "" && asset != undefined && asset != null) {
        setPaymentType(asset.id);
      }
    }, [])
  );

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Wallet address" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <P
                style={{
                  fontSize: 12,
                  fontFamily: fonts.poppinsRegular,
                  marginBottom: 6,
                }}
              >
                Select account
              </P>
              {wallets.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => setPaymentType(item.id)}
                >
                  <View
                    style={[
                      styles.pyItem,
                      {
                        borderColor:
                          paymentType === item.id
                            ? colors.primary
                            : colors.stroke,
                        borderWidth: item.rate == "Coming soon.." ? 0 : 1,
                        marginTop: item.rate == "Coming soon.." ? -16 : 0,
                        marginBottom: index == paymentTypes.length - 1 ? 0 : 16,
                      },
                    ]}
                  >
                    {paymentType === item.id && (
                      <SvgXml
                        xml={svg.checked}
                        style={{ position: "absolute", right: 16 }}
                      />
                    )}
                    <SvgXml
                      xml={item.asset === "USDT" ? svg.tather : svg.usdCoin}
                    />
                    <View style={{ marginLeft: 8 }}>
                      <P style={styles.pyName}>
                        {item.asset === "USDT" ? "Tether" : "USD Coin"}
                      </P>
                      <P style={styles.rate}>
                        {item.asset === "USDT"
                          ? "1 USDT ~ 1 USD"
                          : "1 USD ~ 1 USD"}
                      </P>
                    </View>
                    {paymentType === item.name && (
                      <SvgXml
                        xml={svg.ppTick}
                        style={{ position: "absolute", right: 16 }}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
              {/* {paymentType != null && (
                <TouchableOpacity
                  onPress={() => {
                    setShowCountries(true);
                  }}
                  disabled={network.length === 1}
                >
                  <Input
                    value={
                      network[0]?.chain === "CHAIN_TRON"
                        ? "Tron"
                        : network[0]?.chain === "CHAIN_CELO"
                        ? "Celo"
                        : "..."
                    }
                    label="Network"
                    placeholder="Celo"
                    inputStyle={{ width: "60%", color: "#161817" }}
                    contStyle={{ marginTop: 16 }}
                    editable={false}
                    rightIcon={
                      network.length > 1 && (
                        <View
                          style={{
                            // backgroundColor: "red",
                            width: "15%",
                            height: "100%",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <SvgXml xml={svg.dropDown} />
                        </View>
                      )
                    }
                  />
                </TouchableOpacity>
              )} */}

              {displayArrivalTime && (
                <View style={{ marginTop: 6 }}>
                  <NoteComponent2 text={displayArrivalTime} />
                </View>
              )}
            </View>
            <View style={{ width: "80%", alignSelf: "center", marginTop: 32 }}>
              <Button
                btnText="Next"
                onPress={() =>
                  navigation.navigate("AddMoneyP2pAccountScreen", {
                    wallet: paymentType,
                  })
                }
                disabled={paymentType === null}
              />
            </View>
          </View>
        </ScrollView>
      </Div>
      <BottomSheet
        isVisible={showCountries}
        showBackArrow={false}
        backspaceText="Network"
        onClose={() => setShowCountries(false)}
        modalContentStyle={{ height: "65%" }}
        extraModalStyle={{ height: "63%" }}
        components={
          <ScrollView>
            <View style={{ paddingTop: 24 }}>
              <P
                style={{
                  fontSize: 12,
                  color: colors.gray,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Select a network from assets to send as money
              </P>
              <View>
                {networks.map((item, index) => {
                  return (
                    <TouchableOpacity
                      key={index}
                      disabled={item.arrival.includes("Coming soon")}
                      onPress={() => {
                        setActiveNetwork(item.name);
                        setCountry(item.name);
                        setDisplayArrivalTime(item.arrival);
                        setTimeout(() => {
                          setShowCountries(false);
                        }, 1000);
                      }}
                    >
                      <View
                        style={{
                          width: "100%",
                          padding: 16,
                          paddingTop: 8,
                          paddingBottom: 8,
                          borderRadius: 8,
                          marginBottom: 16,
                          marginTop: 6,
                          backgroundColor:
                            activeNetwork === item.name
                              ? colors.lowOpPrimary2
                              : "transparent",
                        }}
                      >
                        <P style={{ fontSize: 12, lineHeight: 18 }}>
                          {item.name}
                        </P>
                        <P
                          style={{
                            fontSize: 12,
                            color: colors.gray,
                            fontFamily: fonts.poppinsRegular,
                          }}
                        >
                          {item.arrival}
                        </P>
                      </View>
                    </TouchableOpacity>
                  );
                })}
                <NoteComponent2 text="Please note that minimum payment is 0.000001 USDT is supported assets on SFx money app, if you send asset to different network your assets will be lost" />
              </View>
            </View>
          </ScrollView>
        }
      />
      {loader && <Loader visible={loader} loading={true} />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: "100%",
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    padding: 24,
  },
  pyItem: {
    width: "100%",
    borderRadius: 6,
    borderWidth: 1,
    marginBottom: 16,
    alignItems: "center",
    flexDirection: "row",
    padding: 16,
  },
  pyName: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
  },
  rate: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
  },
  noteCont: {
    width: "100%",
    padding: 16,
    paddingTop: 8,
    paddingBottom: 8,
    borderRadius: 8,
  },
});
