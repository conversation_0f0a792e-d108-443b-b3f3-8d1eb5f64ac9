import React, { useContext, useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
  Platform,
  Linking,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import SendMoneyStatus from "../../components/SeendMoneyStatus";
import Link from "../../components/Link";
import {
  GetRateByCountry,
  GetTransationById,
} from "../../RequestHandlers/Wallet";
import * as Clipboard from "expo-clipboard";
import { formatDate } from "../../components/FormatDate";
import { countries } from "../../components/counties";
import { GetRateById } from "../../RequestHandlers/Wallet";
import Loader from "../../components/ActivityIndicator";
import { ensureHttps } from "../../components/AddHttp";
import FailedToLoad from "../../components/ErrorSate/FailedToLoad";
import { useToast } from "../../context/ToastContext";
import { CredentailsContext } from "../../RequestHandlers/CredentailsContext";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import {
  formatNumberWithCommas,
  formatToTwoDecimals,
} from "../../Utils/numberFormat";
import DashedLine from "../../components/DashedLine";
import DashedBorderView from "../../components/DashedBorderView";

const { width, height } = Dimensions.get("window");

export default function AllRTransactionsDetails({ navigation, route }) {
  const [showSendStatus, setShowSendStatus] = useState(false);
  const { handleToast } = useToast();
  const [tranStat, setTranStat] = useState("pending");
  const { transactionType, id } = route.params || "";
  const [resData, setResData] = useState<any>([]);
  const [loader, setLoader] = useState(false);
  const [yellowCardCode, setyellowCardCode] = useState("");
  const [curDetails, setCurDetails] = useState<any>([]);
  const [yData, setYData] = useState<any>([]);
  const [symbol, setSymbol] = useState("");
  const [isDataLaodFailed, setIsDataLoadFailed] = useState(false);
  const { storedCredentails } = useContext(CredentailsContext);

  const copyAccNum = async (accNum) => {
    const copiedText = await Clipboard.setStringAsync(accNum);
    if (copiedText === true) {
      handleToast("Code copied to clipboard", "success");
    } else {
      handleToast("Error copying code", "error");
    }
  };
  const transaction = async () => {
    setLoader(true);
    try {
      const res = await withApiErrorToast(GetTransationById(id), handleToast);
      if (res.error) {
        handleToast("Error fetching details", "error");
        setIsDataLoadFailed(true);
      } else {
        setIsDataLoadFailed(false);
      }
      if (res.transaction) {
        setResData(res?.transaction);
      }
      if (res.yellowCardData) {
        setYData(res?.yellowCardData);
        setSymbol(getSymbol(res.yellowCardData.currency));
        getRateById(res?.yellowCardData?.currency);
      }
      setyellowCardCode(res?.transaction?.internalTransferSender?.homeCountry);
      if (res?.transaction?.internalTransferSender) {
        const yc = getYellowCardCode(res?.transaction?.user?.homeCountry);
        getRateById(yc);
      }
      const yc = getYellowCardCode(
        res?.transaction?.internalTransferSender?.homeCountry
      );
      // getRateById(yc);
      if (res?.transaction?.status === "completed") {
        setTranStat("Successful");
      } else if (res?.transaction?.status === "failed") {
        setTranStat("Failed");
      } else {
        setTranStat("Pending");
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setLoader(false);
    }
  };
  const getYellowCardCode = (countryName) => {
    if (countryName === "Turkey" || countryName === "North Cyprus") {
      return "TRY";
    }
    const country = countries.find((item) => item.country === countryName);
    return country ? country.currencyCode : "Country not found";
  };

  const getSymbol = (symbol) => {
    if (symbol === "TRY") {
      return "₺";
    }
    const curSymbol = countries.find((item) => item.currencyCode === symbol);
    return curSymbol ? curSymbol.symbol : "$";
  };
  const yc = getYellowCardCode(yellowCardCode);
  const getRateById = async (yc) => {
    setLoader(true);
    const provider =
      yc === "NGN" ? "link" : yc === "TRY" ? "tcmb" : "yellow-card";
    try {
      // const rate = await GetRateById(yc);
      const sfxRate = await GetRateByCountry(yc, provider);
      sfxRate.map((item, index) => {
        if (item.type === "buy") {
          setCurDetails(item);
        }
      });
      if (sfxRate) {
        setLoader(false);
      }
    } catch (error) {}
  };
  const formatNetwork = (text) => {
    const chainNameMapping = {
      CHAIN_POLYGON: "Polygon",
      CHAIN_AVALANCHE: "Avalanche - C Chain",
      CHAIN_ARBITRUM: "Arbitrum One",
      CHAIN_BASE: "Base",
      CHAIN_SOLANA: "Solana",
    };
    // Check if the text exists in the chainNameMapping
    if (chainNameMapping[text]) {
      return chainNameMapping[text];
    }
    // Fallback to original logic if not found in mapping
    const chain = text.split("_");
    return chain[1];
  };
  const copyHashNum = async (accNum) => {
    const copiedText = await Clipboard.setStringAsync(accNum);
    if (copiedText === true) {
      handleToast("Code copied to clipboard", "success");
    } else {
      handleToast("Error copying code", "error");
    }
  };
  function truncateTransactionHash(hash, startLength = 6, endLength = 4) {
    if (!hash || hash.length <= startLength + endLength) {
      return hash; // Return the full hash if it's too short to truncate
    }
    return (
      `${hash.slice(0, startLength)}...${hash.slice(-endLength)}` || "......"
    );
  }

  useEffect(() => {
    transaction();
  }, []);

  if (loader) {
    return <Loader />;
  }
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Transaction details"
          navigation={navigation}
        />
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 50 }}
        >
          {isDataLaodFailed ? (
            <>
              <FailedToLoad
                onPress={() => {
                  setLoader(true);
                  transaction();
                }}
              />
            </>
          ) : (
            <>
              <View style={styles.contentBody}>
                <View style={styles.detailWrap}>
                  {transactionType === "sfx money app" ? (
                    <>
                      <Image
                        source={
                          // @ts-ignore
                          resData?.internalTransferSender
                            ? // @ts-ignore
                              {
                                uri: ensureHttps(
                                  resData?.internalTransferSender?.picture
                                ),
                              }
                            : require("../../assets/face.png")
                        }
                        style={{
                          position: "absolute",
                          zIndex: 10,
                          width: 42,
                          height: 42,
                          alignSelf: "center",
                          borderRadius: 100,
                          top: -15,
                        }}
                      />
                      <DetailCard
                        amount={
                          <>
                            <P
                              style={{
                                fontSize: 24,
                                lineHeight: 36,
                                marginRight: 2,
                              }}
                            >
                              {resData?.sfxToSfxCurrency &&
                              resData?.sfxToSfxCurrency !== "USD"
                                ? getSymbol(resData?.sfxToSfxCurrency)
                                : "$"}
                              {resData?.sfxToSfxCurrency &&
                              resData?.sfxToSfxCurrency !== "USD"
                                ? formatToTwoDecimals(
                                    Number(resData?.localAmount)
                                  )
                                : formatToTwoDecimals(Number(resData?.amount))}
                            </P>
                            <P style={{ marginTop: 5 }}>
                              {resData?.sfxToSfxCurrency || "USD"}
                            </P>
                          </>
                        }
                        convertedAmount={
                          <>
                            {resData?.sfxToSfxCurrency &&
                              resData.sfxToSfxCurrency !== "USD" && (
                                <P
                                  style={{
                                    fontSize: 18,
                                    lineHeight: 24,
                                    marginRight: 2,
                                  }}
                                >
                                  ${Number(resData?.amount)}{" "}
                                  <P style={{ marginTop: 5 }}>USD</P>
                                </P>
                              )}
                          </>
                        }
                        timer={
                          <View style={styles.indicatorCont}>
                            {/* <View
                                                    style={[
                                                      styles.indicatorDot,
                                                      {
                                                        backgroundColor:
                                                          
                                                      },
                                                    ]}
                                                  ></View> */}
                            <P
                              style={{
                                fontSize: 10,
                                lineHeight: 16,
                                color:
                                  tranStat?.toLowerCase() == "pending"
                                    ? colors.yellow
                                    : tranStat?.toLowerCase() === "failed"
                                    ? colors.red
                                    : colors.green,
                              }}
                            >
                              {tranStat}
                            </P>
                          </View>
                        }
                        lineStyle={{ borderStyle: "dashed", marginTop: 24 }}
                        bottomComponent={
                          <View style={styles.desCont}>
                            <View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Fee</P>
                                <P style={styles.value}>
                                  {formatNumberWithCommas(0)}{" "}
                                  <P
                                    // @ts-ignore
                                    style={[
                                      styles.value,
                                      { fontFamily: fonts.poppinsRegular },
                                    ]}
                                  >
                                    {resData?.sfxToSfxCurrency || "USD"}
                                  </P>
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Note</P>
                                <P style={styles.value}>
                                  {resData?.reason ? resData.reason : "None"}
                                </P>
                              </View>
                              {/* <View style={styles.items}>
                                <P style={styles.holder}>Exchange rate</P>
                                <P style={styles.value}>
                                  1 USD ~ 1 USDC
                                </P>
                              </View> */}
                            </View>
                          </View>
                        }
                        bottomComponent2={
                          <>
                            <View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Username</P>
                                <P style={styles.value}>
                                  {/* @ts-ignore */}
                                  {resData?.internalTransferSender?.username}
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Account name</P>
                                <P style={styles.value}>
                                  {/*@ts-ignore*/}
                                  {resData?.internalTransferSender?.firstName}
                                  {resData?.internalTransferSender?.middleName
                                    ? ` ${resData?.internalTransferSender?.middleName}`
                                    : ""}{" "}
                                  {/* @ts-ignore */}
                                  {resData?.internalTransferSender?.lastName}
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Note</P>
                                <P style={styles.value}>
                                  {resData?.reason ? resData.reason : "None"}
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Reference number</P>
                                <View
                                  style={{
                                    flexDirection: "row",
                                    width: 150,
                                    justifyContent: "flex-end",
                                    alignItems: "center",
                                  }}
                                >
                                  <TouchableOpacity
                                    onPress={() => {
                                      // @ts-ignore
                                      copyAccNum(resData?.ref);
                                    }}
                                  >
                                    <SvgXml
                                      xml={svg.lightCopy}
                                      style={{ marginRight: 10 }}
                                    />
                                  </TouchableOpacity>
                                  <P
                                    // @ts-ignore
                                    style={[
                                      styles.value,
                                      { textAlign: "right", width: 120 },
                                    ]}
                                  >
                                    {/* @ts-ignore */}
                                    {resData?.ref}
                                  </P>
                                </View>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Timestamp</P>
                                <P style={styles.value}>
                                  {/* @ts-ignore */}
                                  {formatDate(resData?.updatedAt)}
                                </P>
                              </View>
                            </View>
                            {Platform.OS === "ios" ? (
                              <DashedLine
                                color={colors.stroke}
                                style={{ marginVertical: 8 }}
                                width={(80 * width) / 100}
                              />
                            ) : (
                              <DashedBorderView
                                borderColor={colors.stroke}
                                borderWidth={1}
                                style={{ marginVertical: 8 }}
                              />
                            )}

                            <View style={[styles.items, { marginTop: 8 }]}>
                              <P style={styles.holder}>Payment method</P>
                              <P style={styles.value}>Add money</P>
                            </View>
                            <View style={[styles.items]}>
                              <P style={styles.holder}>Type</P>
                              <P style={styles.value}>SFx money app</P>
                            </View>
                            <View style={[styles.items, { marginBottom: 0 }]}>
                              <P style={styles.holder}>Account</P>
                              {/* @ts-ignore */}
                              <P style={styles.value}>
                                {resData?.fromCurrency}
                              </P>
                            </View>
                          </>
                        }
                      />
                    </>
                  ) : transactionType === "bank transfer" ? (
                    <>
                      <SvgXml
                        xml={svg.bank2}
                        width={32}
                        height={32}
                        style={{
                          top: -15,
                          position: "absolute",
                          zIndex: 10,
                          alignSelf: "center",
                        }}
                      />
                      <DetailCard
                        amount={
                          <>
                            <P
                              style={{
                                fontSize: 24,
                                lineHeight: 36,
                                marginRight: 2,
                              }}
                            >
                              ${formatToTwoDecimals(Number(resData?.amount))}
                            </P>
                            <P style={{ marginTop: 5 }}>USD</P>
                          </>
                        }
                        convertedAmount={
                          <>
                            <P
                              style={{
                                fontSize: 16,
                                lineHeight: 24,
                                marginRight: 2,
                              }}
                            >
                              {symbol}
                              {formatNumberWithCommas(
                                Number(resData?.localAmount)
                              ) || "......"}
                            </P>
                            <P
                              style={{
                                marginTop: 2,
                                fontSize: 12,
                                lineHeight: 18,
                              }}
                            >
                              {yData?.currency}
                            </P>
                          </>
                        }
                        timer={
                          <View style={styles.indicatorCont}>
                            {/* <View
                                                      style={[
                                                        styles.indicatorDot,
                                                        {
                                                          backgroundColor:
                                                            
                                                        },
                                                      ]}
                                                    ></View> */}
                            <P
                              style={{
                                fontSize: 10,
                                lineHeight: 16,
                                color:
                                  tranStat?.toLowerCase() == "pending"
                                    ? colors.yellow
                                    : tranStat?.toLowerCase() === "failed"
                                    ? colors.red
                                    : colors.green,
                              }}
                            >
                              {tranStat}
                            </P>
                          </View>
                        }
                        lineStyle={{ borderStyle: "dashed", marginTop: 24 }}
                        bottomComponent={
                          <View style={styles.desCont}>
                            <View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Fee</P>
                                <P style={styles.value}>
                                  {formatNumberWithCommas(resData?.fee)}{" "}
                                  <P
                                    // @ts-ignore
                                    style={[
                                      styles.value,
                                      { fontFamily: fonts.poppinsRegular },
                                    ]}
                                  >
                                    {yData?.currency}
                                  </P>
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Exchange rate</P>
                                <P style={styles.value}>
                                  {" "}
                                  1 USD ~ {resData?.exchangeRate?.toFixed(
                                    2
                                  )}{" "}
                                  {resData?.fromCurrency}
                                </P>
                              </View>
                            </View>
                          </View>
                        }
                        bottomComponent2={
                          <>
                            <View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Account number</P>
                                <P style={styles.value}>
                                  {yData?.bankInfo?.accountNumber}
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Bank</P>
                                <P style={styles.value}>
                                  {yData?.bankInfo?.name}
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Account name</P>
                                <P style={styles.value}>
                                  {" "}
                                  {yData?.bankInfo?.accountName}
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Note</P>
                                <P style={styles.value}>
                                  {" "}
                                  {yData?.reason ? yData?.reason : "None"}
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Reference number</P>
                                <View
                                  style={{
                                    flexDirection: "row",
                                    width: 150,
                                    justifyContent: "flex-end",
                                    alignItems: "center",
                                  }}
                                >
                                  <TouchableOpacity
                                    onPress={() => {
                                      copyAccNum(resData?.ref);
                                    }}
                                  >
                                    <SvgXml
                                      xml={svg.lightCopy}
                                      style={{ marginRight: 10 }}
                                    />
                                  </TouchableOpacity>
                                  <P
                                    // @ts-ignore
                                    style={[
                                      styles.value,
                                      { textAlign: "right", width: 120 },
                                    ]}
                                  >
                                    {resData?.ref}
                                  </P>
                                </View>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Timestamp</P>
                                <P style={styles.value}>
                                  {formatDate(resData?.updatedAt)}
                                </P>
                              </View>
                            </View>
                            <DashedLine
                              color={colors.stroke}
                              style={{ marginVertical: 8 }}
                              width={(80 * width) / 100}
                            />
                            <View style={[styles.items, { marginTop: 8 }]}>
                              <P style={styles.holder}>Payment method</P>
                              <P style={styles.value}>Add money</P>
                            </View>
                            <View style={[styles.items]}>
                              <P style={styles.holder}>Type</P>
                              <P style={styles.value}>Bank transfer</P>
                            </View>
                            <View style={[styles.items, { marginBottom: 0 }]}>
                              <P style={styles.holder}>Account</P>
                              <P style={styles.value}>USDC</P>
                            </View>
                          </>
                        }
                      />
                    </>
                  ) : transactionType === "mobile money" ? (
                    <>
                      <Image
                        source={require("../../assets/momo.png")}
                        style={{
                          position: "absolute",
                          zIndex: 10,
                          width: 32,
                          height: 32,
                          alignSelf: "center",
                          top: -15,
                        }}
                      />
                      <DetailCard
                        amount={
                          <>
                            <P
                              style={{
                                fontSize: 24,
                                lineHeight: 36,
                                marginRight: 2,
                              }}
                            >
                              ${formatToTwoDecimals(Number(resData?.amount))}
                            </P>
                            <P style={{ marginTop: 5 }}>USD</P>
                          </>
                        }
                        convertedAmount={
                          <>
                            <P
                              style={{
                                fontSize: 16,
                                lineHeight: 24,
                                marginRight: 2,
                              }}
                            >
                              {symbol}
                              {formatNumberWithCommas(
                                Number(resData?.localAmount)
                              )}
                            </P>
                            <P
                              style={{
                                marginTop: 2,
                                fontSize: 12,
                                lineHeight: 18,
                              }}
                            >
                              {yData?.currency}
                            </P>
                          </>
                        }
                        timer={
                          <View style={styles.indicatorCont}>
                            {/* <View
                                                     style={[
                                                       styles.indicatorDot,
                                                       {
                                                         backgroundColor:
                                                           
                                                       },
                                                     ]}
                                                   ></View> */}
                            <P
                              style={{
                                fontSize: 10,
                                lineHeight: 16,
                                color:
                                  tranStat?.toLowerCase() == "pending"
                                    ? colors.yellow
                                    : tranStat?.toLowerCase() === "failed"
                                    ? colors.red
                                    : colors.green,
                              }}
                            >
                              {tranStat}
                            </P>
                          </View>
                        }
                        lineStyle={{ borderStyle: "dashed", marginTop: 24 }}
                        bottomComponent={
                          <View style={styles.desCont}>
                            <View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Fee</P>
                                <P style={styles.value}>
                                  {formatNumberWithCommas(resData?.fee)}{" "}
                                  <P
                                    // @ts-ignore
                                    style={[
                                      styles.value,
                                      { fontFamily: fonts.poppinsRegular },
                                    ]}
                                  >
                                    {yData?.currency}
                                  </P>
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Exchange rate</P>
                                <P style={styles.value}>
                                  1 USD ~ {resData?.exchangeRate?.toFixed(2)}{" "}
                                  {resData?.fromCurrency}
                                </P>
                              </View>
                            </View>
                          </View>
                        }
                        bottomComponent2={
                          <>
                            <View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Mobile number</P>
                                <P style={styles.value}>
                                  {yData?.recipient?.phone}
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Service provider</P>
                                <P style={styles.value}>
                                  {yData?.source?.networkName}
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Account name</P>
                                <P style={styles.value}>
                                  {yData?.recipient?.name}
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Note</P>
                                <P style={styles.value}>
                                  {yData?.reason ? yData?.reason : "None"}
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Reference number</P>
                                <View
                                  style={{
                                    flexDirection: "row",
                                    width: 150,
                                    justifyContent: "flex-end",
                                    alignItems: "center",
                                  }}
                                >
                                  <TouchableOpacity
                                    onPress={() => {
                                      copyAccNum(resData?.ref);
                                    }}
                                  >
                                    <SvgXml
                                      xml={svg.lightCopy}
                                      style={{ marginRight: 10 }}
                                    />
                                  </TouchableOpacity>
                                  <P
                                    // @ts-ignore
                                    style={[
                                      styles.value,
                                      { textAlign: "right", width: 120 },
                                    ]}
                                  >
                                    {resData?.ref}
                                  </P>
                                </View>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Timestamp</P>
                                <P style={styles.value}>
                                  {formatDate(resData?.updatedAt)}
                                </P>
                              </View>
                            </View>
                            <DashedLine
                              color={colors.stroke}
                              style={{ marginVertical: 8 }}
                              width={(80 * width) / 100}
                            />
                            <View style={[styles.items, { marginTop: 8 }]}>
                              <P style={styles.holder}>Payment method</P>
                              <P style={styles.value}>Add money</P>
                            </View>
                            <View style={[styles.items]}>
                              <P style={styles.holder}>Type</P>
                              <P style={styles.value}>Mobile money</P>
                            </View>
                            <View style={[styles.items, { marginBottom: 0 }]}>
                              <P style={styles.holder}>Account</P>
                              <P style={styles.value}>USDC</P>
                            </View>
                          </>
                        }
                      />
                    </>
                  ) : transactionType === "p2p" ? (
                    <>
                      <SvgXml
                        xml={
                          resData?.toCurrency === "USDT"
                            ? svg.tather
                            : svg.usdCoin
                        }
                        width={32}
                        height={32}
                        style={{
                          position: "absolute",
                          zIndex: 10,
                          alignSelf: "center",
                          top: -15,
                        }}
                      />
                      <DetailCard
                        amount={
                          <>
                            <P
                              style={{
                                fontSize: 24,
                                lineHeight: 36,
                                marginRight: 2,
                              }}
                            >
                              $
                              {formatToTwoDecimals(Number(resData?.amount)) ||
                                "0.00"}
                            </P>
                            <P style={{ marginTop: 5 }}>USD</P>
                          </>
                        }
                        convertedAmount={
                          <>
                            <P
                              style={{
                                fontSize: 16,
                                lineHeight: 24,
                                marginRight: 2,
                              }}
                            >
                              {formatToTwoDecimals(Number(resData?.amount)) ||
                                "0.00"}
                            </P>
                            <P
                              style={{
                                marginTop: 2,
                                fontSize: 12,
                                lineHeight: 18,
                              }}
                            >
                              {resData?.toCurrency}
                            </P>
                          </>
                        }
                        timer={
                          <View style={styles.indicatorCont}>
                            {/* <View
                                                     style={[
                                                       styles.indicatorDot,
                                                       {
                                                         backgroundColor:
                                                           
                                                       },
                                                     ]}
                                                   ></View> */}
                            <P
                              style={{
                                fontSize: 10,
                                lineHeight: 16,
                                color:
                                  tranStat?.toLowerCase() == "pending"
                                    ? colors.yellow
                                    : tranStat?.toLowerCase() === "failed"
                                    ? colors.red
                                    : colors.green,
                              }}
                            >
                              {tranStat}
                            </P>
                          </View>
                        }
                        lineStyle={{ borderStyle: "dashed", marginTop: 24 }}
                        bottomComponent={
                          <View style={styles.desCont}>
                            <View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Fee</P>
                                <P style={styles.value}>
                                  {formatNumberWithCommas(resData?.fee)}{" "}
                                  <P
                                    // @ts-ignore
                                    style={[
                                      styles.value,
                                      { fontFamily: fonts.poppinsRegular },
                                    ]}
                                  >
                                    USD
                                  </P>
                                </P>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Network</P>
                                <P style={styles.value}>
                                  {resData?.chain
                                    ? formatNetwork(resData?.chain)
                                    : "...."}
                                </P>
                              </View>
                              {/* <View style={styles.items}>
                            <P style={styles.holder}>Exchange rate</P>
                            <P style={styles.value}>
                              1 {resData?.toCurrency} ~ 1 USD
                            </P>
                          </View> */}
                            </View>
                          </View>
                        }
                        bottomComponent2={
                          <>
                            <View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Sender</P>
                                <P
                                  // @ts-ignore
                                  style={[
                                    styles.value,
                                    {
                                      textAlign: "right",
                                      width: 150,
                                      marginRight: 4,
                                    },
                                  ]}
                                >
                                  {resData?.externalfromWalletAddress}
                                </P>
                              </View>

                              {resData?.transactionHash ? (
                                <View style={styles.items}>
                                  <P style={styles.holder}>Transaction Hash</P>
                                  <View
                                    style={{
                                      flexDirection: "row",
                                      width: 150,
                                      justifyContent: "flex-end",
                                      alignItems: "center",
                                    }}
                                  >
                                    <TouchableOpacity
                                      onPress={() => {
                                        copyHashNum(resData?.transactionHash);
                                      }}
                                    >
                                      <SvgXml
                                        xml={svg.lightCopy}
                                        style={{
                                          marginRight: 10,
                                        }}
                                      />
                                    </TouchableOpacity>
                                    <P
                                      // @ts-ignore
                                      style={[
                                        styles.value,
                                        {
                                          textAlign: "right",
                                          width: 120,
                                          marginRight: 4,
                                        },
                                      ]}
                                    >
                                      {truncateTransactionHash(
                                        resData?.transactionHash
                                      )}
                                    </P>
                                  </View>
                                </View>
                              ) : (
                                <></>
                              )}
                              <View style={styles.items}>
                                <P style={styles.holder}>Reference number</P>
                                <View
                                  style={{
                                    flexDirection: "row",
                                    width: 150,
                                    justifyContent: "flex-end",
                                    alignItems: "center",
                                  }}
                                >
                                  <TouchableOpacity
                                    onPress={() => {
                                      copyAccNum(resData?.ref);
                                    }}
                                  >
                                    <SvgXml
                                      xml={svg.lightCopy}
                                      style={{ marginRight: 10 }}
                                    />
                                  </TouchableOpacity>
                                  <P
                                    // @ts-ignore
                                    style={[
                                      styles.value,
                                      {
                                        textAlign: "right",
                                        width: 120,
                                        marginRight: 4,
                                      },
                                    ]}
                                  >
                                    {resData?.ref}
                                  </P>
                                </View>
                              </View>
                              <View style={styles.items}>
                                <P style={styles.holder}>Timestamp</P>
                                <P style={styles.value}>
                                  {formatDate(resData?.updatedAt)}
                                </P>
                              </View>
                            </View>
                            <DashedLine
                              color={colors.stroke}
                              style={{ marginVertical: 8 }}
                              width={(80 * width) / 100}
                            />
                            <View style={[styles.items, { marginTop: 8 }]}>
                              <P style={styles.holder}>Payment method</P>
                              <P style={styles.value}>Add money</P>
                            </View>
                            <View style={[styles.items]}>
                              <P style={styles.holder}>Type</P>
                              <P style={styles.value}>Wallet Transfer</P>
                            </View>
                            <View style={[styles.items, { marginBottom: 0 }]}>
                              <P style={styles.holder}>Credited to</P>
                              <P style={styles.value}>{resData?.toCurrency}</P>
                            </View>
                          </>
                        }
                      />
                    </>
                  ) : (
                    <></>
                  )}
                  <View style={styles.buttonWrap}>
                    {tranStat?.toLowerCase() === "successful" && (
                      <Button
                        btnText="View receipt"
                        onPress={() => {
                          navigation.navigate("AllRRecieptScreen", {
                            transactionType: transactionType,
                            data: resData,
                            data2: yData,
                          });
                        }}
                      />
                    )}
                    <View
                      style={{
                        alignItems: "center",
                        flexDirection: "row",
                        justifyContent: "center",
                        marginTop: 32,
                      }}
                    >
                      <SvgXml xml={svg.chat} style={{ marginRight: 4 }} />
                      <Link
                        style={{ fontSize: 12 }}
                        onPress={() => {
                          const username =
                            storedCredentails?.user?.username || "Not provided";
                          const email =
                            storedCredentails?.user?.email || "Not provided";
                          const message = `Hi, Support, I have an issue that requires resolving.\nMy Username is ${username} and My email is ${email}`;
                          const encodedMessage = encodeURIComponent(message);
                          Linking.openURL(
                            `https://wa.me/905338563416?text=${encodedMessage}`
                          );
                        }}
                      >
                        Report transaction
                      </Link>
                    </View>
                  </View>
                </View>
              </View>
            </>
          )}
        </ScrollView>
      </Div>
      {/* {loader && <Loader />} */}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },

  desCont: {
    width: "100%",
    marginTop: 8,
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    width: "60%",
    // backgroundColor: 'red',
    textAlign: "right",
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  indicatorCont: {
    padding: 19.5,
    paddingTop: 4,
    paddingBottom: 4,
    borderRadius: 99,
    flexDirection: "row",
    alignItems: "center",
  },
  indicatorDot: {
    width: 8,
    height: 8,
    borderRadius: 99,
    marginRight: 4,
  },
  dash: {
    borderWidth: Platform.OS === "ios" ? 1 : 0,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    marginTop: 24,
  },
});
