import React, { useCallback, useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { colors } from "../../config/colors";
import Button from "../../components/Button";
import Input from "../../components/Input";
import {
  AddBeneficiary,
  EditBeneficiary,
  GetBeneficiaries,
  GetUserByName,
} from "../../RequestHandlers/User";
import ContentLoader, { Rect } from "react-content-loader/native";
import Loader from "../../components/ActivityIndicator";
import { useToast } from "../../context/ToastContext";

const { width, height } = Dimensions.get("window");

export default function EditSfxBene({ navigation, route }) {
  const [isEnabled, setIsEnabled] = useState(false);
  const [showQrCode, setShowQrCode] = useState(false);
  const [bene, setBene] = useState(false);
  const toggleSwitch = () => setIsEnabled((previousState) => !previousState);
  const [nameError, setNameError] = useState(false);
  const [loadng, setLoading] = useState(false);
  const [username, setUsername] = useState("");
  const [note, setNote] = useState("");
  const [details, setdetails] = useState<any>([]);
  const { data, userName } = route?.params || "";
  const [isKycDone, setIsKycDone] = useState(false);
  const [loader, setLoader] = useState(false);
  const [beneficiaries, setBeneficiaries] = useState<any>([]);
  const [isOn, setIsOn] = useState(false);
  const [ld, setLd] = useState(false);
  const { handleToast } = useToast();
  const checkBeneficiary = () => {
    setBene(true);
  };

  const isEmpty = (string: string) => {
    if (
      string == "" ||
      string == " " ||
      string == null ||
      string == undefined
    ) {
      return false;
    } else {
      return true;
    }
  };

  const [error, setError] = useState("");
  const [debouncedUsername, setDebouncedUsername] = useState(username); // Debounced username

  const getUser = async (username) => {
    setLoading(true);
    setError(""); // Clear error before checking

    try {
      const getUser = await GetUserByName(username);
      console.log(getUser);

      if (getUser.username) {
        setLoading(false);
        setIsKycDone(getUser?.verified);
        setBene(true);
        setdetails(getUser);
        setError(""); // Clear error if user is found
      } else if (username.length === 0) {
        setLoading(false);
      } else {
        setLoading(false);
        setBene(false);
        setError("Username not found."); // Set error if username not found
      }
    } catch (error) {
      setLoading(false);
      setError("An error occurred. Please try again."); // Error handling
    }
  };
  useEffect(() => {
    const handler = setTimeout(() => {
      if (debouncedUsername) {
        getUser(debouncedUsername); // Fetch user details after debounce
      }
    }, 500); // Wait 500ms before triggering the API call
    return () => {
      clearTimeout(handler);
    };
  }, [debouncedUsername]);
  useEffect(() => {
    if (data != "" && data != undefined && data != null) {
      getUser(userName);
      setUsername(userName);
    }
  }, [data != "" && data != undefined && data != null]);
  const handleInputChange = (text) => {
    setUsername(text);
    setError(""); // Clear error when typing
    setDebouncedUsername(text); // Set the debounced value
    setNameError(false);
  };
  const handleScan = (index) => {
    getUser(index);
    setUsername(index);
  };

  const sendSFXMoneyApp = () => {
    console.log(true);
    if (isEmpty(username) === false) {
      console.log(true);
      setNameError(true);
      // handleToast("Fill your UserName", "error");
    } else {
      setNameError(false);
      navigation.navigate("AmountScreen", {
        username: username,
        note: note,
        details: details,
      });
    }
  };
  const AddBene = async () => {
    if (error != "") {
      handleToast("User not found", "error");
    } else {
      try {
        const body = {
          account: details?.username,
          providerName: "sfx-money-app",
          name: `${details?.firstName} ${details?.lastName}`,
          type: "sfx-money-app",
          country: "NG",
        };
        const response = await EditBeneficiary(data, body);
        if (response.account) {
          handleToast("Beneficiary updated", "success");
        } else {
          handleToast(response.message, "error");
          setIsOn(false);
        }
      } catch (error) {}
    }
  };

  const handleSwitch = (state) => {
    if (isOn) {
      setIsOn(false);
    } else {
      setIsOn(true);
      if (state) {
        if (details.length === 0) {
          console.log("no user to save");
          handleToast("No beneficiary to save", "error");
          setTimeout(() => {
            setIsOn(false);
          }, 1000);
        } else {
          AddBene();
        }
      } else {
        console.log("i tire o");
      }
    }
  };

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Edit beneficiary" navigation={navigation} />
        <View>
          <ScrollView automaticallyAdjustKeyboardInsets={true}>
            <View style={styles.contentBody}>
              <View style={styles.detailWrap}>
                <Input
                  label="Username"
                  placeholder="Mato123"
                  inputStyle={{ width: "85%" }}
                  // contStyle={{ marginBottom: 16 }}
                  value={username}
                  error={nameError}
                  onChangeText={handleInputChange} // Call the handler on text change
                />
                {nameError && (
                  <View style={{ width: "100%" }}>
                    <P
                      style={{
                        color: colors.red,
                        marginTop: 8,
                        textAlign: "left",
                        fontSize: 12,
                      }}
                    >
                      Username is required
                    </P>
                  </View>
                )}
                {error ? (
                  <View style={{ width: "100%" }}>
                    <P
                      style={{
                        color: colors.red,
                        marginTop: 8,
                        textAlign: "left",
                        fontSize: 12,
                      }}
                    >
                      {error}
                    </P>
                  </View>
                ) : null}
                {loadng ? (
                  <ContentLoader
                    style={{ marginBottom: 16, marginTop: 16 }}
                    width={"100%"}
                    height={44}
                    speed={2}
                    backgroundColor="#F7F4FF"
                    foregroundColor="#ecebeb"
                  >
                    {/* The Rect now covers the entire height of the loader */}
                    <Rect x="0" y="0" rx="4" ry="4" width="100%" height="44" />
                  </ContentLoader>
                ) : (
                  bene && (
                    <View style={styles.benefeciary}>
                      {/* @ts-ignore */}
                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Image
                          // @ts-ignore
                          source={
                            details.picture
                              ? { uri: details.picture }
                              : require("../../assets/defualtAvatar.png")
                          }
                          style={{
                            width: 30,
                            height: 30,
                            borderRadius: 100,
                            marginRight: 10,
                          }}
                        />
                        {/* @ts-ignore */}
                        <P>{`${details?.firstName} ${details?.lastName}`}</P>
                      </View>
                      <SvgXml xml={svg.green_check} />
                    </View>
                  )
                )}
              </View>
              <View style={{ width: "80%", marginTop: 32 }}>
                <Button
                  btnText="Save beneficiary"
                  loading={ld}
                  onPress={() => {
                    console.log("pressed");
                    if (
                      username === "" ||
                      username === undefined ||
                      username === null
                    ) {
                      setNameError(true);
                    } else {
                      AddBene();
                    }
                  }}
                />
              </View>
            </View>
          </ScrollView>
        </View>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    // justifyContent:"center",
    alignItems: "center",
  },
  benefeciary: {
    paddingTop: 4,
    paddingRight: 24,
    paddingBottom: 4,
    paddingLeft: 24,
    backgroundColor: "#F7F4FF",
    borderRadius: 8,
    flexDirection: "row",
    width: "100%",
    height: 44,
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
    marginTop: 16,
  },
  deatilsHead: {
    width: "100%",
    height: 42,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: "5%",
  },
  detailWrap: {
    padding: 24,
    width: "90%",
    alignSelf: "center",
    // height:200,
    backgroundColor: "white",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  detailWrap2: {
    // padding: 24,
    width: "90%",
    alignSelf: "center",
    height: 246,
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent: "center",
    alignItems: "center",
    marginTop: 60,
  },

  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  label: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
  },
});
