import React, { useEffect, useState, useRef } from "react";
import {
  Dimensions,
  StyleSheet,
  View,
  TouchableOpacity,
  Platform,
  Linking,
  Animated,
  Easing,
} from "react-native";
import { colors } from "../config/colors";
const { width, height } = Dimensions.get("window");
import { SvgXml } from "react-native-svg";
import { fonts } from "../config/Fonts";
import P from "../components/P";
import { svg } from "../config/Svg";
import Button from "../components/Button";
import * as Updates from "expo-updates";
import * as Application from "expo-application";

interface PProps {
  onPress?: () => void;
  isUpdate?: boolean;
}

export function UpdateModal({ onPress }: PProps) {
  const [isUpdate, setIsUpdate] = useState(false);
  const slideAnim = useRef(new Animated.Value(-height)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isUpdate) {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isUpdate]);

  const handleClose = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -height,
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsUpdate(false);
    });
  };

  async function checkForStoreUpdate() {
    try {
      const currentVersion = Application.nativeApplicationVersion;
      let storeVersion = null;

      if (Platform.OS === "android") {
        // Android - Google Play Store with proper headers
        const response = await fetch(
          "https://play.google.com/store/apps/details?id=com.henry.sfxmoneyapp",
          {
            headers: {
              "User-Agent":
                "Mozilla/5.0 (Linux; Android 10; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
              "Accept-Language": "en-US,en;q=0.9",
            },
          }
        );

        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);

        const text = await response.text();
        // New regex pattern that matches Google Play's current structure
        const match = text.match(/\[\[\["([\d.]+?)"\]\],/);
        storeVersion = match?.[1];
        console.log(match);
      } else {
        // iOS - App Store API
        const response = await fetch(
          "https://itunes.apple.com/lookup?bundleId=com.sfx" // Ensure this matches your actual bundle ID
        );

        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);

        const data = await response.json();
        storeVersion = data.results?.[0]?.version;
      }

      console.log("Current:", currentVersion, "Store:", storeVersion);

      if (!storeVersion) throw new Error("Failed to retrieve store version");

      // Proper version comparison
      const compareVersions = (a, b) => {
        const aParts = a.split(".").map(Number);
        const bParts = b.split(".").map(Number);

        for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
          const aVal = aParts[i] || 0;
          const bVal = bParts[i] || 0;
          if (bVal > aVal) return true;
          if (bVal < aVal) return false;
        }
        return false;
      };

      if (compareVersions(currentVersion, storeVersion)) {
        setIsUpdate(true);
      } else {
        setIsUpdate(false);
      }
    } catch (error) {
      console.log("Store Update Error:", error);
      // Optional: Show error to user or retry logic
    }
  }

  useEffect(() => {
    checkForStoreUpdate();
  }, []);

  if (!isUpdate) return null;

  return (
    <View style={styles.modalContainer}>
      <Animated.View 
        style={[
          styles.overlay,
          { opacity: fadeAnim }
        ]}
      />
      <Animated.View
        style={[
          styles.modalContent,
          { transform: [{ translateY: slideAnim }] }
        ]}
      >
        <TouchableOpacity
          style={styles.closeButton}
          onPress={handleClose}
        >
          <SvgXml xml={svg.xClose} />
        </TouchableOpacity>
        <SvgXml xml={svg.rocket} />
        <View style={styles.textContainer}>
          <P style={styles.title}>New update available!</P>
          <P style={styles.description}>
            Experience improved performance with the latest SFx Money update.
            Update now to enjoy a smoother, optimized experience!
          </P>
        </View>
        <View style={styles.buttonContainer}>
          <Button
            btnText="Update now"
            onPress={() => {
              const storeUrl = Platform.select({
                android: "market://details?id=com.henry.sfxmoneyapp",
                ios: "itms-apps://apps.apple.com/app/id6723890908", // Use actual Apple ID
              });

              Linking.openURL(storeUrl).catch(() => {
                // Fallback URL
                Linking.openURL(
                  Platform.select({
                    android:
                      "https://play.google.com/store/apps/details?id=com.henry.sfxmoneyapp",
                    ios: "https://apps.apple.com/ng/app/sfx-money-app/id6723890908",
                  })
                );
              });
            }}
          />
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1000,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.white,
  },
  modalContent: {
    width: (90 * width) / 100,
    height: (100 * height) / 100,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    backgroundColor: colors.white,
  },
  closeButton: {
    width: 40,
    height: 40,
    position: "absolute",
    top: 70,
    right: 0,
  },
  textContainer: {
    marginTop: 24,
    alignItems: 'center',
  },
  title: {
    textAlign: "center",
  },
  description: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    lineHeight: 18,
    textAlign: "center",
    marginTop: 4,
    paddingLeft: 20,
    paddingRight: 20,
  },
  buttonContainer: {
    marginTop: 24,
  },
});
