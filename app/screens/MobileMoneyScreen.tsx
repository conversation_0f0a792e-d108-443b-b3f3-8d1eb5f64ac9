import React, { useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Image,
} from "react-native";
import { fonts } from "../config/Fonts";
import Div from "../components/Div";
import AuthenticationHedear from "../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "../components/P";
import MicroBtn from "../components/MicroBtn";
import { colors } from "../config/colors";
import Input from "../components/Input";
import Button from "../components/Button";
import BottomSheet from "../components/BottomSheet";
import ListItemSelect from "../components/ListItemSelect";

const { width, height } = Dimensions.get("window");

export default function MobileMoneyScreen({ navigation }) {
  const [countryCode2, setCountryCode2] = useState("");
  const [moneyIcon, setMoneyIcon] = useState(require("../assets/momo.png"));
  const [show2, setShow2] = useState(false);
  const [isproviderSelected, setIsProviderSelected] = useState(null);
  // const [providerName, setProvider] = useState('')
  const mMoneys = [
    { icon: require("../assets/momo.png"), name: "Memo" },
    { icon: require("../assets/zamtel.png"), name: "Zamtel" },
  ];
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Mobile money details"
          navigation={navigation}
        />
        <View style={styles.contentBody}>
          <View style={styles.modalCard}>
            <TouchableOpacity
              onPress={() => {
                setShow2(true);
              }}
            >
              <Input
                value={countryCode2}
                label="Select provider"
                placeholder="Momo"
                inputStyle={{ width: "65%", color: "#161817" }}
                contStyle={{ marginBottom: 16 }}
                editable={false}
                leftIcon={
                  <View>
                    <Image
                      source={moneyIcon}
                      style={{ width: 24, height: 24, marginLeft: 14 }}
                    />
                  </View>
                }
                rightIcon={
                  <View
                    style={{
                      //   backgroundColor: "red",
                      width: "15%",
                      height: "100%",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <SvgXml xml={svg.dropDown} />
                  </View>
                }
              />
            </TouchableOpacity>
            <View>
              <Input label="Mobile number" placeholder="**********" />
            </View>
          </View>

          <View style={{ width: "80%", alignSelf: "center", marginTop: 32 }}>
            <Button
              btnText="Confirm"
              onPress={() => navigation.navigate("AmountInputScreen")}
            />
          </View>
        </View>
      </Div>
      <BottomSheet
        isVisible={show2}
        onClose={() => setShow2(false)}
        backspaceText="Provider"
        showBackArrow={false}
        modalContentStyle={{ height: "40%" }}
        extraModalStyle={{ height: "38%" }}
        components={
          <View>
            <P
              style={{
                marginTop: 24,
                fontSize: 12,
                lineHeight: 19.2,
                color: colors.gray,
              }}
            >
              Select your service provider
            </P>
            <View>
              {mMoneys.map((item, index) => {
                return (
                  <ListItemSelect
                    key={index}
                    text1={item.name}
                    image={item.icon}
                    onPress={() => {
                      setIsProviderSelected(index);
                      setCountryCode2(item.name);
                      setMoneyIcon(item.icon);

                      setTimeout(() => {
                        setShow2(false);
                      }, 2000);
                    }}
                    containerStyle={{
                      marginBottom: 16,
                      marginTop: index == 0 ? 6 : 0,
                    }}
                    isActive={isproviderSelected == index}
                  />
                );
              })}
            </View>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: "rgba(247, 244, 255, 1)",
  },
  btnCard: {
    width: "90%",
    minHeight: 156,
    backgroundColor: "white",
    alignSelf: "center",
    marginTop: 24,
    borderRadius: 12,
    paddingTop: 16,
    paddingBottom: 16,
    // paddingLeft: 16,
    // paddingRight: 16,
  },
  btnSec1: {
    width: "100%",
    justifyContent: "space-around",
    flexDirection: "row",
    marginBottom: 24,
    // paddingHorizontal: 18.33
  },
  modalCard: {
    width: "90%",
    padding: 16,
    marginTop: 24,
    paddingTop: 24,
    paddingBottom: 24,
    backgroundColor: colors.white,
    alignSelf: "center",
    borderRadius: 16,
  },
});
