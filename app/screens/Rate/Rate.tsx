import React, { useState, useEffect, useCallback } from "react";
import {
  StyleSheet,
  View,
  Image,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { TextInput } from "react-native-gesture-handler";
import { colors } from "../../config/colors";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import { fonts } from "../../config/Fonts";
import NoteComponent2 from "../../components/NoteComponent2";
import { countries } from "../../components/counties";
import P from "../../components/P";
import { GetAllRate, GetAllSFxRate } from "../../RequestHandlers/Wallet";
import Loader from "../../components/ActivityIndicator";
import { useFocusEffect } from "@react-navigation/native";
import { formatToTwoDecimals } from "../../Utils/numberFormat";

export default function Rates({ navigation }) {
  const [activeType, setActiveType] = useState(null);
  const [activeCountry, setActiveCountry] = useState("");
  const [activeFlag, setActiveFlag] = useState(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [allRate, setAllRate] = useState([]);
  const [filteredCountries, setFilteredCountries] = useState([]);

  useFocusEffect(
    useCallback(() => {
      getAllRates();
    }, [])
  );

  const getAllRates = async () => {
    try {
      const sfxRates = await GetAllSFxRate();

      // Group rates by currency pair and provider
      const ratesByPairAndProvider = {};
      sfxRates.forEach(
        (rate: {
          from: string;
          to: string;
          type: string;
          amount: number;
          provider: string;
        }) => {
          const key = `${rate.from}-${rate.to}`;
          if (!ratesByPairAndProvider[key]) {
            ratesByPairAndProvider[key] = {};
          }
          if (!ratesByPairAndProvider[key][rate.type]) {
            ratesByPairAndProvider[key][rate.type] = [];
          }
          ratesByPairAndProvider[key][rate.type].push(rate);
        }
      );
      const ratesMap = {};
      Object.keys(ratesByPairAndProvider).forEach((pair) => {
        ratesMap[pair] = {};
        Object.keys(ratesByPairAndProvider[pair]).forEach((type) => {
          const rates = ratesByPairAndProvider[pair][type];
          if (rates.length > 1) {
            const yellowCardRate = rates.find(
              (r: { provider: string; amount: number }) =>
                r.provider === "sfx-yellow-card" || r.provider === "yellow-card"
            );
            if (yellowCardRate) {
              ratesMap[pair][type] = yellowCardRate.amount;
            } else {
              ratesMap[pair][type] = rates[0].amount;
            }
          } else if (rates.length === 1) {
            ratesMap[pair][type] = rates[0].amount;
          }
        });
      });
      const matchedRates = countries
        .filter((country) => {
          const key = `USD-${country.currencyCode}`;
          return ratesMap[key];
        })
        .map((country) => {
          const keySell = `USD-${country.currencyCode}`;
          const keyBuy = `${country.currencyCode}-USD`;
          const sellrate = ratesMap[keySell];
          const buyrate = ratesMap[keyBuy];
          return {
            ...country,
            buyRate: buyrate?.buy || "N/A",
            sellRate: sellrate?.sell || "N/A",
          };
        });
      setFilteredCountries(matchedRates);
      setAllRate(matchedRates);
    } catch (error) {}
  };

  useEffect(() => {
    if (searchQuery) {
      const filtered = allRate.filter((country) =>
        country.country.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCountries(filtered);
    } else {
      setFilteredCountries(allRate);
    }
  }, [searchQuery, allRate]);

  return (
    <View style={styles.body}>
      <View style={styles.customInput}>
        <SvgXml xml={svg.search} style={{ marginRight: 8 }} />
        <TextInput
          style={styles.input}
          placeholder="Search"
          cursorColor={colors.black}
          value={searchQuery}
          onChangeText={(text) => setSearchQuery(text)}
        />
      </View>
      <View
        style={{
          marginTop: 24,
          width: "90%",
          alignSelf: "center",
          marginBottom: 16,
        }}
      >
        <NoteComponent2
          text={"Exchange rates shown are for informational purposes only. "}
        />
      </View>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View
          style={{
            width: "90%",

            backgroundColor: colors.white,
            alignSelf: "center",
            borderRadius: 12,
            marginBottom: 200,
          }}
        >
          <View style={{ paddingTop: 12, paddingBottom: 12, paddingLeft: 24 }}>
            <P style={{ fontSize: 12, color: colors.gray }}>Currency rate</P>
          </View>
          <View style={styles.rateNav}>
            <P style={styles.add}>Add</P>
            <P style={styles.add}>Send</P>
          </View>
          <View>
            {filteredCountries.length === 0 ? (
              <></>
            ) : (
              filteredCountries.map((item, index) => (
                <View
                  key={`country-${item.currencyCode}-${index}`}
                  style={[
                    styles.rateItem,
                    {
                      borderBottomWidth:
                        index === filteredCountries.length - 1 ? 0 : 1,
                    },
                  ]}
                >
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      width: "55%",
                    }}
                  >
                    <Image
                      source={item.flag}
                      style={{
                        width: 24,
                        height: 24,
                        marginRight: 8,
                        borderRadius: 100,
                        objectFit: "cover",
                      }}
                    />
                    <View>
                      <P style={{ fontSize: 12, width: 120 }} numberOfLines={1}>
                        {item.country}
                      </P>
                      <P style={{ fontSize: 12, width: 120 }} numberOfLines={1}>
                        {item.currencyCode} ~ USD
                      </P>
                    </View>
                  </View>
                  <View style={{ flexDirection: "row" }}>
                    <TouchableOpacity
                      onPress={() =>
                        navigation.navigate("AddMoneyRate2", {
                          from: "AddMoneyRate",
                          item: item,
                        })
                      }
                    >
                      <View style={styles.rateCont}>
                        <P style={styles.rateText}>
                          {item?.buyRate != "N/A"
                            ? formatToTwoDecimals(item?.buyRate)
                            : formatToTwoDecimals(item?.buyRate)}
                        </P>
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() =>
                        navigation.navigate("AddMoneyRate2", {
                          from: "SendMoneyRate",
                          item: item,
                        })
                      }
                    >
                      <View style={[styles.rateCont, { marginLeft: 20 }]}>
                        <P
                          // @ts-ignore
                          style={[
                            styles.rateText,
                            { marginRight: 0, borderColor: colors.red },
                          ]}
                        >
                          {item?.sellRate != "N/A"
                            ? formatToTwoDecimals(item?.sellRate)
                            : formatToTwoDecimals(item.sellRate)}
                        </P>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
              ))
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
  },

  customInput: {
    width: "90%",
    height: 44,
    backgroundColor: colors.white,
    alignSelf: "center",
    borderRadius: 12,
    marginTop: 16,
    paddingLeft: 16,
    paddingRight: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  input: {
    width: "80%",
    height: 44,
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
  },
  rateNav: {
    width: "100%",
    backgroundColor: "#E5E5EB",
    flexDirection: "row",
    paddingTop: 12.5,
    paddingBottom: 12.5,
    justifyContent: "flex-end",
  },
  rateItem: {
    width: "100%",
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 24,
    paddingRight: 24,
    flexDirection: "row",
    justifyContent: "space-between",
    borderBottomWidth: 1,
    borderBottomColor: colors.stroke,
  },
  rateText: {
    fontSize: 11,
    borderWidth: 0,
    textAlign: "center",
  },
  add: {
    fontSize: 12,
    paddingLeft: 30,
    paddingRight: 30,
  },
  rateCont: {
    width: 70,
    height: 34,
    borderWidth: 1,
    borderColor: colors.green,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
});
