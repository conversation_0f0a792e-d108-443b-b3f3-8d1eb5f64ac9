import React, { useEffect, useState } from "react";
import { View, StyleSheet, Dimensions, ScrollView, Image } from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import InputCard from "../../components/InputCard";
import Keyboard from "../../components/Keyboard";
import Button from "../../components/Button";
import { colors } from "../../config/colors";
import NoteComponent2 from "../../components/NoteComponent2";
import i18n from "../../../i18n";
import { formatToTwoDecimals } from "../../Utils/numberFormat";

const baseHeight = 802;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");

export default function AddMoneyRate2({ navigation, route }) {
  const [inputValue, setInputValue] = useState("0");
  const { from, item, activeRate } = route.params || "";
  const [error, setError] = useState(false);
  const [isUsdInput, setIsUsdInput] = useState(true); // Track which currency is being input
  const ngnRate = item.buyRate; // Example exchange rate

  const formatNumber = (value) => {
    value = value?.toString();
    return value?.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const formatNumberWithDecimal = (value, decimalPlaces = 2) => {
    if (!isNaN(value)) {
      return Number(value)
        .toFixed(decimalPlaces)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    return "0.00";
  };

  const handleKeyPress = (key) => {
    setError(false); // Reset error state on key press

    if (key === "←") {
      setInputValue((prev) => (prev.length > 1 ? prev.slice(0, -1) : "0"));
    } else if (key === "Enter") {
      // Handle enter key press
    } else {
      setInputValue((prev) => {
        let newValue = prev === "0" && key !== "." ? key : prev + key;
        newValue = newValue.replace(/[^0-9.]/g, ""); // Remove any non-numeric characters
        return newValue;
      });
    }
  };

  const toggleCurrency = () => {
    setIsUsdInput(!isUsdInput);
    setInputValue("0"); // Reset input value when toggling
  };

  const convertedValue = isUsdInput
    ? formatNumberWithDecimal(Number(inputValue) * ngnRate)
    : formatNumberWithDecimal(Number(inputValue) / ngnRate);

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text={from.includes("AddMoneyRate") ? i18n.t("amr") : i18n.t("smr")}
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.inputCardWrap}>
              <InputCard
                headerText={i18n.t("cr")}
                onTogglePress={toggleCurrency}
                amountValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 32,
                        lineHeight: 48,
                        marginRight: 4,
                      }}
                    >
                      {isUsdInput
                        ? `$${formatNumber(inputValue)}`
                        : `${item.symbol}${formatNumber(inputValue)}`}
                      <P style={{ lineHeight: 48 }}>
                        {isUsdInput
                          ? activeRate?.currencyCode
                          : item.currencyCode}
                      </P>
                    </P>
                  </>
                }
                convertedValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 16,
                        lineHeight: 24,
                        marginRight: 4,
                      }}
                    >
                      {isUsdInput
                        ? `${item.symbol}${formatToTwoDecimals(
                            Number(inputValue) * ngnRate
                          )}`
                        : `$${formatToTwoDecimals(
                            Number(inputValue) / ngnRate
                          )}`}
                      <P style={{ lineHeight: 24, fontSize: 12 }}>
                        {isUsdInput ? item.currencyCode : activeRate.currencyCode}
                      </P>
                    </P>
                  </>
                }
                toggleStyle={{ top: "45%" }}
                text1={`Exchange rate: 1 USD ~ ${ngnRate} ${item.symbol}`}
                error={error}
                extraComponent={
                  <View style={{ width: "90%", marginTop: 8 }}>
                    <NoteComponent2 text={i18n.t("exchangeRate")} />
                  </View>
                }
              />
            </View>
            <View style={styles.bottom}>
              <View style={{ width: "90%", alignSelf: "center" }}>
                <Keyboard onKeyPress={handleKeyPress} />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    height: (92 * height) / 100,
    backgroundColor: "rgba(247, 244, 255, 1)",
    paddingTop: 16,
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
  },
  bottom: {
    width,
    top: (148 / baseHeight) * height,
  },
});
