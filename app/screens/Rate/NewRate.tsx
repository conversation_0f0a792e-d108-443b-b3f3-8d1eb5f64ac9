import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  StyleSheet,
  View,
  Image,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { TextInput } from "react-native-gesture-handler";
import { colors } from "../../config/colors";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import { fonts } from "../../config/Fonts";
import NoteComponent2 from "../../components/NoteComponent2";
import { countries } from "../../components/counties";
import P from "../../components/P";
import { GetAllRate, GetAllSFxRate } from "../../RequestHandlers/Wallet";
import Loader from "../../components/ActivityIndicator";
import { useFocusEffect } from "@react-navigation/native";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import { useToast } from "../../context/ToastContext";
import { formatToTwoDecimals } from "../../Utils/numberFormat";


export default function MainRates({ navigation }) {
  const [activeType, setActiveType] = useState(null);
  const [activeCountry, setActiveCountry] = useState("");
  const [activeFlag, setActiveFlag] = useState(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [allRate, setAllRate] = useState([]);
  const [filteredCountries, setFilteredCountries] = useState([]);
  const [loading, setLoading] = useState(true);
  const { handleToast } = useToast();
  const [activeRate, setActiveRate] = useState<any>({
    country: "United States",
    flag: require("../../assets/usa.png"),
    symbol: "$",
    currencyCode: "USD",
  });
  const rContries = [
    {
      country: "United States",
      flag: require("../../assets/usa.png"),
      symbol: "$",
      currencyCode: "USD",
    },
    {
      country: "Turkey",
      flag: require("../../assets/turkey.png"),
      symbol: "₺",
      currencyCode: "TRY",
    },
  ];
  const usData = {
    country: "United States",
    flag: require("../../assets/usa.png"),
    flagcode: "🇺🇸",
    countryCode: "+1",
    alphaCode: "USA",
    buyRate: 1620,
    sellRate: 1620,
    currency: "US Dollar",
    symbol: "$",
    currencyCode: "USD",
    code2: "US",
    YellowCardCode: "US",
  };

  useFocusEffect(
    useCallback(() => {
      getAllRates();
    }, [activeRate?.currencyCode])
  );

  const getAllRates = async () => {
    setLoading(true);
    try {
      const sfxRates = await withApiErrorToast(GetAllSFxRate(activeRate.currencyCode), handleToast);
      const ratesByPairAndProvider = {};
      sfxRates.forEach(
        (rate: {
          from: string;
          to: string;
          type: string;
          amount: number;
          provider: string;
        }) => {
          const key = `${rate.from}-${rate.to}`;
          if (!ratesByPairAndProvider[key]) {
            ratesByPairAndProvider[key] = {};
          }
          if (!ratesByPairAndProvider[key][rate.type]) {
            ratesByPairAndProvider[key][rate.type] = [];
          }
          ratesByPairAndProvider[key][rate.type].push(rate);
        }
      );

      // Then create a map with prioritized rates
      const ratesMap = {};

      // Process each currency pair
      Object.keys(ratesByPairAndProvider).forEach((pair) => {
        ratesMap[pair] = {};

        // Process each rate type (buy/sell)
        Object.keys(ratesByPairAndProvider[pair]).forEach((type) => {
          const rates = ratesByPairAndProvider[pair][type];
          if (rates.length > 1) {
            const yellowCardRate = rates.find(
              (r: { provider: string; amount: number }) =>
                r.provider === "sfx-yellow-card" || r.provider === "yellow-card"
            );
            if (yellowCardRate) {
              ratesMap[pair][type] = yellowCardRate.amount;
            } else {
              ratesMap[pair][type] = rates[0].amount;
            }
          } else if (rates.length === 1) {
            // If only one provider, use that rate
            ratesMap[pair][type] = rates[0].amount;
          }
        });
      });
      const appendCountry = countries;
      let matchedRates = appendCountry
        .filter((country) => {
          // When TRY tab is selected, exclude US and Turkey from the rate list
          if (activeRate.currencyCode === "TRY") {
            if (country.currencyCode === "USD" || country.currencyCode === "TRY") {
              return false;
            }
          }

          const key = `${activeRate.currencyCode}-${country.currencyCode}`;
          return ratesMap[key];
        })
        .map((country) => {
          const keySell =
            country.currencyCode === "USD"
              ? `${country.currencyCode}-${activeRate.currencyCode}`
              : `${activeRate.currencyCode}-${country.currencyCode}`;
          const keyBuy =
            country.currencyCode === "USD"
              ? `${activeRate.currencyCode}-${country.currencyCode}`
              : `${country.currencyCode}-${activeRate.currencyCode}`;
          const sellrate = ratesMap[keySell];
          const buyrate = ratesMap[keyBuy];
          return {
            ...country,
            buyRate: buyrate?.buy || "N/A",
            sellRate: sellrate?.sell || "N/A",
          };
        });

      setFilteredCountries(matchedRates);
      setAllRate(matchedRates);
    } catch (error) {
      console.error("Error fetching rates:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (searchQuery) {
      // Filter countries based on the search query
      const filtered = allRate.filter((country) =>
        country.country.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCountries(filtered);
    } else {
      setFilteredCountries(allRate); // Reset to all countries when the search query is empty
    }
  }, [searchQuery, allRate]);

  return (
    <View style={styles.body}>
      <View style={styles.customInput}>
        <SvgXml xml={svg.search} style={{ marginRight: 8 }} />
        <TextInput
          style={styles.input}
          placeholder="Search"
          cursorColor={colors.black}
          value={searchQuery}
          onChangeText={(text) => setSearchQuery(text)}
        />
      </View>
      <View
        style={{
          marginTop: 24,
          width: "90%",
          alignSelf: "center",
          marginBottom: 16,
        }}
      >
        <NoteComponent2
          text={"Exchange rates shown are for informational purposes only."}
        />
      </View>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View
          style={{
            width: "90%",
            backgroundColor: colors.white,
            alignSelf: "center",
            borderRadius: 12,
            marginBottom: 200,
          }}
        >
          <View style={{ paddingTop: 12, paddingBottom: 12, paddingLeft: 16 }}>
            <P style={{ fontSize: 13, color: colors.dGray }}>Exchange rate</P>
          </View>
          <View
            style={{
              width: "100%",
              flexDirection: "row",
              alignItems: "center",
              paddingLeft: 16,
              paddingRight: 16,
              justifyContent: "space-between",
              marginBottom: 12,
            }}
          >
            {rContries.map((item) => (
              <TouchableOpacity
                key={`currency-option-${item.currencyCode}`}
                style={{
                  width: "48%",
                  paddingHorizontal: 16,
                  paddingVertical: 8,
                  borderWidth: 1,
                  borderColor:
                    activeRate.symbol == item.symbol
                      ? colors.primary
                      : colors.stroke,
                  borderRadius: 8,
                  flexDirection: "row",
                  alignItems: "center",
                }}
                onPress={() => {
                  setActiveRate(item);
                }}
              >
                <Image
                  source={item.flag}
                  style={{
                    width: 24,
                    height: 24,
                    borderRadius: 100,
                    marginRight: 8,
                  }}
                />
                <P style={{ fontSize: 12 }} numberOfLines={1}>
                  {item.currencyCode}
                </P>
                <SvgXml
                  xml={
                    item.symbol === activeRate.symbol ? svg.checked : svg.check
                  }
                  style={{ position: "absolute", right: 16 }}
                />
              </TouchableOpacity>
            ))}
          </View>
          <View style={styles.rateNav}>
            <P style={{ fontSize: 13, color: colors.dGray, paddingLeft: 16 }}>
              Currency
            </P>
            <View style={{ flexDirection: "row", alignItems: "center", width: "50%", justifyContent: "space-around" }}>
              <P style={styles.add}>Add</P>
              <P style={styles.add}>Send</P>
            </View>
          </View>
          <View>
            {loading ? (
              <View style={{
                alignItems: "center",
                justifyContent: "center",
                paddingVertical: 40
              }}>
                <ActivityIndicator size="large" color={colors.primary} />
              </View>
            ) : filteredCountries.length === 0 ? (
              <View style={{
                alignItems: "center",
                justifyContent: "center",
                paddingVertical: 40
              }}>
                <P style={{ color: colors.gray, fontSize: 14 }}>
                  No rates available
                </P>
              </View>
            ) : (
              filteredCountries.map((item, index) => (
                <React.Fragment key={`country-${item.currencyCode}-${index}`}>
                  <View
                    style={[
                      styles.rateItem,
                      {
                        borderBottomWidth:
                          index === filteredCountries.length - 1 ? 0 : 1,
                      },
                    ]}
                  >
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        width: "50%",
                      }}
                    >
                      <Image
                        source={item.flag}
                        style={{
                          width: 24,
                          height: 24,
                          marginRight: 8,
                          borderRadius: 100,
                          objectFit: "cover",
                        }}
                      />
                      <View>
                        <P
                          style={{ fontSize: 12, width: 120 }}
                          numberOfLines={1}
                        >
                          {item.country}
                        </P>
                        <P
                          style={{ fontSize: 12, width: 120 }}
                          numberOfLines={1}
                        >
                          {item.currencyCode} ~ {activeRate.currencyCode}
                        </P>
                      </View>
                    </View>
                    <View style={{ flexDirection: "row", width: "50%", justifyContent: "space-between" }}>
                      <TouchableOpacity
                        onPress={() =>
                          navigation.navigate("AddMoneyRate2", {
                            from: "AddMoneyRate",
                            activeRate: activeRate,
                            item: item,
                          })
                        }
                        style={{ width: "45%" }}
                      >
                        <View style={styles.rateCont}>
                          <P style={styles.rateText}>
                            {item?.buyRate != "N/A"
                              ? formatToTwoDecimals(item?.buyRate)
                              : formatToTwoDecimals(item?.buyRate)}
                          </P>
                        </View>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() =>
                          navigation.navigate("AddMoneyRate2", {
                            from: "SendMoneyRate",
                            item: item,
                            activeRate: activeRate,
                          })
                        }
                        style={{ width: "45%" }}
                      >
                        <View style={[styles.rateCont]}>
                          <P
                            // @ts-ignore
                            style={[
                              styles.rateText,
                              { marginRight: 0, borderColor: colors.red },
                            ]}
                          >
                           {item?.sellRate != "N/A"
                            ? formatToTwoDecimals(item?.sellRate)
                            : formatToTwoDecimals(item.sellRate)}
                          </P>
                        </View>
                      </TouchableOpacity>
                    </View>
                  </View>
                </React.Fragment>
              ))
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
  },

  customInput: {
    width: "90%",
    height: 44,
    backgroundColor: colors.white,
    alignSelf: "center",
    borderRadius: 12,
    marginTop: 16,
    paddingLeft: 16,
    paddingRight: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  input: {
    width: "80%",
    height: 44,
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
  },
  rateNav: {
    width: "100%",
    backgroundColor: colors.secBackground,
    flexDirection: "row",
    paddingTop: 12.5,
    paddingBottom: 12.5,
    justifyContent: "space-between",
  },
  rateItem: {
    width: "100%",
    paddingVertical: 24,
    paddingHorizontal: 24,
    flexDirection: "row",
    justifyContent: "space-between",
    borderBottomWidth: 1,
    borderBottomColor: colors.stroke,
  },
  rateText: {
    fontSize: 11,
    borderWidth: 0,
    textAlign: "center",
  },
  add: {
    fontSize: 12,
    // paddingLeft: 30,
    // paddingRight: 30,
  },
  rateCont: {
    width: "100%",
    height: 34,
    borderWidth: 1,
    borderColor: colors.green,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
});
