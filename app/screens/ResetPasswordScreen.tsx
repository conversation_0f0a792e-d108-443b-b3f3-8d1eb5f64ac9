import React, { useRef, useState, useEffect } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  Dimensions,
  Keyboard,
} from "react-native";
import Div from "../components/Div";
import { colors } from "../config/colors";
import P from "../components/P";
import { fonts } from "../config/Fonts";
import Button from "../components/Button";
import Link from "../components/Link";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import Input from "../components/Input";
import BottomComponent from "../components/BottomComponent";
import * as yup from "yup";
import { Formik } from "formik";

import { ResetPassword } from "../RequestHandlers/Authentication";
import { useToast } from "../context/ToastContext";
import AuthHeader from "../components/AuthHeader";
import H4 from "../components/H4";
import PasswordValidation from "../components/PasswordValidation";
import { withApiErrorToast } from "../Utils/withApiErrorToast";

const screenHeight = Dimensions.get("window").height;
export default function ResetPasswordScreen({ navigation, route }) {
  const { handleToast } = useToast();
  const { email, activityId } = route.params;
  const [loading, setLoading] = useState(false);
  const resetSchema = yup.object().shape({
    newPassword: yup
      .string()
      .required("New password is required")
      .matches(
        /^(?=.*[0-9])(?=.*[*?!#$%&@^()\-_=+\\|[\]{};:/?.>])(?=.*[a-z])(?=.*[A-Z])[a-zA-Z0-9*?!#$%&@^()\-_=+\\|[\]{};:/?.>]{8,}$/,
        "Password must contain at least 8 characters, including letters, numbers, and special characters, with at least one uppercase letter"
      ),
    confirmPassword: yup
      .string()
      .required("Type your password again")
      .oneOf([yup.ref("newPassword"), null], "Passwords must match"),
  });
  return (
    <View style={styles.body}>
      <Div style={{ height: screenHeight }}>
        <Formik
          initialValues={{
            email: email, // email is being passed as a prop to the initial value
            newPassword: "",
            confirmPassword: "",
          }}
          validationSchema={resetSchema}
          onSubmit={async (values, actions) => {
            Keyboard.dismiss();
            setLoading(true);
            try {
              const body = { ...values, activityId: activityId };
              const resetP = await withApiErrorToast(
                ResetPassword(body),
                handleToast
              );
              if (resetP.status === true) {
                setLoading(false);
                handleToast(resetP.message, "success");
                setTimeout(() => {
                  navigation.navigate("NewLoginScreen");
                }, 500);
              } else {
                setLoading(false);
                handleToast(resetP.message, "error");
              }
            } catch (error) {
              setLoading(false);
              handleToast("Network error", "error");
            }finally{
              setLoading(false)
            }
          }}
        >
          {(formikProps) => (
            <ScrollView style={styles.container}>
              <AuthHeader style={{ width: "95%" }} navigation={navigation} />
              <View
                style={{
                  width: "90%",
                  justifyContent: "center",
                  alignSelf: "center",
                  marginTop: 8,
                  alignItems: "center",
                }}
              >
                <H4 style={styles.text1}>Resets password</H4>
                <P style={styles.text2}>
                  Create a new password to secure your account
                </P>
              </View>
              <View style={styles.components}>
                <Input
                  placeholder="*******"
                  label="New password"
                  inputStyle={{ width: "85%" }}
                  // contStyle={{ marginBottom: 32 }}
                  type={"password"}
                  onChangeText={formikProps.handleChange("newPassword")}
                  value={formikProps.values.newPassword}
                  onBlur={formikProps.handleBlur("newPassword")}
                  error={
                    formikProps.errors.newPassword &&
                    formikProps.touched.newPassword
                  }
                  showPasswordStrength={true}
                />
                <PasswordValidation password={formikProps.values.newPassword} />
                {formikProps.errors.newPassword &&
                  formikProps.touched.newPassword && (
                    <P style={styles.errorText}>
                      {formikProps.errors.newPassword}
                    </P>
                  )}
                <Input
                  placeholder="*******"
                  label="Confirm new password"
                  inputStyle={{ width: "85%" }}
                  contStyle={{ marginTop: 16 }}
                  type={"password"}
                  onChangeText={formikProps.handleChange("confirmPassword")}
                  value={formikProps.values.confirmPassword}
                  onBlur={formikProps.handleBlur("confirmPassword")}
                  error={
                    formikProps.errors.confirmPassword &&
                    formikProps.touched.confirmPassword
                  }
                />
                {formikProps.errors.confirmPassword &&
                  formikProps.touched.confirmPassword && (
                    <P style={styles.errorText}>
                      {formikProps.errors.confirmPassword}
                    </P>
                  )}
                <View style={{ marginTop: 32 }}>
                  <Button
                    btnText="Reset password"
                    onPress={formikProps.handleSubmit}
                    loading={loading}
                  />
                </View>

                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "center",
                    marginTop: 32,
                  }}
                >
                  <P
                    style={{
                      fontSize: 12,
                      lineHeight: 22.4,
                      fontFamily: fonts.poppinsRegular,
                    }}
                  >
                    Remember password?{" "}
                  </P>
                  <Link
                    style={{
                      fontSize: 12,
                      lineHeight: 21,
                      textDecorationLine: "underline",
                    }}
                    onPress={() => navigation.navigate("NewLoginScreen")}
                  >
                    Login
                  </Link>
                </View>
              </View>
            </ScrollView>
          )}
        </Formik>
        <BottomComponent navigation={navigation} />
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
    height: screenHeight,
  },

  text1: {
    fontSize: 20,
    fontFamily: fonts.poppinsBold,
    lineHeight: 30,
  },
  text2: {
    fontSize: 14,
    lineHeight: 22.4,
    fontFamily: fonts.poppinsRegular,
    textAlign: "center",
    paddingHorizontal: 40,
  },
  components: {
    width: "90%",
    marginTop: 24,
    alignSelf: "center",
    // backgroundColor:"red"
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
});
