import React, { useEffect, useState } from "react";
import { View, StyleSheet, Dimensions, ScrollView, Image } from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import InputCard from "../../components/InputCard";
import Keyboard from "../../components/Keyboard";
import Button from "../../components/Button";
import { colors } from "../../config/colors";
import {
  GetRateByCountry,
  GetRateById,
  GetYellowCardEstimatedFee,
} from "../../RequestHandlers/Wallet";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import useDebounce from "../../components/Debounce";
import { useToast } from "../../context/ToastContext";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import {
  formatNumberWithCommas,
  formatToTwoDecimals,
} from "../../Utils/numberFormat";

const baseHeight = 802;
const { width, height } = Dimensions.get("window");
export default function AddMoneyAmountScreen1({ navigation, route }) {
  const { country, symbol, currencyCode, networkID, max, min, aplhaCode2 } =
    route?.params;
  const { handleToast } = useToast();
  const [inputValue, setInputValue] = useState("0");
  const [error, setError] = useState(false);
  const [isUsdInput, setIsUsdInput] = useState(true);
  const [ngnRate, setNgnRate] = useState(0);
  const [localAmount, setLocalAmount] = useState(0.0);
  const [localFee, setLocalFee] = useState(0);
  const [fee, setFee] = useState<any>([]);
  const debouncedValue = useDebounce(Number(inputValue), 500);
  const debouncedValue2 = useDebounce(Number(inputValue) / ngnRate, 500);

  const formatNumber = (value) => {
    value = value?.toString();
    return value?.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };
  const formatNumberWithDecimal = (value, decimalPlaces = 2) => {
    if (!isNaN(value)) {
      return Number(value)
        .toFixed(decimalPlaces)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    return "0.00";
  };
  const handleKeyPress = (key) => {
    setError(false);
    if (key === "←") {
      setInputValue((prev) => (prev.length > 1 ? prev.slice(0, -1) : "0"));
    } else if (key === "Enter") {
    } else {
      setInputValue((prev) => {
        let newValue = prev === "0" && key !== "." ? key : prev + key;
        newValue = newValue.replace(/[^0-9.]/g, ""); // Remove any non-numeric characters
        return newValue;
      });
    }
  };
  const getFee = async () => {
    try {
      const res = await withApiErrorToast(
        GetYellowCardEstimatedFee(
          aplhaCode2,
          debouncedValue,
          "bank",
          "DEPOSIT",
          isUsdInput ? "USD" : currencyCode
        ),
        handleToast
      );
      if (res.fee) {
        setNgnRate(res.rate);
        setFee(res);
        if (Number(inputValue) === 0) {
          setLocalAmount(0.0);
          setLocalFee(0);
        } else {
          setLocalAmount(res.totalAmountInLocal);
          if (isUsdInput) {
          }
          setLocalFee(res.feeInLocal);
        }
      }
    } catch (error) {}
  };
  // const getRateById = async () => {
  //   try {
  //     const rate = await GetRateById(currencyCode);
  //     const sfxRate = await GetRateByCountry(currencyCode, "yellow-card");
  //     console.log(sfxRate);
  //     sfxRate.map((item, index) => {
  //       if (item.type === "buy") {
  //         console.log(item);
  //         setNgnRate(item.amount);
  //       }
  //     });
  //     // if (rate) {
  //     //   setNgnRate(rate[0].buy);
  //     // }
  //   } catch (error) {
  //     ;
  //   }
  // };

  useEffect(() => {
    getFee();
  }, [isUsdInput ? debouncedValue : debouncedValue2]);

  // useEffect(() => {
  //   getRateById();
  // }, []);

  const toggleCurrency = () => {
    setIsUsdInput(!isUsdInput);
    setInputValue("0"); // Reset input value when toggling
  };

  const convertedValue = isUsdInput
    ? formatNumberWithDecimal(Number(inputValue) * ngnRate)
    : formatNumberWithDecimal(Number(inputValue) / ngnRate);

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Amount" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.inputCardWrap}>
              <InputCard
                headerText="How much do you want to send"
                onTogglePress={toggleCurrency}
                toggleStyle={{ top: "50%" }}
                amountValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 30,
                        lineHeight: 40,
                        marginRight: 4,
                      }}
                    >
                      {isUsdInput
                        ? `$${formatNumber(Number(inputValue))}`
                        : `${symbol}${formatNumber(inputValue)}`}
                      <P style={{ lineHeight: 48 }}>
                        {isUsdInput ? "USD" : currencyCode}
                      </P>
                    </P>
                  </>
                }
                convertedValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 16,
                        lineHeight: (24 / baseHeight) * height,
                        marginRight: 4,
                      }}
                    >
                      {isUsdInput
                        ? `${symbol}${formatNumberWithCommas(
                            Number(inputValue) * ngnRate
                          )}`
                        : `$${
                            inputValue === "0"
                              ? 0?.toFixed(2)
                              : formatToTwoDecimals(
                                  Number(inputValue) / ngnRate
                                )
                          }`}
                      <P
                        style={{
                          lineHeight: (24 / baseHeight) * height,
                          fontSize: 12,
                        }}
                      >
                        {isUsdInput ? currencyCode : "USD"}
                      </P>
                    </P>
                  </>
                }
                extraComponent={
                  <View style={styles.fee}>
                    <View style={{ width: 24, alignItems: "center" }}>
                      <SvgXml xml={svg.coin1} style={{ marginRight: 8 }} />
                    </View>
                    <P style={{ fontSize: 12, color: colors.gray }}>
                      Charges: {formatNumberWithCommas(localFee) || 0.0}{" "}
                      {currencyCode}
                    </P>
                  </View>
                }
                text1={`Exchange rate: 1 USD ~ ${ngnRate} ${currencyCode}`}
                error={error}
              />
            </View>
            <View style={styles.bottom}>
              <View style={{ width: "90%", alignSelf: "center" }}>
                <Keyboard onKeyPress={handleKeyPress} />
              </View>
              <View
                style={{
                  width: "80%",
                  alignSelf: "center",
                  marginTop: 16,
                }}
              >
                <Button
                  btnText="Next"
                  onPress={() => {
                    if (inputValue === "0") {
                      setError(true);
                    } else {
                      // Convert input to local currency amount
                      const localAmount = isUsdInput
                        ? Number(inputValue) * ngnRate
                        : Number(inputValue);

                      // Calculate effective minimum (default to $3 USD if no min from API)
                      let effectiveMinLocal = min !== 0 ? min : 3 * ngnRate;
                      let effectiveMinUSD = effectiveMinLocal / ngnRate;

                      if (effectiveMinUSD < 3) {
                        effectiveMinUSD = 3;
                        effectiveMinLocal = 3 * ngnRate;
                      }

                      // Calculate effective maximum (capped at $3000 USD)
                      const maxUSD = max !== 0 ? max / ngnRate : 0;
                      const effectiveMaxUSD = maxUSD > 3000 ? 3000 : maxUSD;
                      const effectiveMaxLocal = effectiveMaxUSD * ngnRate;

                      // Check min/max limits
                      if (max !== 0 && localAmount > effectiveMaxLocal) {
                        handleToast(
                          `The maximum amount to deposit is ${
                            isUsdInput ? "$" : symbol
                          }${
                            isUsdInput
                              ? effectiveMaxUSD.toFixed(2)
                              : formatNumberWithDecimal(effectiveMaxLocal)
                          }${isUsdInput ? "USD" : currencyCode}`,
                          "error"
                        );
                        setError(true);
                      } else if (localAmount < effectiveMinLocal) {
                        handleToast(
                          `The minimum amount to deposit is ${
                            isUsdInput ? "$" : symbol
                          }${
                            isUsdInput
                              ? effectiveMinUSD.toFixed(2)
                              : formatNumberWithDecimal(effectiveMinLocal)
                          }${isUsdInput ? "USD" : currencyCode}`,
                          "error"
                        );
                        setError(true);
                      } else {
                        navigation.navigate("ConfirmDetailScreen", {
                          country,
                          inputValue: inputValue,
                          ngnRate,
                          currencyCode,
                          networkID,
                          symbol,
                          aplhaCode2,
                          fee,
                          isUsdInput,
                        });
                      }
                    }
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}
const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    height: (92 * height) / 100,
    backgroundColor: "rgba(247, 244, 255, 1)",
    paddingTop: 16,
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
  },
  bottom: {
    width,
    top: (100 / baseHeight) * height,
  },
  fee: {
    flexDirection: "row",
    alignItems: "center",
    // width: "100%"
    padding: 4,
    paddingLeft: 0,
  },
});
