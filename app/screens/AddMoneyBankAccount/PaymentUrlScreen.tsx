import React, { useState, useRef, useEffect } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  <PERSON>ert,
  BackHandler,
  Text,
} from "react-native";
import { WebView } from "react-native-webview";
import { useFocusEffect } from "@react-navigation/native";
import { colors } from "../../config/colors";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import Div from "../../components/Div";
import P from "../../components/P";
import { useCallback } from "react";

const { width, height } = Dimensions.get("window");

interface PaymentUrlScreenProps {
  navigation: any;
  route: {
    params: {
      url: string;
      title?: string;
      onSuccess?: () => void;
      onCancel?: () => void;
      onError?: (error: any) => void;
      transaction?: any;
    };
  };
}

export default function PaymentUrlScreen({ navigation, route }: PaymentUrlScreenProps) {
  const { url, title = "Payment", onSuccess, onCancel, onError, transaction } = route.params || {};
  const [loading, setLoading] = useState(true);
  const [hasLoaded, setHasLoaded] = useState(false);
  const webViewRef = useRef<WebView>(null);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Disable iOS swipe back gesture
  useEffect(() => {
    navigation.setOptions({
      gestureEnabled: false,
    });
  }, [navigation]);

  // Handle hardware back button
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        // Reset navigation stack to avoid loading issues
        // @ts-ignore
        navigation.reset({
          index: 0,
          routes: [{ name: "BottomTabNavigator" }],
        });
        return true;
      };
      // Disable iOS swipe back gesture
      navigation.setOptions({
        gestureEnabled: false,
      });
      // Handle Android back button
      BackHandler.addEventListener("hardwareBackPress", onBackPress);

      return () => {
        BackHandler.removeEventListener("hardwareBackPress", onBackPress);
      };
    }, [navigation])
  );

  const handleNavigationStateChange = (navState: any) => {
    // Check for success/failure URLs or patterns
    const currentUrl = navState.url.toLowerCase();
    console.log(currentUrl, transaction?.payment?.redirectUrl) ;
    if (currentUrl?.includes(transaction?.payment?.redirectUrl)) {
      navigation.navigate("MoneySentScreen1", {
        transaction: transaction?.transaction,
      })
    }
  };

  const handleError = (syntheticEvent: any) => {
    const { nativeEvent } = syntheticEvent;
    console.error("WebView error:", nativeEvent);
    
    if (onError) {
      onError(nativeEvent);
    }
    
    Alert.alert(
      "Payment Error",
      "There was an error loading the payment page. Please try again.",
      [
        {
          text: "Retry",
          onPress: () => {
            if (webViewRef.current) {
              webViewRef.current.reload();
            }
          },
        },
        {
          text: "Cancel",
          style: "cancel",
          onPress: () => navigation.goBack(),
        },
      ]
    );
  };

  const handleLoadStart = () => {
    console.log("WebView load started");
    // setLoading(true);
    setHasLoaded(false);

    // Clear any existing timeout
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }

    // Set a timeout to prevent infinite loading
    loadingTimeoutRef.current = setTimeout(() => {
      console.log("WebView load timeout - forcing loading to false");
      setLoading(false);
      setHasLoaded(true);
    }, 5000); // 15 second timeout
  };

  const handleLoadEnd = () => {
    console.log("WebView load ended");
    setLoading(false);
    setHasLoaded(true);

    // Clear timeout since loading completed
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }
  };

  const handleLoad = () => {
    console.log("WebView loaded successfully");
    setLoading(false);
    setHasLoaded(true);

    // Clear timeout since loading completed
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, []);

  if (!url) {
    Alert.alert("Error", "No payment URL provided", [
      { text: "OK", onPress: () => navigation.goBack() },
    ]);
    return null;
  }

  return (
    <View style={styles.container}>
      <Div>
        <AuthenticationHedear
          text={title}
          navigation={navigation}
          goHome={true}
          // onBackPress={() => {
          //   Alert.alert(
          //     "Cancel Payment",
          //     "Are you sure you want to cancel this payment?",
          //     [
          //       {
          //         text: "Continue Payment",
          //         style: "cancel",
          //       },
          //       {
          //         text: "Cancel Payment",
          //         style: "destructive",
          //         onPress: () => {
          //           if (onCancel) {
          //             onCancel();
          //           }
          //           navigation.goBack();
          //         },
          //       },
          //     ]
          //   );
          // }}
        />
        
        <View style={styles.webViewContainer}>
          {loading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              {/* Add text to help debug */}
              <View style={{ marginTop: 16, alignItems: 'center' }}>
                <P style={{ fontSize: 14, color: colors.gray }}>Loading payment page...</P>
                {hasLoaded && (
                  <P style={{ fontSize: 12, color: colors.gray2, marginTop: 4 }}>
                    If this takes too long, please check your internet connection
                  </P>
                )}
              </View>
            </View>
          )}
          
          <WebView
            ref={webViewRef}
            source={{ uri: url }}
            style={styles.webView}
            onNavigationStateChange={handleNavigationStateChange}
            onError={handleError}
            onLoadStart={handleLoadStart}
            onLoadEnd={handleLoadEnd}
            onLoad={handleLoad}
            startInLoadingState={false}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            allowsBackForwardNavigationGestures={true}
            scalesPageToFit={true}
            mixedContentMode="compatibility"
            userAgent="Mozilla/5.0 (Linux; Android 10; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36"
            // Android specific props
            cacheEnabled={false}
            incognito={true}
            thirdPartyCookiesEnabled={true}
            // Additional timeout handling
            onHttpError={(syntheticEvent) => {
              console.log("HTTP Error:", syntheticEvent.nativeEvent);
              setLoading(false);
            }}
            onRenderProcessGone={(syntheticEvent) => {
              console.log("Render process gone:", syntheticEvent.nativeEvent);
              setLoading(false);
            }}
          />
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  webViewContainer: {
    flex: 1,
    position: "relative",
  },
  webView: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.white,
    zIndex: 1,
  },
});
