import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { svg } from "../../config/Svg";
import { SvgXml } from "react-native-svg";
import NoteComponent from "../../components/NoteComponent";
import Input from "../../components/Input";
import BottomSheet from "../../components/BottomSheet";
import CountrySelect from "../../components/CountrySelect";
import Button from "../../components/Button";
import NoteComponent2 from "../../components/NoteComponent2";
import { NotBaCountires } from "../../components/NotBaCountirs";
import { GetChannels, GetMinMAx } from "../../RequestHandlers/Wallet";
import Loader from "../../components/ActivityIndicator";
import ExtraId from "../../components/ExtraId";
import { CheckId } from "../../RequestHandlers/User";
import { useFocusEffect } from "@react-navigation/native";
import { GetRateByCountry } from "../../RequestHandlers/Wallet";
import { formatNumber } from "../../Utils/formatNumber";
import H4 from "../../components/H4";
import { formatToTwoDecimals } from "../../Utils/numberFormat";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import { useToast } from "../../context/ToastContext";

const { width, height, fontScale } = Dimensions.get("window");
export default function AddMoneyBankAccountScreen({ navigation }) {
  const [paymentType, setPaymentType] = useState("USD coin");
  const [flag, setFlag] = useState(require("../../assets/nigeria.png"));
  const [country, setCountry] = useState("");
  const [showCountries, setShowCountries] = useState(false);
  const activeFlagRef = useRef<any | null>(null);
  const countryRef = useRef<String | null>(null);
  const yellowCardRef = useRef<String | null>(null);
  const currencyCodeRef = useRef<String | null>(null);
  const symbolRef = useRef<String | null>(null);
  const alphaCodeRef = useRef<String | null>(null);
  const [yellowCard, setYellowCard] = useState("");
  const [currencyCode, setCurrencyCode] = useState("");
  const [networkID, setNetworkID] = useState("");
  const [aplhaCode2, setAlphaCode2] = useState("");
  const [symbol, setSymbol] = useState("");
  const [loading, setLoading] = useState(false);
  const [max, setMax] = useState(0);
  const [min, setMin] = useState(0);
  const [showMultiOption, setShowMultiOption] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [showReq, setShowReq] = useState(false);
  const [NextState, setNextState] = useState(null);
  const [showChannelError, setShowChannelError] = useState(false);
  const [showYellowCardWarning, setShowYellowCardWarning] = useState(false);
  const [gateWayError, setGateWayError] = useState(false);
  const { handleToast } = useToast();
  const [options, setOptions] = useState([
    {
      time: "5 min",
      rate: "₦0",
      minMax: "$3 - $3,000",
      requirement: "NIN and BVN",
      type: "yellowCard",
    },
    {
      time: "15 min",
      rate: "₦0",
      minMax: "$15 - $3,000",
      requirement: "BVN or passport",
      type: "link",
    },
  ]);
  const paymentTypes = [
    // { name: "USD(Tether)", rate: "1 USDT ~ 1 USD", icon: svg.tather },
    { name: "USD coin", rate: "1 USDC ~ 1 USD", icon: svg.usdCoin },
  ];
  const handleActiveCountry = (newActiveType: string | null) => {
    setCountry(newActiveType);
  };
  const handleActiveFlag = (newActiveType: any | null) => {
    if (newActiveType) {
      setFlag(newActiveType);
    }
  };
  const handleActiveYellowCard = (newActiveType: string | null) => {
    setYellowCard(newActiveType);
  };
  const handleActiveCurrencyCode = (newActiveType: string | null) => {
    setCurrencyCode(newActiveType);
  };
  const handleActiveSymbol = (newActiveType: string | null) => {
    setSymbol(newActiveType);
  };
  const handleActiveAplhaCode2 = (newActiveType: string | null) => {
    setAlphaCode2(newActiveType);
    if (newActiveType === "NG") {
      setShowMultiOption(true);
      setSelectedOption(null);
    } else {
      setShowMultiOption(false);
    }
  };
  useEffect(() => {
    countryRef.current = country;
  }, [country]);

  useEffect(() => {
    yellowCardRef.current = yellowCard;
  }, [yellowCard]);
  useEffect(() => {
    alphaCodeRef.current = aplhaCode2;
  }, [aplhaCode2]);
  useEffect(() => {
    currencyCodeRef.current = currencyCode;
  }, [currencyCode]);
  useEffect(() => {
    symbolRef.current = symbol;
  }, [symbol]);

  useEffect(() => {
    activeFlagRef.current = flag;
  }, [flag]);

  useEffect(() => {
    if (!country) {
      setFlag(require("../../assets/nigeria.png"));
    }
  }, [country]);

  const getChannels = async (code) => {
    setLoading(true);
    try {
      const channels = await withApiErrorToast(GetChannels(code), handleToast);
      if (channels.error) {
        setGateWayError(true);
      } else {
        setGateWayError(false);
      }
      const result = channels.find(
        (item) =>
          item.rampType === "deposit" &&
          (item.channelType === "p2p" ||
            item.channelType === "bank" ||
            item.channelType === "eft")
      );
      setNetworkID(result.id);
    } catch (error) {
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  const getMinMax = async (code) => {
    try {
      const res = await GetMinMAx(code, "bank", "DEPOSIT");
      if (res.maximumAmountInLocal) {
        setMin(res.minimumAmountInLocal);
        setMax(res.maximumAmountInLocal);
      }
    } catch (error) {}
  };

  const checkId = async () => {
    try {
      const res = await CheckId();
      const isNigerian = country === "Nigeria";
      const hasRequiredDocs = isNigerian
        ? res.bvn && res.nin && res.phoneNumber
        : res.phoneNumber;
      setNextState(hasRequiredDocs ? 1 : 0);
    } catch (error) {
      console.error("Failed to check ID:", error);
      setNextState(0);
    }
  };
  const fetchRates = async () => {
    try {
      const yellowCardRates = await withApiErrorToast(
        GetRateByCountry("NGN", "yellow-card"),
        handleToast
      );
      const linkRates = await GetRateByCountry("NGN", "link");
      const yellowCardOption = yellowCardRates
        .filter((item) => item.type === "buy")
        .map((item) => ({
          time: "5 min",
          rate: `₦${formatToTwoDecimals(item.amount)}`,
          minMax: "$3 - $3,000",
          requirement: "NIN and BVN",
          type: "yellowCard",
        }))[0];
      const linkOption = linkRates
        .filter((item) => item.type === "buy")
        .map((item) => ({
          time: "15 min",
          rate: `₦${formatToTwoDecimals(item.amount)}`,
          minMax: "$15 - $3,000",
          requirement: "BVN or passport",
          type: "link",
        }))[0];
      setOptions([yellowCardOption, linkOption]);
    } catch (error) {
      console.error("Error fetching rates:", error);
    }
  };

  useEffect(() => {
    fetchRates();
  }, []);
  useFocusEffect(
    useCallback(() => {
      checkId();
    }, [])
  );

  useEffect(() => {
    if (yellowCard) {
      if (yellowCard === "NG" && selectedOption === "link") {
        return;
      } else {
        getChannels(yellowCard);
        getMinMax(yellowCard);
      }
    }
  }, [yellowCard, selectedOption]);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Bank account" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <View style={{ marginBottom: 16 }}>
                <NoteComponent2
                  text={`Please note that you will receive USD in your ${paymentType} account.`}
                />
              </View>
              {/* {paymentTypes.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => setPaymentType(item.name)}
                >
                  <View
                    style={[
                      styles.pyItem,
                      {
                        borderColor:
                          paymentType === item.name
                            ? colors.primary
                            : colors.stroke,
                        backgroundColor:
                          paymentType === item.name ? "#F7F4FF" : "transparent",
                      },
                    ]}
                  >
                    <SvgXml xml={item.icon} />
                    <View style={{ marginLeft: 8 }}>
                      <P style={styles.pyName}>{item.name}</P>
                      <P style={styles.rate}>{item.rate}</P>
                    </View>
                    {paymentType === item.name && (
                      <SvgXml
                        xml={svg.ppTick}
                        style={{ position: "absolute", right: 16 }}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              ))} */}

              <TouchableOpacity
                onPress={() => {
                  setShowCountries(true);
                }}
              >
                <View>
                  <Input
                    value={country}
                    label="Country"
                    placeholder="Nigeria"
                    inputStyle={{ width: "65%", color: "#161817" }}
                    editable={false}
                    leftIcon={
                      <Image
                        source={flag}
                        style={{
                          width: 24,
                          height: 24,
                          marginLeft: 14,
                          objectFit: "cover",
                          borderRadius: 100,
                          opacity: country === "" ? 0.5 : 1,
                        }}
                      />
                    }
                    rightIcon={
                      <View
                        style={{
                          width: "15%",
                          height: "100%",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <SvgXml xml={svg.dropDown} />
                      </View>
                    }
                  />
                </View>
              </TouchableOpacity>
              <>
                {country === "Nigeria" && (
                  <>
                    {showMultiOption && (
                      <View style={styles.optionCont}>
                        <P
                          style={{
                            fontSize: 12,
                            fontFamily: fonts.poppinsRegular,
                            lineHeight: 18,
                            marginBottom: 6,
                          }}
                        >
                          Select preferred method
                        </P>

                        {options.map((item, index) => {
                          return (
                            <TouchableOpacity
                              key={index}
                              style={[
                                styles.option,
                                {
                                  borderColor:
                                    selectedOption === item.type
                                      ? colors.primary
                                      : "#E6E5E5",
                                },
                              ]}
                              onPress={() => {
                                setSelectedOption(item.type);
                              }}
                            >
                              <View style={{ width: "90%", gap: 4 }}>
                                <View style={styles.opHolder}>
                                  <View
                                    style={{
                                      gap: 4,
                                      flexDirection: "row",
                                      alignItems: "center",
                                    }}
                                  >
                                    <SvgXml xml={svg.timer} />
                                    <P style={styles.opText}>Time</P>
                                  </View>
                                  <View style={{ width: 100 }}>
                                    <P style={styles.vText}>{item.time}</P>
                                  </View>
                                </View>
                                <View style={styles.opHolder}>
                                  <View
                                    style={{
                                      gap: 4,
                                      flexDirection: "row",
                                      alignItems: "center",
                                    }}
                                  >
                                    <SvgXml xml={svg.rate} />
                                    <P style={styles.opText}>Rate</P>
                                  </View>
                                  <View style={{ width: 100 }}>
                                    <P style={styles.vText}>{item.rate}</P>
                                  </View>
                                </View>
                                <View style={styles.opHolder}>
                                  <View
                                    style={{
                                      gap: 4,
                                      flexDirection: "row",
                                      alignItems: "center",
                                    }}
                                  >
                                    <SvgXml xml={svg.dolls} />
                                    <P style={styles.opText}>Min - max</P>
                                  </View>
                                  <View style={{ width: 100 }}>
                                    <P style={styles.vText}>{item.minMax}</P>
                                  </View>
                                </View>
                                <View style={styles.opHolder}>
                                  <View
                                    style={{
                                      gap: 4,
                                      flexDirection: "row",
                                      alignItems: "center",
                                    }}
                                  >
                                    <SvgXml xml={svg.paper} />
                                    <P style={styles.opText}>Requirement</P>
                                  </View>
                                  <View style={{ width: 100 }}>
                                    <P style={styles.vText}>
                                      {item.requirement}
                                    </P>
                                  </View>
                                </View>
                              </View>
                              <SvgXml
                                width={16}
                                height={16}
                                xml={
                                  selectedOption === item.type
                                    ? svg.checked
                                    : svg.check
                                }
                              />
                            </TouchableOpacity>
                          );
                        })}
                      </View>
                    )}
                  </>
                )}
              </>
            </View>
            <View style={{ width: "80%", alignSelf: "center", marginTop: 32 }}>
              <Button
                btnText="Next"
                onPress={() => {
                  // Handle gateway errors
                  if (selectedOption !== "link" && gateWayError) {
                    setShowChannelError(true);
                    return;
                  }

                  // Handle verification requirements
                  if (
                    NextState === 0 &&
                    (selectedOption === "yellowCard" || !showMultiOption)
                  ) {
                    setShowReq(true);
                    return;
                  }

                  // Handle Nigeria-specific flows
                  if (country === "Nigeria" && showMultiOption) {
                    if (selectedOption === "link") {
                      navigation.navigate("LinkAddAmount");
                      return;
                    }
                    if (selectedOption === "yellowCard") {
                      setShowYellowCardWarning(true);
                      return;
                    }
                  }

                  // Default navigation
                  navigation.navigate("AddMoneyAmountScreen1", {
                    country,
                    networkID,
                    currencyCode,
                    aplhaCode2,
                    symbol,
                    max,
                    min,
                  });
                }}
                disabled={
                  country === "" ||
                  (country === "Nigeria" && selectedOption === null)
                }
              />
            </View>
          </View>
        </ScrollView>
      </Div>
      {showReq && (
        <ExtraId
          type={country !== "Nigeria" ? "phone" : "other"}
          extraFunction={() => {
            setShowReq(false);
            setShowMultiOption(false);
            if (country === "Nigeria" && selectedOption === "yellowCard") {
              setShowYellowCardWarning(true);
            } else {
              navigation.navigate("AddMoneyAmountScreen1", {
                country: country,
                networkID: networkID,
                currencyCode: currencyCode,
                aplhaCode2: aplhaCode2,
                symbol: symbol,
                max: max,
                min: min,
              });
            }
          }}
          close={() => {
            setShowReq(false);
            setShowMultiOption(true);
          }}
        />
      )}
      <BottomSheet
        isVisible={showCountries}
        showBackArrow={false}
        backspaceText="Select country"
        onClose={() => setShowCountries(false)}
        modalContentStyle={{ height: "65%" }}
        extraModalStyle={{ height: "63%" }}
        components={
          <CountrySelect
            excludedCountries={NotBaCountires}
            onActiveCountryChange={handleActiveCountry}
            onActiveFlag={handleActiveFlag}
            onActiveYellowCard={handleActiveYellowCard}
            onActiveCurrencyCode={handleActiveCurrencyCode}
            onSymbolChange={handleActiveSymbol}
            onActiveAlphaCode2Change={handleActiveAplhaCode2}
            onPress={(index: number) => {
              // Important: Close the modal AFTER all the callbacks have been processed
              setTimeout(() => {
                setShowCountries(false);
              }, 100);
            }}
          />
        }
      />
      <BottomSheet
        isVisible={showChannelError}
        showBackArrow={false}
        backspaceText=""
        onClose={() => setShowChannelError(false)}
        modalContentStyle={{ height: "55%" }}
        extraModalStyle={{ height: "53%" }}
        components={
          <View
            style={{
              width: "100%",
              alignItems: "center",
              justifyContent: "center",
              paddingTop: 34,
            }}
          >
            <SvgXml xml={svg.noCloud} width={44} height={44} />

            <P style={{ marginTop: 16 }}>Payment option unavailable!</P>
            <P
              style={{
                textAlign: "center",
                fontSize: 12,
                color: colors.gray,
                marginTop: 4,
                fontFamily: fonts.poppinsRegular,
              }}
            >
              We apologize for any inconvenience this may cause and appreciate
              your patience while we work to enhance our payment options.
            </P>

            <View style={{ marginTop: 32 }}>
              <Button
                btnText="Explore other payment options"
                onPress={() => {
                  setShowChannelError(false);
                  navigation.pop();
                }}
              />
            </View>
          </View>
        }
      />

      {/* Yellow Card Warning Bottom Sheet */}
      <BottomSheet
        isVisible={showYellowCardWarning}
        showBackArrow={false}
        backspaceText="Important Notice"
        onClose={() => setShowYellowCardWarning(false)}
        modalContentStyle={{ height: "60%" }}
        extraModalStyle={{ height: "57%" }}
        components={
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 24 }}
          >
            <View
              style={{
                width: "100%",
                alignItems: "center",
                justifyContent: "center",
                paddingTop: 24,
              }}
            >
              <SvgXml xml={svg.notice} />
              <H4
                style={{
                  marginTop: 16,
                  fontFamily: fonts.poppinsSemibold,
                  fontSize: 18,
                }}
              >
                Please note
              </H4>
              <P
                style={{
                  textAlign: "center",
                  fontSize: 12,
                  color: colors.black,
                  marginTop: 16,
                  fontFamily: fonts.poppinsRegular,
                  lineHeight: 22,
                }}
              >
                Deposit only from{" "}
                <P style={{ fontSize: 12, fontFamily: fonts.poppinsSemibold }}>
                  a bank account in your name.
                </P>{" "}
                The bank account must also display your full name. Avoid using
                POS, ATM, or digital bank accounts, as they may not include your
                name.
              </P>

              <P
                style={{
                  textAlign: "center",
                  fontSize: 12,
                  marginTop: 16,
                  fontFamily: fonts.poppinsMedium,
                  lineHeight: 22,
                }}
              >
                Deposits from accounts that don't match the name on your SFx
                Money App may result in a loss of funds.
              </P>

              <View style={{ marginTop: 32, width: "80%" }}>
                <Button
                  btnText="Next"
                  onPress={() => {
                    setShowYellowCardWarning(false);
                    // Navigate to amount input screen

                    navigation.navigate("AddMoneyAmountScreen1", {
                      country: country,
                      networkID: networkID,
                      currencyCode: currencyCode,
                      aplhaCode2: aplhaCode2,
                      symbol: symbol,
                      max: max,
                      min: min,
                    });
                  }}
                />
              </View>
            </View>
          </ScrollView>
        }
      />
      <Loader loading={true} visible={loading} />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: "100%",
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    padding: 24,
  },
  pyItem: {
    width: "100%",
    borderRadius: 6,
    borderWidth: 1,
    marginBottom: 16,
    alignItems: "center",
    flexDirection: "row",
    padding: 16,
  },
  pyName: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
  },
  rate: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
  },
  noteCont: {
    width: "100%",
    padding: 16,
    paddingTop: 8,
    paddingBottom: 8,
    borderRadius: 8,
  },
  optionCont: {
    width: "100%",
    marginTop: 16,
  },
  option: {
    width: "100%",
    paddingLeft: 12,
    paddingRight: 12,
    paddingTop: 8,
    paddingBottom: 8,
    borderWidth: 1,
    borderColor: "#E6E5E5",
    borderRadius: 8,
    marginBottom: 16,
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  opText: {
    fontSize: fontScale == 1 ? 11 : fontScale > 1 ? 10 : 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  opHolder: {
    width: "100%",
    flexDirection: "row",
    // justifyContent: "space-between",
    gap: 10,
    alignItems: "center",
    // backgroundColor: "red",
  },
  vText: {
    textAlign: "left",
    fontSize: fontScale == 1 ? 11 : fontScale > 1 ? 10 : 12,
  },
});
