import React, { useState, useEffect, useContext } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import Link from "../../components/Link";
import BottomComponent from "../../components/BottomComponent";
import BottomSheet from "../../components/BottomSheet";
import BottomSheetComponent from "../../components/BottomSheetComponent";
import ShareOption from "../../components/ShareOption";
import LongSheet from "../../components/LongSheet";
import H4 from "../../components/H4";
import * as Clipboard from "expo-clipboard";
import NoteComponent from "../../components/NoteComponent";
import NoteComponent2 from "../../components/NoteComponent2";
import { DepositContext } from "../../context/DepositeContext";
import { countries } from "../../components/counties";

const { width, height } = Dimensions.get("window");

export default function MobileMoneyTransactionDetails({ navigation }) {
  const { deposit } = useContext(DepositContext);
  const [showBtmSheet, setShowBtmSheet] = useState(false);
  const [longSheet, setLongSheet] = useState(false);
  const [isTransactionFaild, setIsTransactionFaild] = useState(null);
  const [isTransactionSuccessful, setIsTransactionSuccessful] = useState(false);
  const [isTranSocketHit, setIsTranSocketHit] = useState(null);
  const [moneyAdded, setMoneyAdded] = useState(false);
  const [isRefCopied, setIsRefCopied] = useState(false);
  const [isAccCopied, setIsAccCopied] = useState(false);
  const [refNUm, setRefNum] = useState("SF1122334455");
  const [accNum, setAccNum] = useState("1122334455");
  const [country, setCountry] = useState("");
  const formatNumber = (value) => {
    value = value?.toString();
    return value?.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0;
  };
  const [timeLeft, setTimeLeft] = useState(0);
  // Timer logic
  const calculateTimeLeft = (expiresAt: string | Date): number => {
    const expiryDate = new Date(expiresAt);
    const now = new Date();
    const diffInSeconds = Math.floor(
      (expiryDate.getTime() - now.getTime()) / 1000
    );

    return diffInSeconds > 0 ? diffInSeconds : 0;
  };

  useEffect(() => {
    if (deposit.payment?.source) {
      setAccNum(deposit.payment.source.accountNumber);
    }

    if (deposit?.payment?.expiresAt) {
      const expiresAt = deposit.payment.expiresAt;
      setTimeLeft(calculateTimeLeft(expiresAt));
      const intervalId = setInterval(() => {
        setTimeLeft((prevTimeLeft) => {
          if (prevTimeLeft <= 1) {
            clearInterval(intervalId);
            return 0;
          }
          return prevTimeLeft - 1;
        });
      }, 1000);
      return () => clearInterval(intervalId);
    }
  }, [deposit?.payment?.expiresAt]);

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}` || "unknown";
  };
  const getSymbolByCurrency = (fromCurrency: string) => {
    const matchedCountry = countries.find(
      (country) => country.currencyCode === fromCurrency
    );
    return matchedCountry ? matchedCountry.symbol : null;
  };
  const fromCurrency = deposit?.payment?.currency;
  const symbol = getSymbolByCurrency(fromCurrency);

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Transaction details"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <DetailCard
                headText={"Send money to SFx money app"}
                amount={
                  <>
                    <P style={{ fontSize: 32, lineHeight: 48, marginRight: 2 }}>
                      $
                      {deposit?.payment?.amount
                        ? formatNumber(deposit?.payment?.amount?.toFixed(2))
                        : 0}
                    </P>
                    <P style={{ marginTop: 5 }}>USD</P>
                  </>
                }
                convertedAmount={
                  <>
                    <P style={{ fontSize: 16, lineHeight: 24, marginRight: 2 }}>
                      {symbol}
                      {deposit?.payment?.convertedAmount
                        ? formatNumber(
                            deposit?.payment?.convertedAmount?.toFixed(2)
                          )
                        : 0}
                    </P>
                    <P style={{ marginTop: 2, fontSize: 12, lineHeight: 18 }}>
                      {deposit?.payment?.currency}
                    </P>
                  </>
                }
                timer={
                  <View
                    style={[
                      styles.timer,
                      {
                        backgroundColor: colors.secBackground,
                      },
                    ]}
                  >
                    {timeLeft > 0 ? (
                      <P style={styles.statusText}>
                        Expires in{" "}
                        <P
                          // @ts-ignore
                          style={[
                            styles.statusText,
                            {
                              color: colors.primary,
                              fontFamily: fonts.poppinsMedium,
                            },
                          ]}
                        >
                          {formatTime(timeLeft)}
                        </P>
                      </P>
                    ) : (
                      <P style={styles.statusText}>Expired</P>
                    )}
                  </View>
                }
                bottomComponent={
                  <>
                    <NoteComponent2 text="Only add money from an account matching your SFx account name" />
                    <View style={styles.desCont}>
                      <View style={styles.items}>
                        <SvgXml
                          xml={svg.userSquare}
                          style={{ marginRight: 8 }}
                        />
                        <View>
                          <P style={styles.holder}>Account name</P>
                          <P style={styles.value}>SFx mobile money</P>
                        </View>
                      </View>
                      <View style={styles.items}>
                        <SvgXml xml={svg.bank} style={{ marginRight: 8 }} />
                        <View>
                          <P style={styles.holder}>Service provider</P>
                          <P style={styles.value}>
                            {deposit?.payment?.source?.networkName}
                          </P>
                        </View>
                      </View>
                      <View style={[styles.items, { marginBottom: 0 }]}>
                        <SvgXml
                          xml={svg.userBrown}
                          style={{ marginRight: 8 }}
                        />
                        <View>
                          <P style={styles.holder}>
                            {country === "Turkey" ? "IBAN" : "Account number"}
                          </P>
                          <P
                            // @ts-ignore
                            style={[
                              styles.value,
                              { width: (45 * width) / 100 },
                            ]}
                          >
                            {accNum}
                          </P>
                        </View>
                      </View>
                    </View>
                  </>
                }
              />
              <View style={styles.buttonWrap}>
                <Button
                  btnText="Share details"
                  onPress={() => setLongSheet(true)}
                />
              </View>

              <View
                style={{
                  flexDirection: "row",
                  width: "100%",
                  alignItems: "center",
                  justifyContent: "center",
                  marginTop: 26,
                }}
              >
                <SvgXml xml={svg.download} style={{ marginRight: 4 }} />
                <Link style={{ fontSize: 12, lineHeight: 24 }}>
                  Download receipt
                </Link>
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.secBackground,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
    marginTop: (3.2 * height) / 100,
  },
  items: {
    width: "100%",
    flexDirection: "row",
    padding: 8,
    alignItems: "center",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    marginBottom: 4,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  copyBtn: {
    paddingTop: 4,
    paddingBottom: 4,
    padding: 13,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
  },
  contentCard: {
    width: "90%",
    alignSelf: "center",
    borderWidth: 1,
    borderColor: colors.stroke,
    borderRadius: 12,
    // marginTop: 24,
    marginTop: (2.7 * height) / 100,
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 16,
    paddingRight: 16,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  addMoney: {
    fontSize: 12,
    lineHeight: 19.2,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  amt: {
    fontSize: (4 * height) / 100,
    // fontSize: 32,
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  amtCur: {
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  timer: {
    paddingTop: 4,
    paddingBottom: 4,
    paddingLeft: 16,
    paddingRight: 16,
    borderRadius: 99,
    marginTop: 16,
  },
  statusText: {
    fontSize: 10,
    lineHeight: 16,
    fontFamily: fonts.poppinsRegular,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginTop: (2.7 * height) / 100,
    paddingBottom: (2.7 * height) / 100,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
  },
  section3Wrap: {
    width: "100%",
    marginTop: (2.7 * height) / 100,
    borderColor: colors.stroke,
    paddingLeft: 16,
  },
  barCont: {
    // backgroundColor: "red",
    alignItems: "center",
  },
  bar1: {
    height: 46,
    width: 2,
    borderRadius: 2,
    backgroundColor: colors.green,
    marginTop: 4,
    marginBottom: 4,
  },
  bar2: {
    height: 28,
    width: 2,
    borderRadius: 2,
    marginTop: 4,
    marginBottom: 4,
  },
  progressDesCont: {
    flexDirection: "row",
  },
  progTextHead: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  progTextBody: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: (5 * height) / 100,
    // marginTop: 42,
    // alignItems: 'center',
    // justifyContent: 'center',
  },
});
