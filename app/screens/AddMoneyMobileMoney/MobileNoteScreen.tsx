import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
  BackHandler,
} from "react-native";
import React, { useCallback } from "react";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import NoteComponent2 from "../../components/NoteComponent2";
import Button from "../../components/Button";
import { CommonActions, useFocusEffect } from "@react-navigation/native";
import { formatToTwoDecimals } from "../../Utils/numberFormat";
const { width, height } = Dimensions.get("window");
const MobileNoteScreen = ({ navigation, route }) => {
  const { symbol } = route?.params || "";
  const { transaction } = route?.params || {};
  const { accName } = route?.params || "";
  const { fee } = route?.params || {};
  

  const notes = [
    `Please approve the payment of ${symbol}${formatToTwoDecimals(
      transaction?.payment?.convertedAmount
    )} on your mobile money wallet, or ask the sender to approve it on their end`,
    "Once the payment is confirmed by our mobile money partner, your SFx account will be credited",
  ];
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        // Reset navigation stack to avoid loading issues
        // @ts-ignore
        // navigation.navigate("BottomTabNavigator");
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "BottomTabNavigator" }],
          })
        );
        return true;
      };
      // Disable iOS swipe back gesture
      navigation.setOptions({
        gestureEnabled: false,
      });
      // Handle Android back button
      BackHandler.addEventListener("hardwareBackPress", onBackPress);

      return () => {
        BackHandler.removeEventListener("hardwareBackPress", onBackPress);
      };
    }, [navigation])
  );
  return (
    <View style={styles.cont}>
      <Div>
        <ScrollView
          style={{ paddingHorizontal: 24 }}
          contentContainerStyle={{ paddingHorizontal: 24, paddingBottom: 50 }}
        >
          <View
            style={{ alignSelf: "center", alignItems: "center", marginTop: 32 }}
          >
            <SvgXml xml={svg.mobileNote} />
            <H4
              style={{ textAlign: "center", fontFamily: fonts.poppinsSemibold }}
            >
              Kindly approve mobile{"\n"}payment
            </H4>

            <View style={{ marginTop: 24, gap: 16 }}>
              {notes.map((item, index) => (
                <View
                  key={index}
                  style={{
                    width: "100%",
                    flexDirection: "row",
                    alignItems: "center",
                    gap: 8,
                  }}
                >
                  <SvgXml xml={svg.checkGreen} />
                  <P style={{ fontFamily: fonts.poppinsRegular }}>{item}</P>
                </View>
              ))}
            </View>
            <View style={{ marginTop: 24 }}>
              <NoteComponent2
                type="red"
                contStyle={{ backgroundColor: colors.redSubtle }}
                component={
                  <View>
                    <P style={{ fontSize: 10 }}>Reminder:</P>
                    <P
                      style={{ fontSize: 10, lineHeight: 14, fontFamily: fonts.poppinsRegular }}
                    >
                      This process takes no more than 10 minutes. Thank you for
                      your patience
                    </P>
                  </View>
                }
              />
            </View>
          </View>
          <View
            style={{
              marginTop: (6 * height) / 100,
              width: "100%",
              alignSelf: "center",
            }}
          >
            <Button
              btnText="I’ve approved the money"
              onPress={() =>
                navigation.navigate("MoneySentScreen2", {
                  transaction: transaction.transaction,
                })
              }
            />
          </View>
        </ScrollView>
      </Div>
    </View>
  );
};

const styles = StyleSheet.create({
  cont: {
    width: "100%",
    flex: 1,
    backgroundColor: colors.white,
  },
});

export default MobileNoteScreen;
