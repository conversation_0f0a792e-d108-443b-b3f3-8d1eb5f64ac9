import React, { useEffect, useState, useContext } from "react";
import {
  Dimensions,
  ScrollView,
  StyleSheet,
  View,
  Keyboard,
  TouchableOpacity,
  Platform,
} from "react-native";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import Div from "../components/Div";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import H4 from "../components/H4";
import P from "../components/P";
import Input from "../components/Input";
import Link from "../components/Link";
import Button from "../components/Button";
import GoogleBtn from "../components/GoogleBtn";
import { Formik } from "formik";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as yup from "yup";
import { CredentailsContext } from "../RequestHandlers/CredentailsContext";
import { Login } from "../RequestHandlers/Authentication";
import BottomComponent from "../components/BottomComponent";
import i18n from "../../i18n";
import { LanguageContext } from "../context/LanguageContext";
import AppleBtn from "../components/AppleBtn";
import { useToast } from "../context/ToastContext";
import { withApiErrorToast } from "../Utils/withApiErrorToast";

const baseHeight = 800;
const { width, height } = Dimensions.get("window");

export default function ({ navigation }) {
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [googlePromt, setGooglePromt] = useState(false);
  const [loading, setLoading] = useState(false);
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);
  const { handleToast } = useToast();
  // @ts-ignore
  const { language, changeLanguage } = useContext(LanguageContext);
  useEffect(() => {
    // Set the justLoggedIn flag when the user visits the login page
    AsyncStorage.setItem("justLoggedIn", "true")
      .then(() => console.log("Set justLoggedIn flag on login page"))
      .catch((err) => console.error("Error setting justLoggedIn flag:", err));

    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  const LoginSchema = yup.object().shape({
    email: yup
      .string()
      .email(i18n.t("login.iea"))
      .required(i18n.t("login.eir")),
    password: yup.string().required(i18n.t("login.pir")),
  });
  return (
    <View style={styles.container}>
      <Div>
        <Formik
          initialValues={{
            email: "",
            password: "",
          }}
          validationSchema={LoginSchema}
          onSubmit={async (values, actions) => {
            Keyboard.dismiss();
            setLoading(true);
            try {
              const login = await withApiErrorToast(Login(values), handleToast);
              if (login.token) {
                handleToast(i18n.t("login.ls"), "success");
                await AsyncStorage.setItem("hasLoggedInBefore", "true");
                navigation.navigate("VerifySessionScreen", {
                  email: values.email,
                  credentails: login,
                });
              } else {
                handleToast(login.message, "error");
              }
            } catch (error) {
              handleToast("Network error", "error");
            } finally {
              setLoading(false);
            }
          }}
        >
          {(formikProps) => (
            <ScrollView automaticallyAdjustKeyboardInsets={true}>
              <View style={styles.lgCont}>
                {/* <SvgXml xml={svg.sfxLogo} style={{ marginBottom: 20 }} /> */}
                <H4 style={{ fontFamily: fonts.poppinsSemibold }}>
                  {i18n.t("login.texth")}
                </H4>
                <P
                  style={{
                    fontSize: 14,
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  {i18n.t("login.textb")}
                </P>
              </View>
              <View style={styles.form}>
                <View style={styles.inputWrap}>
                  <Input
                    label={i18n.t("login.email")}
                    placeholder="<EMAIL>"
                    onChangeText={formikProps.handleChange("email")}
                    value={formikProps.values.email}
                    onBlur={formikProps.handleBlur("email")}
                    autoCapitalize="none"
                    error={
                      formikProps.errors.email && formikProps.touched.email
                    }
                  />
                  {formikProps.errors.email && formikProps.touched.email && (
                    <P style={styles.errorText}>{formikProps.errors.email}</P>
                  )}
                </View>
                <View style={{ marginBottom: 8 }}>
                  <Input
                    label={i18n.t("login.pwd")}
                    placeholder="*******"
                    secureTextEntry={true}
                    onChangeText={formikProps.handleChange("password")}
                    value={formikProps.values.password}
                    onBlur={formikProps.handleBlur("password")}
                    error={
                      formikProps.errors.password &&
                      formikProps.touched.password
                    }
                    type="password"
                  />
                  {formikProps.errors.password &&
                    formikProps.touched.password && (
                      <P style={styles.errorText}>
                        {formikProps.errors.password}
                      </P>
                    )}
                </View>
                <Link
                  style={{
                    textDecorationLine: "underline",
                    alignSelf: "flex-end",
                  }}
                  onPress={() => navigation.navigate("ForgotPasswordScreen")}
                >
                  {i18n.t("login.fp")}?
                </Link>
                <View style={styles.btnCont}>
                  <Button
                    btnText={i18n.t("login.login")}
                    onPress={formikProps.handleSubmit}
                    loading={loading}
                  />
                  <View style={styles.separator}>
                    <View style={styles.line}></View>
                    <P
                      style={{
                        fontSize: 12,
                        fontFamily: fonts.poppinsRegular,
                        color: colors.gray,
                      }}
                    >
                      {i18n.t("login.or")}
                    </P>
                    <View style={styles.line}></View>
                  </View>
                  <GoogleBtn onPress={() => {}} navigation={navigation} />
                  {Platform.OS === "ios" && (
                    <View style={{ marginTop: 16 }}>
                      <AppleBtn onPress={() => {}} navigation={navigation} />
                    </View>
                  )}
                </View>
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                    marginTop: (3 * height) / 100,
                  }}
                >
                  <P style={{ fontSize: 12, marginRight: 4 }}>
                    {i18n.t("dha")}
                  </P>
                  <Link
                    style={{ textDecorationLine: "underline", fontSize: 12 }}
                    onPress={() => navigation.navigate("SignupScreen")}
                  >
                    {i18n.t("su")}
                  </Link>
                </View>
              </View>
            </ScrollView>
          )}
        </Formik>
        <BottomComponent navigation={navigation} />
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  lgCont: {
    width,
    alignItems: "center",
    paddingHorizontal: 24,
    marginTop: (6 * height) / 100,
  },
  form: {
    width: (90 * width) / 100,
    minHeight: (50 * height) / 100,
    alignSelf: "center",
    marginTop: 24,
  },
  inputWrap: {
    marginBottom: 16,
  },
  btnCont: {
    width: "100%",
    // backgroundColor: "red",
    justifyContent: "space-between",
    marginTop: (5 * height) / 100,
  },
  bottomCont: {
    position: "absolute",
    bottom: (32 / baseHeight) * height,
    width: "90%",
    flexDirection: "row",
    backgroundColor: "white",
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "space-between",
  },
  bottomIcons: {
    width: 104,
    height: 22,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  bottomIconsText: {
    fontSize: 12,
    lineHeight: 22,
    color: "#A5A1A1",
    marginLeft: 4,
    textDecorationLine: "underline",
    textDecorationColor: "#A5A1A1",
  },
  line: {
    width: "45%",
    height: 1,
    backgroundColor: colors.stroke,
  },
  separator: {
    width: "100%",
    lineHeight: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 10,
    marginBottom: 10,
    // marginTop: (4.6 * height) / 100,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
});
