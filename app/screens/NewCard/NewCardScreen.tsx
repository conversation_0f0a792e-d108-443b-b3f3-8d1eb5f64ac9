import {
  Dimensions,
  Image,
  Modal,
  ScrollView,
  StyleSheet,
  View,
} from "react-native";
import { colors } from "../../config/colors";
import { useState } from "react";
import Div from "../../components/Div";
import H4 from "../../components/H4";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import GradientText from "../../components/GradientText";
import { useNavigation } from "@react-navigation/native";
const { width, height } = Dimensions.get("window");

export default function NewCardScreen({ navigation }) {
  const [hasCard, setHasCard] = useState(false);

  const renderNoCardSatet = () => {
    return (
      <View
        style={{
          width: "90%",
          alignSelf: "center",
          minHeight: (90 * height) / 100,
          justifyContent: "center",
        }}
      >
        <View
          style={{ alignItems: "center", width: "80%", alignSelf: "center" }}
        >
          <GradientText style={{ textAlign: "center" }}>
            Empower your finances
          </GradientText>
          <H4
            style={{
              fontSize: 24,
              fontFamily: fonts.poppinsSemibold,
              lineHeight: 28.8,
              marginTop: 8,
            }}
          >
            with the SFx Card
          </H4>
          <P style={{ fontFamily: fonts.poppinsRegular, textAlign: "center" }}>
            Your gateway to seamless transactions and unmatched convenience
          </P>
        </View>
        <Image
          source={require("../../assets/CardMock.webp")}
          style={{
            width: (80 * width) / 100,
            height: (55 * height) / 100,
            objectFit: "fill",
            borderRadius: 12,
            marginTop: (5 * height) / 100,
          }}
        />
        <Button
          btnText="Get virtual card"
          style={{ width: "90%", alignSelf: "center", marginTop: -20 }}
          onPress={() => {
            navigation.navigate("CardPurchaseInfoScreen");
          }}
        />
      </View>
    );
  };

  return (
    <View style={styles.body}>
      <Div>
        <ScrollView
          style={{ width: "100%" }}
          contentContainerStyle={{ paddingBottom: 120 }}
          showsVerticalScrollIndicator={false}
        >
          {!hasCard ? renderNoCardSatet() : <View></View>}
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
});
