import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
  Pressable,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import {SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { colors } from "../../config/colors";
import Button from "../../components/Button";
import Input from "../../components/Input";
import BottomSheet from "../../components/BottomSheet";
import LocalGovSelect from "../../components/LocalGovSelect";
import { GetUserDetails } from "../../RequestHandlers/User";
import { useToast } from "../../context/ToastContext";

const { width, height } = Dimensions.get("window");

export default function ProfileAccountTier({ navigation }) {
  const [choose, setChoose] = useState("silver");
  const [accIndex, setAccIndex] = useState(0);
  const dotArry = [1, 2];
  const [loading, setLoading] = useState(false);
  const {handleToast} = useToast()

  const getUserDetails = async () => {
    setLoading(true);
    try {
      const userDetails = await GetUserDetails();
      if (userDetails) {
        setLoading(false);
      }
      if (
        userDetails.verified === "false" ||
        userDetails.verified === "pending" ||
        userDetails.verified === "failed"
      ) {
        setChoose("bronze");
        setAccIndex(0);
      } else if (userDetails?.tier?.level === 1) {
        setChoose("silver");
        setAccIndex(1);
      } else {
        setChoose("gold");
        setAccIndex(1);
      }
    } catch (error) {
      ;
    }
  };

  useEffect(() => {
    getUserDetails();
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Account tier" navigation={navigation} />
        <ScrollView
          contentContainerStyle={{ flexGrow: 1, paddingBottom: "35%" }}
        >
          <View
            style={{
              width: "100%",
              alignItems: "center",
            }}
          >
            {choose === "bronze" && (
              <Pressable
                style={[
                  styles.card,
                  {
                    borderWidth: choose == "bronze" ? 1 : 0,
                    borderColor: "#8B52FF",
                  },
                ]}
                onPress={() => {}}
              >
                {choose == "bronze" && (
                  <View
                    style={{
                      backgroundColor: "#8C52FF",
                      width: 74,
                      height: 23,
                      borderRadius: 6,
                      alignItems: "center",
                      justifyContent: "center",
                      position: "absolute",
                      top: -10,
                      left: 16,
                    }}
                  >
                    <P
                      style={{
                        fontSize: 10,
                        color: "#fff",
                        fontFamily: fonts.poppinsSemibold,
                      }}
                    >
                      Current tier
                    </P>
                  </View>
                )}
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    marginBottom: 16,
                  }}
                >
                  <View>
                    <P style={styles.teir}>Tier 0</P>
                    <P style={styles.header}>Bronze</P>
                  </View>
                  <View style={styles.shield}>
                    <Image
                      source={require("../../assets/pBadge3.png")}
                      style={{ width: 43, height: 43 }}
                    />
                  </View>
                </View>
                <View style={styles.line}>
                  <P style={styles.left}>Daily add or send money</P>
                  <P style={styles.right}>$0.00</P>
                </View>
                <View style={styles.line}>
                  <P style={styles.left}>Daily card top-up</P>
                  <P style={styles.right}>$0.00</P>
                </View>
                <View style={styles.line}>
                  <P style={styles.left}>Crypto deposit</P>
                  <P style={styles.right}>$0.00</P>
                </View>
              </Pressable>
            )}
            <Pressable
              style={[
                styles.card,
                {
                  borderWidth: choose == "silver" ? 1 : 0,
                  borderColor: "#8B52FF",
                },
              ]}
              onPress={() => {}}
            >
              {choose == "silver" && (
                <View
                  style={{
                    backgroundColor: "#8C52FF",
                    width: 74,
                    height: 23,
                    borderRadius: 6,
                    alignItems: "center",
                    justifyContent: "center",
                    position: "absolute",
                    top: -10,
                    left: 16,
                  }}
                >
                  <P
                    style={{
                      fontSize: 10,
                      color: "#fff",
                      fontFamily: fonts.poppinsSemibold,
                    }}
                  >
                    Current tier
                  </P>
                </View>
              )}
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  marginBottom: 16,
                }}
              >
                <View>
                  <P style={styles.teir}>Tier 1</P>
                  <P style={styles.header}>Silver</P>
                </View>
                <View style={styles.shield}>
                  <Image
                    source={require("../../assets/pBadge1.png")}
                    style={{ width: 43, height: 43 }}
                  />
                </View>
              </View>
              <View style={styles.line}>
                <P style={styles.left}>Daily add or send money</P>
                <P style={styles.right}>$1 - $3,000</P>
              </View>
              <View style={styles.line}>
                <P style={styles.left}>Daily card top-up</P>
                <P style={styles.right}>$10,000</P>
              </View>
              <View style={styles.line}>
                <P style={styles.left}>Crypto deposit</P>
                <P style={styles.right}>Unlimited</P>
              </View>
            </Pressable>
            {choose !== "bronze" && (
              <Pressable
                style={[
                  styles.card,
                  {
                    borderWidth: choose == "gold" ? 1 : 0,
                    borderColor: "#8B52FF",
                  },
                ]}
                onPress={() => {}}
              >
                {choose == "gold" && (
                  <View
                    style={{
                      backgroundColor: "#8C52FF",
                      width: 74,
                      height: 23,
                      borderRadius: 6,
                      alignItems: "center",
                      justifyContent: "center",
                      position: "absolute",
                      top: -10,
                      left: 16,
                    }}
                  >
                    <P
                      style={{
                        fontSize: 10,
                        color: "#fff",
                        fontFamily: fonts.poppinsSemibold,
                      }}
                    >
                      Current tier
                    </P>
                  </View>
                )}
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    marginBottom: 16,
                  }}
                >
                  <View>
                    <P style={styles.teir}>Tier 2</P>
                    <P style={styles.header}>Gold</P>
                  </View>
                  <View style={styles.shield}>
                    <Image
                      source={require("../../assets/pBadge2.png")}
                      style={{ width: 43, height: 43 }}
                    />
                  </View>
                </View>
                <View style={styles.line}>
                  <P style={styles.left}>Daily add or send money</P>
                  <P style={styles.right}>Unlimited</P>
                </View>
                <View style={styles.line}>
                  <P style={styles.left}>Daily card top-up</P>
                  <P style={styles.right}>$10,000</P>
                </View>
                <View style={styles.line}>
                  <P style={styles.left}>Crypto deposit</P>
                  <P style={styles.right}>Unlimited</P>
                </View>
              </Pressable>
            )}

            <View style={[styles.dotsContainer]}>
              {dotArry.map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.dot,
                    { opacity: index === accIndex ? 1 : 0.3 },
                  ]}
                ></View>
              ))}
            </View>
            {choose !== "gold" && (
              <View style={{ width: "80%", marginTop: 32 }}>
                <Button
                  btnText={
                    choose === "bronze"
                      ? "Upgrade to tier 1"
                      : "Upgrade to tier 2"
                  }
                  onPress={() => {
                    if (choose === "bronze") {
                      navigation.navigate("AccountVerificationPromt");
                    } else {
                      handleToast("Tier 2 is coming soon", "success")
                      // navigation.navigate("ProfileUpgradeAccount");
                    }
                  }}
                />
              </View>
            )}
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  shield: {
    width: 43,
    height: 43,
    backgroundColor: "#F7F4FF",
    borderRadius: 100,
    justifyContent: "center",
    alignItems: "center",
  },
  card: {
    width: "90%",
    height: 177,
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 24,
    marginTop: 10,
    marginBottom: 16,
  },
  header: { fontSize: 14, fontFamily: fonts.poppinsMedium },
  line: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  left: { fontSize: 12, color: colors.gray, fontFamily: fonts.poppinsRegular },
  right: { fontSize: 12, color: "#161817", fontFamily: fonts.poppinsMedium },
  teir: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  dotsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 14,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 5,
    backgroundColor: colors.primary,
    marginRight: 8,
  },
});
