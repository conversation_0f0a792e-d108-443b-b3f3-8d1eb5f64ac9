import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { colors } from "../../config/colors";
import Button from "../../components/Button";
import Input from "../../components/Input";
import BottomSheet from "../../components/BottomSheet";
import LocalGovSelect from "../../components/LocalGovSelect";
import * as ImagePicker from "expo-image-picker";
import {
  GetUserDetails,
  UpdateprofilePic,
  UpdateUser,
} from "../../RequestHandlers/User";
import Loader from "../../components/ActivityIndicator";
import PCard from "../../components/PCard";
import { countries } from "../../components/counties";
import { AfricanCountriesList } from "../../components/AfricanCountriesList";
import CountryCodSelect from "../../components/CountryCodSelect";
import CountrySelect from "../../components/CountrySelect";
import { Formik } from "formik";
import * as yup from "yup";
import { ensureHttps } from "../../components/AddHttp";
import { useToast } from "../../context/ToastContext";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import NonVerifiedAppHeader from "../../components/NonVerifiedAppHeader";
import { invalidateProfilePictureCache } from "../../Utils/userDetailsCacheUtils";

const baseWidth = 360;
const { width, height } = Dimensions.get("window");

export default function ProfileIndex({ navigation }) {
  const [isEnabled, setIsEnabled] = useState(false);
  const [bene, setBene] = useState(false);
  const { handleToast } = useToast();
  const [activeAvatar, setActiveAvatar] = useState(null);
  const [flag, setFlag] = useState(require("../../assets/nigeria.png"));
  const [codeFlag, setCodeFlag] = useState(require("../../assets/nigeria.png"));
  const [country, setCountry] = useState("");
  const [showCountries, setShowCountries] = useState(false);
  const [isAccVerified, setIsAccVerified] = useState("true");
  const [accTire, setAccTier] = useState("Tier 1");
  const [countryCode, setCountryCode] = useState("");
  const [cCode, setCCode] = useState("");
  const [loader, setLoader] = useState(false);
  const [accVerification, setAccVerification] = useState("");
  const [uDetails, setUDetails] = useState<any>([]);
  const [show, setShow] = useState(false);
  const [show2, setShow2] = useState(false);
  const [phone, setPhone] = useState(null);
  const [residentAddress, setResidentAdrees] = useState(null);
  const [username, setUserName] = useState(null);
  const [middleName, setMiddleName] = useState("");
  const toggleSwitch = () => setIsEnabled((previousState) => !previousState);
  const [error, setError] = useState(false);
  const [addError, setAddError] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errormessage, setErrorMessage] = useState("Message");
  const [profilePic, setProfilePic] = useState("");
  const [isKycDone, setIsKycDone] = useState("false");
  const updateScheme = yup.object().shape({
    phoneNumber: yup
      .string()
      .required("Phone number is required")
      .min(7, "Invalid mobile number")
      .matches(/^[0-9]+$/, "Phone number should not include letters"),
    residentAddress: yup.string().required("Residential address is required"),
  });
  const checkBeneficiary = () => {
    setBene(true);
  };
  const handleActiveCountry = (newActiveType: string | null) => {
    setCountry(newActiveType);
  };
  const handleActiveFlag = (newActiveType: any | null) => {
    if (newActiveType) {
      setFlag(newActiveType);
    }
  };
  const ChangePic = async (img) => {
    setLoader(true);
    try {
      const uploadPic = await UpdateprofilePic({
        picture: img,
      });
      if (uploadPic.message.includes("Successfully")) {
        handleToast(uploadPic.message, "success");
        // Invalidate profile picture cache so other screens refresh
        await invalidateProfilePictureCache();
      } else {
        handleToast(uploadPic.message, "error");
      }
    } catch (error) {
    } finally {
      setLoader(false);
    }
  };
  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.5,
        base64: true,
      });

      if (!result.canceled && result.assets?.length > 0) {
        const selectedImage = result.assets[0];

        // Check file size (5MB = 5 * 1024 * 1024 bytes)
        const maxSizeInBytes = 5 * 1024 * 1024; // 5MB

        if (selectedImage.fileSize && selectedImage.fileSize > maxSizeInBytes) {
          handleToast(
            "Image size must be less than 5MB. Please select a smaller image.",
            "error"
          );
          return;
        }

        // If file size is acceptable, proceed with upload
        setActiveAvatar({
          uri: "data:image/jpeg;base64," + selectedImage.base64,
        });
        ChangePic("data:image/jpeg;base64," + selectedImage.base64);
      }
    } catch (error) {
      console.error("Error picking image:", error);
      handleToast("Failed to select image. Please try again.", "error");
    }
  };

  const userDetails = async () => {
    try {
      const userDetails = await withApiErrorToast(
        GetUserDetails(),
        handleToast
      );
      if (userDetails?.email) {
        setLoader(false);
        setIsAccVerified(userDetails.verified);
        setActiveAvatar({ uri: ensureHttps(userDetails.picture) });
        setUDetails(userDetails);
        setCountry(userDetails?.homeCountry);
        setUserName(userDetails?.username);
        setResidentAdrees(userDetails?.residentAddress);
        setUserName(userDetails?.username);
        setMiddleName(userDetails?.middleName || "");
        // Find the corresponding flag based on the country
        const matchedAfricanCountry = AfricanCountriesList.find(
          (item) => item.name === userDetails?.homeCountry
        );

        if (matchedAfricanCountry) {
          // Use flagcdn.com URL format
          const flagUrl = `https://flagcdn.com/w2560/${matchedAfricanCountry.code.toLowerCase()}.png`;
          setFlag({ uri: flagUrl });
          console.log(matchedAfricanCountry.code);
          setCCode(matchedAfricanCountry.code);
          setCountryCode(matchedAfricanCountry.code);
        } else {
          console.log("Country not found in any list.");
        }
        // // const matchedCountry = countries.find(
        // //   (item) => item.country === userDetails?.homeCountry
        // // );

        // // if (matchedCountry) {
        // //   setFlag(matchedCountry.flag);
        // // } else {
        //   // Check in AfricanCountriesList if not found in main countries list
        // }

        const phoneNumber = userDetails?.phoneNumber;
        if (phoneNumber) {
          // Loop through countries to find the matching country code in the phone number
          const matchedCountryByCode = AfricanCountriesList.find((item) => {
            const countryCodeWithPlus = item.tel_code.startsWith("+")
              ? item.tel_code
              : `+${item.tel_code}`;
            return phoneNumber?.startsWith(countryCodeWithPlus);
          });
          if (matchedCountryByCode) {
            // Generate flag URL using country code
            const flagUrl = `https://flagcdn.com/w2560/${matchedCountryByCode.code.toLowerCase()}.png`;
            setCodeFlag({ uri: flagUrl });
            setCountryCode(matchedCountryByCode.tel_code); // Set the country code based on the phone number
            const phoneWithoutCode = phoneNumber?.replace(
              matchedCountryByCode?.tel_code?.startsWith("+")
                ? matchedCountryByCode.tel_code
                : `+${matchedCountryByCode.tel_code}`,
              ""
            );
            setPhone(phoneWithoutCode);
          } else {
            console.log("Country code not found in the phone number.");
          }
        }
      } else {
        setLoader(false);
        handleToast(userDetails.message, "error");
      }
    } catch (error) {
      setLoader(false);
      handleToast("Network error", "error");
    } finally {
      setLoader(false);
    }
  };

  const updateUser = async () => {
    let errorHandling = 0;
    if (phone.length < 7) {
      setError(true);
      errorHandling = 1;
    } else {
      setError(false);
    }

    if (residentAddress.length < 3) {
      setAddError(true);
      errorHandling = 1;
    } else {
      setAddError(false);
    }

    if (errorHandling === 0) {
      setLoading(true);
      try {
        // Build the body object conditionally
        const baseBody: any = {
          phoneNumber: `${countryCode}${phone?.trim().replace(/^0/, "")}`,
          homeCountry: country,
          residentAddress: residentAddress?.trim(),
        };

        // Add username if it's different from current username
        if (username !== uDetails.username) {
          baseBody.username = username.trim();
        }

        // Only include middleName if it exists and is not empty
        if (middleName && middleName.trim() !== "") {
          baseBody.middleName = middleName.trim();
        }

        const body = baseBody;
        const updateUser = await withApiErrorToast(
          UpdateUser(body),
          handleToast
        );
        if (updateUser.message === "User Updated Successfully!") {
          setLoading(false);
          handleToast(updateUser.message, "success");
          userDetails();
        } else {
          setLoading(false);
          handleToast(updateUser.message, "error");
        }
      } catch (error) {
      } finally {
        setLoading(false);
      }
    } else {
    }
  };

  useEffect(() => {
    setLoader(true);
    userDetails();
  }, []);

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Profile" navigation={navigation} />
        <ScrollView
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 100 }}
          showsVerticalScrollIndicator={false}
        >
          {isAccVerified === "true" ? (
            <></>
          ) : (
            // <TouchableOpacity
            // style={styles.verificationPromt}
            //   onPress={() => {
            //     if (isAccVerified === "false") {
            //       navigation.navigate("AccountVerificationPromt");
            //     } else if (isAccVerified === "pending") {
            //       navigation.navigate("AccountVerificationPending");
            //     } else if (isAccVerified === "failed") {
            //       navigation.navigate("AccountVerification4t");
            //       // navigation.navigate("AccountVerifcationPending");
            //     } else {
            //     }
            //   }}
            // >
            //   <PCard accStatus={isAccVerified} navigation={navigation} />
            // </TouchableOpacity>
            <View style={{ marginBottom: 24 }}>
              <NonVerifiedAppHeader
                Imageurl={profilePic}
                isKycDone={isKycDone}
                navigation={navigation}
                message={errormessage}
              />
            </View>
          )}
          <View
            style={{
              width: "100%",
              // height: 96,
              alignItems: "center",
            }}
          >
            <TouchableOpacity onPress={pickImage}>
              <Image
                source={
                  activeAvatar === null
                    ? require("../../assets/defualtAvatar.png")
                    : activeAvatar
                }
                style={{ width: 48, height: 48, borderRadius: 100 }}
              />
              <SvgXml
                xml={svg.imgEdit}
                style={{ position: "absolute", bottom: 0, right: 0 }}
              />
            </TouchableOpacity>
            <P
              style={{ marginTop: 8, fontSize: 12 }}
              // @ts-ignore
            >{`${
              // @ts-ignore
              uDetails.firstName === undefined
                ? "..."
                : // @ts-ignore
                  `${uDetails.firstName}${
                    // @ts-ignore
                    uDetails.middleName ? ` ${uDetails?.middleName}` : ""
                  } ${
                    // @ts-ignore
                    uDetails.lastName ? uDetails.lastName : ""
                  }`
            }`}</P>
            {isAccVerified === "true" && (
              <P
                style={{
                  color: "#A5A1A1",
                  fontSize: 12,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                {uDetails?.username} | {uDetails?.phoneNumber}
              </P>
            )}
          </View>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <Input
                label="First name"
                placeholder="Doe"
                inputStyle={styles.input}
                contStyle={{ marginBottom: 16 }}
                customInputStyle={{ backgroundColor: colors.secBackground }}
                onBlur={() => checkBeneficiary()}
                editable={false}
                // @ts-ignore
                value={uDetails?.firstName ? uDetails.firstName : "..."}
              />
              <Input
                label="Middle name"
                placeholder="Michael"
                inputStyle={styles.input}
                contStyle={{ marginBottom: 16 }}
                customInputStyle={{
                  backgroundColor: uDetails?.middleName
                    ? colors.secBackground
                    : colors.white,
                }}
                onBlur={() => checkBeneficiary()}
                editable={!uDetails?.middleName} // Disabled if user already has middle name
                onChangeText={(e) => setMiddleName(e)}
                value={middleName || ""}
              />
              {/* {!uDetails?.middleName && (
                <P style={styles.infoText}>
                  You can add your middle name if you have one
                </P>
              )} */}
              <Input
                label="Last name"
                placeholder="John"
                inputStyle={styles.input}
                contStyle={{ marginBottom: 16 }}
                onBlur={() => checkBeneficiary()}
                editable={false}
                customInputStyle={{ backgroundColor: colors.secBackground }}
                // @ts-ignore
                value={uDetails?.lastName ? uDetails.lastName : "..."}
              />
              <TouchableOpacity
                onPress={() => {
                  setShowCountries(true);
                }}
                disabled={country !== "" || country !== null}
              >
                <Input
                  value={country}
                  label="Home country"
                  // placeholder="Dikmen"
                  inputStyle={{
                    width: "65%",
                    color: "#161817",
                    backgroundColor: colors.secBackground,
                  }}
                  contStyle={{ marginBottom: 16 }}
                  editable={false}
                  leftIcon={
                    <View
                      style={{
                        backgroundColor: colors.secBackground,
                        height: "100%",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <Image
                        source={flag}
                        style={{
                          width: 24,
                          height: 24,
                          marginLeft: 14,
                          objectFit: cCode === "NG" ? "fill" : "cover",
                          borderRadius: 100,
                        }}
                      />
                    </View>
                  }
                  rightIcon={
                    <View
                      style={{
                        backgroundColor: colors.secBackground,
                        width: "15%",
                        height: "100%",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <SvgXml xml={svg.dropDown} />
                    </View>
                  }
                />
              </TouchableOpacity>
              <Input
                label="Phone Number"
                placeholder="8144855058"
                inputStyle={{ width: "80%" }}
                contStyle={{ marginBottom: 4 }}
                value={phone}
                editable={false}
                customInputStyle={{ backgroundColor: colors.secBackground }}
                keyboardType={"numeric"}
                error={false}
                leftIcon={
                  <View
                    style={[
                      styles.pinInput,
                      { backgroundColor: colors.secBackground },
                    ]}
                  >
                    <Image
                      source={codeFlag}
                      style={{
                        width: 24,
                        height: 24,
                        marginLeft: (14 / baseWidth) * width,
                        borderRadius: 100,
                        objectFit: countryCode === "+234" ? "fill" : "cover",
                      }}
                    />
                    {/* @ts-ignore */}
                    <P style={[styles.pinTextInput, { fontSize: 12 }]}>
                      {countryCode}
                    </P>
                  </View>
                }
              />
              <Input
                label="Resident address"
                placeholder="Resident address"
                inputStyle={{ width: "85%" }}
                contStyle={{ marginBottom: 4, marginTop: 16 }}
                onBlur={() => checkBeneficiary()}
                onChangeText={(e) => setResidentAdrees(e)}
                value={
                  // @ts-ignore
                  residentAddress
                }
              />
              {addError && (
                <P style={styles.errorText}>
                  Resident address should not contain less than 3 characters
                </P>
              )}
            </View>
            {isAccVerified === "true" ? (
              <>
                <View style={[styles.detailWrap, { marginTop: 12 }]}>
                  <Input
                    label="Username"
                    placeholder="Username"
                    inputStyle={{ width: "85%" }}
                    contStyle={{ marginBottom: 16 }}
                    onBlur={() => checkBeneficiary()}
                    value={username}
                    editable={false}
                    onChangeText={(e) => setUserName(e)}
                    customInputStyle={{ backgroundColor: colors.secBackground }}
                  />
                </View>
                <View style={[styles.detailWrap, { marginTop: 12 }]}>
                  <TouchableOpacity
                    style={{ width: "100%" }}
                    onPress={() => navigation.navigate("ProfileAccountTier")}
                  >
                    <Input
                      label="Account tier"
                      placeholder="Account tier"
                      inputStyle={styles.input}
                      contStyle={{ marginBottom: 16 }}
                      onBlur={() => checkBeneficiary()}
                      rightIcon={<SvgXml xml={svg.arrowBlack} />}
                      editable={false}
                      value={accTire}
                    />
                  </TouchableOpacity>
                </View>
              </>
            ) : (
              <></>
            )}

            <View style={{ width: "80%", marginTop: 32 }}>
              <Button
                btnText="Update"
                loading={loading}
                onPress={() => {
                  updateUser();
                }}
              />
            </View>
          </View>
        </ScrollView>

        <BottomSheet
          isVisible={showCountries}
          showBackArrow={false}
          backspaceText="Country"
          onClose={() => {
            setShowCountries(false);
            userDetails();
          }}
          modalContentStyle={{ height: "65%" }}
          extraModalStyle={{ height: "63%" }}
          components={
            <CountrySelect
              offHeader={true}
              onActiveCountryChange={handleActiveCountry}
              onActiveFlag={handleActiveFlag}
              onPress={() => {
                setShowCountries(false);
              }}
            />
          }
        />
        <BottomSheet
          isVisible={show}
          backspaceText={"Phone number"}
          onClose={() => setShow(false)}
          showBackArrow={false}
          components={
            <CountryCodSelect
              onPress={(h) => {
                setShow(false);
                setCountryCode(countries[h].countryCode);
                setCodeFlag(countries[h].flag);
              }}
            />
          }
          modalContentStyle={{ height: "55%" }}
          extraModalStyle={{ height: "53%" }}
        />
      </Div>
      <Loader visible={loader} loading={true} />
    </View>
  );
}

const styles = StyleSheet.create({
  pinInput: {
    width: "35%",
    height: "100%",
    alignItems: "center",
    flexDirection: "row",
    borderRightColor: "#E6E5E5",
    borderRightWidth: 1,
  },
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    // height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 16,
    paddingBottom: 24,
    // justifyContent:"center",
    alignItems: "center",
  },
  benefeciary: {
    paddingTop: 4,
    paddingRight: 24,
    paddingBottom: 4,
    paddingLeft: 24,
    backgroundColor: "#F7F4FF",
    borderRadius: 8,
    flexDirection: "row",
    width: "100%",
    height: 44,
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  deatilsHead: {
    width: "100%",
    height: 42,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: "5%",
  },
  detailWrap: {
    padding: 24,
    width: "90%",
    alignSelf: "center",
    // height:200,
    backgroundColor: "white",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  detailWrap2: {
    // padding: 24,
    width: "90%",
    alignSelf: "center",
    height: 246,
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent: "center",
    alignItems: "center",
    marginTop: 60,
  },

  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  pinTextInput: {
    fontSize: 14,
    textAlign: "center",
    color: "#161817",
    fontFamily: fonts.poppinsMedium,
    marginLeft: 5,
    marginRight: 8,
  },
  verificationPromt: {
    width: "90%",
    // backgroundColor: colors.lowOpSuccess,
    alignSelf: "center",
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 24,
  },
  input: {
    width: "85%",
    color: colors.black,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
    textAlign: "left",
    width: "100%",
  },
  infoText: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
    textAlign: "left",
    width: "100%",
  },
});
