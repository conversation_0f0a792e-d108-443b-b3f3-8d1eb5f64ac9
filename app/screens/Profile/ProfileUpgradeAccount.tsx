import React, { useRef, useState, useEffect } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  Dimensions,
  Text,
  TouchableOpacity,
  Image,
} from "react-native";
import Div from "../../components/Div";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Link from "../../components/Link";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import Input from "../../components/Input";
import BottomComponent from "../../components/BottomComponent";
import SignupHeader from "../../components/SignupHeader";
import BottomSheet from "../../components/BottomSheet";
import * as ImagePicker from "expo-image-picker";
import { Identification } from "../../components/Identification";
import IdentificationSelect from "../../components/IdentificationSelect";
import FaceIdScreen from "../FaceIdScreen";
import BottomSheet2 from "../../components/BottomSheet2";
import { CameraView } from "expo-camera";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import NoteComponent2 from "../../components/NoteComponent2";

const baseHeight = 800;
const baseWidth = 360;
const { height, width } = Dimensions.get("window");
const screenHeight = Dimensions.get("window").height;
export default function ProfileUpgradeAccount({ navigation }) {
  const [countryCode2, setCountryCode2] = useState("");
  const [image, setimage] = useState("");
  const [show, setShow] = useState(false);
  const [show2, setShow2] = useState(false);
  const [show3, setShow3] = useState(false);
  const [show4, setShow4] = useState(false);
  const [snappedPicture, setSnappedPicture] = useState("");
  const cameraRef = useRef(null);

  const selectProfileImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 1,
    });

    if (!result.canceled) {
      setimage(result.assets[0].fileName);
      setShow3(false);
    }
  };

  const takePicture = async () => {
    if (cameraRef.current) {
      const options = { quality: 0.5, base64: true };
      const data = await cameraRef.current.takePictureAsync(options);
      setSnappedPicture(data.uri);
      setimage(data.uri.split("Camera")[1].replace("/", ""));
    }
  };

  return (
    <View style={styles.body}>
      <Div style={{ alignItems: "center" }}>
        <AuthenticationHedear text="Upgrade account" navigation={navigation} />
        <View style={styles.container}>
          <View
            style={{
              width: "100%",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <P style={styles.text1}>Identity verification</P>
            <P style={styles.text2}>
              Provide a means of identification {"\n"}to create account
            </P>
          </View>
          <View
            style={{
              width: "100%",
              borderBottomWidth: 1,
              borderColor: colors.stroke,
              borderStyle: "dashed",
              marginTop: 24,
            }}
          ></View>
          <View style={styles.components}>
            <TouchableOpacity
              style={{ flexDirection: "row", justifyContent: "space-between" }}
              onPress={() => setShow(true)}
            >
              <P
                style={{
                  fontFamily: fonts.poppinsRegular,
                  fontSize: 12,

                  marginLeft: 8,
                }}
              >
                Proof of residence
              </P>
              {/* <SvgXml xml={svg.help} /> */}
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => setShow3(true)}
              style={{
                flexDirection: "row",
                width: "100%",
                backgroundColor: "#F1EBFF",
                height: 44,
                borderColor: "#A5A1A1",
                borderWidth: 1,
                borderStyle: "dashed",
                marginBottom: 16,
                marginTop: 8,
                justifyContent: "center",
                alignItems: "center",
                borderRadius: 8,
              }}
            >
              {image == "" ? (
                <SvgXml xml={svg.upup} />
              ) : (
                <SvgXml xml={svg.image} />
              )}
              <P
                style={{
                  fontFamily: fonts.poppinsRegular,
                  fontSize: 14,
                  textDecorationLine: "underline",
                  textDecorationColor: "#161817",
                  color: "#161817",
                  marginLeft: 8,
                }}
              >
                {image == "" ? "Click to upload" : image}
              </P>
            </TouchableOpacity>
            <View>
              <NoteComponent2
                text={
                  "A residency permit card in the following countries is supported: Turkiye, and TRNC."
                }
              />
            </View>
          </View>
        </View>
        <View style={{ width: "75%" }}>
          <P
            style={{
              textAlign: "center",
              fontSize: 12,
              color: colors.gray,
              fontFamily: fonts.poppinsRegular,
              marginTop: 16,
            }}
          >
            This process ensures that your account remains safe and secure.
          </P>
        </View>
        <View></View>
        <View style={{ width: "80%", marginTop: 32 }}>
          <Button
            btnText="Continue"
            onPress={() => navigation.navigate("ProfileAccountTier")}
          />
        </View>
        <BottomSheet
          isVisible={show4}
          backspaceText={"Identification method"}
          onClose={() => setShow4(false)}
          showBackArrow={false}
          // statusBarTranslucent={false}
          components={
            snappedPicture === "" ? (
              <View style={styles.pictureView}>
                <CameraView
                  style={styles.camera}
                  facing={"front"}
                  mode="picture"
                  ref={cameraRef}
                ></CameraView>
                <TouchableOpacity style={styles.outerBtn} onPress={takePicture}>
                  <View style={styles.innerBtn}></View>
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.pictureView}>
                <Image src={snappedPicture} style={styles.camera} />
                <TouchableOpacity onPress={() => setSnappedPicture("")}>
                  <P style={{ color: "#8B52FF" }}>Retake Picture</P>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    setShow4(false);
                    setShow3(false);
                  }}
                  style={styles.complete}
                >
                  <P style={{ color: "#fff" }}>Complete</P>
                </TouchableOpacity>
              </View>
            )
          }
          modalContentStyle={{ height: "100%" }}
          extraModalStyle={{ height: "53%" }}
        />
        <BottomSheet
          isVisible={show2}
          backspaceText={"Identification method"}
          onClose={() => setShow2(false)}
          showBackArrow={false}
          components={
            <>
              <P
                style={{
                  marginTop: 24,
                  fontSize: 12,
                  color: colors.gray,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Select your identification method
              </P>
              <IdentificationSelect
                onPress={(h) => {
                  setShow2(false);
                  setCountryCode2(Identification[h]);
                }}
              />
            </>
          }
          modalContentStyle={{ height: "50%" }}
          extraModalStyle={{ height: "48%" }}
        />
        <BottomSheet
          isVisible={show3}
          backspaceText={"Upload document"}
          onClose={() => setShow3(false)}
          showBackArrow={false}
          components={
            <View>
              <P style={{ color: "#A5A1A1", marginTop: 24 }}>
                Select your upload method
              </P>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-around",
                }}
              >
                <TouchableOpacity
                  style={styles.card}
                  onPress={() => setShow4(true)}
                >
                  <SvgXml xml={svg.camera} />
                  <P>Camera</P>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => selectProfileImage()}
                  style={styles.card}
                >
                  <SvgXml xml={svg.folder} />
                  <P>folder</P>
                </TouchableOpacity>
              </View>
            </View>
          }
          modalContentStyle={{ height: "30%" }}
          extraModalStyle={{ height: "27%" }}
        />
        <BottomSheet
          isVisible={show}
          backspaceText={"Guidance"}
          onClose={() => setShow(false)}
          showBackArrow={false}
          components={
            <ScrollView>
              <P
                style={{
                  marginTop: (24 / baseHeight) * height,
                  fontFamily: fonts.poppinsMedium,
                }}
              >
                What is identity verification?
              </P>
              <P
                style={{
                  color: colors.gray,
                  fontFamily: fonts.poppinsRegular,
                  marginTop: (16 / baseHeight) * height,
                }}
              >
                It's a simple process that helps us confirm you are who you say
                you are. This helps prevent fraud and keeps your hard-earned
                money safe.
              </P>
              <P
                style={{
                  marginTop: (24 / baseHeight) * height,
                  fontFamily: fonts.poppinsMedium,
                }}
              >
                Here's what you'll need:
              </P>
              <P
                style={{
                  color: colors.gray,
                  fontFamily: fonts.poppinsRegular,
                  marginTop: (16 / baseHeight) * height,
                }}
              >
                A valid government-issued ID (passport, driver's license,
                national ID)
              </P>
              <P
                style={{
                  marginTop: (24 / baseHeight) * height,
                  fontFamily: fonts.poppinsMedium,
                }}
              >
                The process is quick and easy:
              </P>
              <View
                style={{
                  marginTop: (16 / baseHeight) * height,
                  flexDirection: "row",
                }}
              >
                <P style={{ color: colors.gray, fontSize: 16 }}>{"\u2022 "}</P>
                <P
                  style={{
                    color: colors.gray,
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  Snap a picture: Take a clear photo of your government ID
                </P>
              </View>
              <View
                style={{
                  marginTop: (6 / baseHeight) * height,
                  flexDirection: "row",
                }}
              >
                <P style={{ color: colors.gray, fontSize: 16 }}>{"\u2022 "}</P>
                <P
                  style={{
                    color: colors.gray,
                    fontFamily: fonts.poppinsRegular,
                    marginRight: (15 / baseWidth) * width,
                  }}
                >
                  Selfie time: Capture a quick selfie to match your ID photo.
                </P>
              </View>
              <P
                style={{
                  marginTop: (24 / baseHeight) * height,
                  fontFamily: fonts.poppinsMedium,
                }}
              >
                The process is quick and easy:
              </P>
              <P
                style={{
                  color: colors.gray,
                  fontFamily: fonts.poppinsRegular,
                  marginTop: (16 / baseHeight) * height,
                }}
              >
                our information is securely stored and used only for
                verification purposes
              </P>
            </ScrollView>
          }
          modalContentStyle={{ height: "70%" }}
          extraModalStyle={{ height: "68%" }}
        />
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  container: {
    // flex: 1,
    backgroundColor: colors.white,
    width: "90%",
    padding: 24,
    borderRadius: 12,
    alignItems: "center",
  },

  text1: {
    fontSize: 16,
    fontFamily: fonts.poppinsMedium,
    marginTop: 20,
    lineHeight: 30,
  },
  text2: {
    fontSize: 12,
    textAlign: "center",
    // lineHeight: 6.4,
    color: "#A5A1A1",
    fontFamily: fonts.poppinsRegular,
  },
  components: {
    width: "100%",
    marginTop: 24,
    alignSelf: "center",
    // marginBottom: 68,
    // backgroundColor:"red"
  },
  pinInput: {
    width: "35%",
    height: "100%",
    alignItems: "center",
    flexDirection: "row",
    borderRightColor: "#E6E5E5",
    borderRightWidth: 1,
  },
  pinTextInput: {
    fontSize: 14,
    textAlign: "center",
    color: "#161817",
    fontFamily: fonts.poppinsMedium,
    marginLeft: 16,
    marginRight: 8,
  },
  card: {
    width: 148 + 16,
    height: 85 + 16,
    borderColor: "#E6E5E5",
    borderWidth: 2,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 8,
  },
  camera: {
    width: "100%",
    height: "70%",
    // borderRadius: 100,
    // overflow:'hidden'
  },
  complete: {
    marginTop: "5%",
    width: "80%",
    height: 50,
    backgroundColor: "#8B52FF",
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
  },
  pictureView: { width: "100%", height: "100%", alignItems: "center" },
  outerBtn: {
    width: 70,
    height: 70,
    borderRadius: 1000,
    backgroundColor: "#8B52FF55",
    marginTop: "5%",
    alignItems: "center",
    justifyContent: "center",
  },
  innerBtn: {
    width: 50,
    height: 50,
    borderRadius: 1000,
    backgroundColor: "#8B52FF",
  },
});
