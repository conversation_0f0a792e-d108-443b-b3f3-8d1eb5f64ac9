import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  TouchableOpacity,
  Text,
  ScrollView,
} from "react-native";
import P from "../../components/P";
import Link from "../../components/Link";
import Button from "../../components/Button";
import { fonts } from "../../config/Fonts";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import BottomSheet from "../../components/BottomSheet";

const groupByDate = (items) => {
  return items.reduce((acc, item) => {
    acc[item.date] = acc[item.date] || [];
    acc[item.date].push(item);
    return acc;
  }, {});
};

const groupByCat = (items) => {
  return items.reduce((acc, item) => {
    acc[item.cat] = acc[item.cat] || [];
    acc[item.cat].push(item);
    return acc;
  }, {});
};

const baseHeight = 812;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function RewardScreen({ navigation }) {
  const [isWaitListJoined, setIsWaitListJoined] = useState(false);
  const [stImg, setStImg] = useState(require("../../assets/clock.png"));
  const [stText, setStText] = useState("Sent money is pending");
  const [activeFilter, setActiveFilter] = useState("All categories");
  const [activeStatus, setActiveStatus] = useState("All status");
  const [showTranStatus, setShowTranStatus] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [categories, setCategories] = useState(false);
  const filters = [
    { name: "All categories", cat: "Quick action" },
    { name: "Add money", cat: "Quick action" },
    { name: "Send money", cat: "Quick action" },
    { name: "Card", cat: "Quick action" },
    { name: "QR code", cat: "Quick action" },
    { name: "Referral", cat: "Quick action" },
    // { name: "Airtime", cat: "Bill payment" },
    // { name: "Internet", cat: "Bill payment" },
    // { name: "Water", cat: "Bill payment" },
    // { name: "Electricity", cat: "Bill payment" },
  ];
  const status = ["All status", "Earning", "Redeemed"];

  const transactions = [
    {
      title: "SFx point earned",
      amount: "5.00",
      type: "bank account",
      time: "6:00 am",
      date: "Today",
    },
    {
      title: "SFx point earned",
      amount: "5.00",
      type: "P2P",
      time: "6:00 am",
      date: "Today",
    },
    {
      title: "SFx point used",
      amount: "5.00",
      type: "mobile money",
      time: "6:00 am",
      date: "Today",
    },
    {
      title: "SFx point earned",
      amount: "5.00",
      type: "sfx money app",
      time: "6:00 am",
      date: "Today",
    },
    {
      title: "SFx point earned",
      amount: "5.00",
      type: "water",
      time: "6:00 am",
      date: "Yesterday",
    },
    {
      title: "SFx point used",
      amount: "5.00",
      type: "water",
      time: "6:00 am",
      date: "Yesterday",
    },
  ];
  const groupedTransactions = groupByDate(transactions);
  const groupedFilters = groupByCat(filters);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Reward history"
          navigation={navigation}
          iconComp={
            transactions.length === 0 ? (
              <></>
            ) : (
              <TouchableOpacity
                style={{ position: "absolute", right: 0 }}
                onPress={() => setShowDatePicker(true)}
              >
                <SvgXml xml={svg.calander} />
              </TouchableOpacity>
            )
          }
        />
        {transactions.length === 0 ? (
          <></>
        ) : (
          <View
            style={{
              width: "100%",
              borderTopWidth: 1,
              borderBottomWidth: 1,
              borderColor: colors.stroke,
              alignItems: "center",
            }}
          >
            <View style={{ width: "90%", height: 64, flexDirection: "row" }}>
              <TouchableOpacity
                style={{
                  flexDirection: "row",
                  width: "50%",
                  alignItems: "center",
                  justifyContent: "center",
                }}
                onPress={() => setCategories(true)}
              >
                <P style={{ fontSize: 12, marginRight: 8 }}>{activeFilter}</P>
                <SvgXml xml={svg.chevDown} />
              </TouchableOpacity>
              <TouchableOpacity
                style={{
                  flexDirection: "row",
                  width: "50%",
                  alignItems: "center",
                  justifyContent: "center",
                }}
                onPress={() => setShowTranStatus(true)}
              >
                <P style={{ fontSize: 12, marginRight: 8 }}>{activeStatus}</P>
                <SvgXml xml={svg.chevDown} />
              </TouchableOpacity>
            </View>
          </View>
        )}
        <ScrollView>
          <View style={styles.itemBox}>
            <View style={styles.accountBalance}></View>
          </View>
          <View style={{ width: "90%", alignSelf: "center" }}>
            {Object.keys(groupedTransactions).length === 0 ? (
              <View style={styles.emptyCont}>
                <SvgXml
                  xml={svg.walletFace}
                  style={{ marginTop: (20 * height) / 100, marginBottom: 16 }}
                />
                <P
                  style={{
                    fontFamily: fonts.poppinsMedium,
                    marginBottom: 4,
                  }}
                >
                  No rewards!
                </P>
                <P
                  style={{
                    color: "#A5A1A1",
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  You have no rewards yet
                </P>
              </View>
            ) : (
              <View>
                {Object.keys(groupedTransactions).map((date, index) => (
                  <View key={index} style={{ marginTop: 24 }}>
                    <P style={styles.datCat}>{date}</P>
                    <View>
                      {groupedTransactions[date].map((item, index) => (
                        <TouchableOpacity
                          onPress={() => {
                            navigation.navigate("PointDetailsScreen", {
                              type: item.title.toLowerCase().includes("earned")
                                ? "earned"
                                : "used",
                            });
                          }}
                        >
                          <View style={styles.item} key={index}>
                            <SvgXml
                              xml={
                                item.title.toLowerCase().includes("earned")
                                  ? svg.coin
                                  : svg.redCoin
                              }
                            />
                            <View style={{ marginLeft: 12 }}>
                              <P style={styles.transactionAmount}>
                                {item.title}
                              </P>
                              <P style={styles.transactionDate}>{item.time}</P>
                            </View>
                            <View
                              style={{
                                position: "absolute",
                                right: 16,
                                top: 16,
                                bottom: 16,
                                alignItems: "flex-end",
                              }}
                            >
                              <P
                                style={{
                                  fontSize: 12,
                                  fontFamily: fonts.poppinsMedium,
                                }}
                              >
                                {item.title.toLowerCase().includes("earned")
                                  ? "+"
                                  : "-"}
                                {item.amount}
                                <P style={{ fontSize: 10 }}>SFxp</P>
                              </P>
                              <P
                                // @ts-ignore
                                style={[styles.transactionDate]}
                              >
                                {item.status}
                              </P>
                            </View>
                          </View>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>
                ))}
              </View>
            )}
          </View>
        </ScrollView>
      </Div>
      <BottomSheet
        isVisible={categories}
        onClose={() => setCategories(false)}
        showBackArrow={false}
        backspaceText="Transaction type"
        modalContentStyle={{ height: "45%" }}
        extraModalStyle={{ height: "43%" }}
        components={
          <ScrollView>
            <View style={{ width: "100%", alignSelf: "center" }}>
              {Object.keys(groupedFilters).map((item, index) => (
                <>
                  <P
                    style={{
                      marginTop: (24 / baseHeight) * height,
                      fontSize: 12,
                    }}
                    key={index}
                  >
                    {item}
                  </P>
                  <View style={{ flexWrap: "wrap", flexDirection: "row" }}>
                    {groupedFilters[item].map((item, index) => (
                      <TouchableOpacity
                        onPress={() => setActiveFilter(item.name)}
                        key={index}
                        style={{
                          paddingLeft: 16,
                          paddingRight: 16,
                          paddingTop: (13 / baseHeight) * height,
                          paddingBottom: (13 / baseHeight) * height,
                          backgroundColor:
                            activeFilter === item.name
                              ? colors.primary
                              : colors.lowOpPrimary3,
                          marginRight: 16,
                          marginTop: (16 / baseHeight) * height,
                          borderRadius: 8,
                        }}
                      >
                        <P
                          style={{
                            fontSize: 12,
                            lineHeight: 18,
                            color:
                              activeFilter === item.name
                                ? colors.white
                                : colors.black,
                          }}
                        >
                          {item.name}
                        </P>
                      </TouchableOpacity>
                    ))}
                  </View>
                </>
              ))}
            </View>
          </ScrollView>
        }
      />
      <BottomSheet
        isVisible={showTranStatus}
        onClose={() => setShowTranStatus(false)}
        modalContentStyle={{ height: "45%" }}
        extraModalStyle={{ height: "43%" }}
        components={
          <ScrollView>
            <View>
              {status.map((item, index) => (
                <TouchableOpacity
                  onPress={() => setActiveStatus(item)}
                  style={{
                    paddingTop: (13 / baseHeight) * height,
                    paddingBottom: (13 / baseHeight) * height,
                    paddingLeft: (16 / baseWidth) * width,
                    paddingRight: (16 / baseWidth) * width,
                    marginTop: 24,
                    borderRadius: 10,
                    backgroundColor:
                      activeStatus === item
                        ? colors.lowOpPrimary3
                        : "transparent",
                  }}
                >
                  <P style={{ fontSize: 12 }}>{item}</P>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        }
      />
      <BottomSheet
        isVisible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        showBackArrow={false}
        backspaceText="Date"
        components={<View></View>}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  itemBox: {
    width: "90%",
    alignItems: "center",
    marginTop: 16,
    alignSelf: "center",
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    width: "80%",
    fontSize: 12,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
  accountBalance: {
    alignItems: "flex-start",
    // backgroundColor:"red",
  },
  balanceText: {
    fontSize: 12,
    color: "rgba(22, 24, 23, 0.6)",
  },
  balanceAmount: {
    fontSize: 24,
    // fontWeight: "bold",
    fontFamily: "poppins-semibold",
    color: "rgba(22, 24, 23, 1)",
  },
  addMoneyButton: {
    // marginTop: 10,
    // paddingVertical: 8,
    paddingTop: 4,
    paddingRight: 16,
    paddingBottom: 4,
    paddingLeft: 16,
    backgroundColor: "rgba(140, 82, 255, 1)",
    borderRadius: 20,
  },
  addMoneyText: {
    color: "#fff",
    fontSize: 12,
  },
  actions: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  actionButton: {
    alignItems: "center",
  },
  actionButtonText: {
    marginTop: 8,
    fontSize: 12,
    color: "rgba(22, 24, 23, 1)",
  },
  item: {
    width: "100%",
    padding: 16,
    backgroundColor: colors.white,
    marginTop: 8,
    borderRadius: 12,
    flexDirection: "row",
  },
  transactionAmount: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    // fontWeight: "bold",
  },
  transactionDate: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  emptyCont: {
    width: "100%",
    height: (60 * height) / 100,
    // backgroundColor: "red",
    alignItems: "center",
    // justifyContent: "center",
  },
  datCat: {
    fontSize: 12,
  },
});
