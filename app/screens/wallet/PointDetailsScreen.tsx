import React from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";

const { width, height } = Dimensions.get("window");

export default function PointDetailsScreen({ navigation, route }) {
  const { type } = route.params;
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="SFx point details" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <View
                style={{
                  width: 32,
                  height: 32,
                  backgroundColor: colors.white,
                  position: "absolute",
                  top: "-4%",
                  alignSelf: "center",
                  borderRadius: 100,
                  alignItems: "center",
                  justifyContent: "center",
                  zIndex: 100,
                }}
              >
                <SvgXml
                  width={18}
                  height={18}
                  xml={type === "earned" ? svg.coin : svg.redCoin}
                />
              </View>
              <DetailCard
                amount={
                  <>
                    <P style={{ fontSize: 24, lineHeight: 36, marginRight: 2 }}>
                      {type === 'earned'? "+":"-"}50
                    </P>
                    <P style={{ marginTop: 5 }}>USD</P>
                  </>
                }
                convertedAmount={
                  <View
                    style={{
                      paddingTop: 4,
                      paddingBottom: 4,
                      paddingLeft: 8,
                      paddingRight: 8,
                      backgroundColor: colors.secBackground,
                      borderRadius: 99,
                      flexDirection: "row",
                      alignItems: "center",
                    }}
                  >
                    <View
                      style={[
                        styles.indicator,
                        {
                          backgroundColor:
                            type === "earned" ? colors.green : colors.red,
                        },
                      ]}
                    ></View>
                    <P style={{fontSize: 10,}}>{type === "earned" ? "Earned" : "Used"}</P>
                  </View>
                }
                bottomComponent={
                  <View style={styles.desCont}>
                    <View style={styles.items}>
                      <P style={styles.holder}>Fee</P>
                      <P style={styles.value}>
                        0{" "}
                        <P
                          // @ts-ignore
                          style={[
                            styles.value,
                            { fontFamily: fonts.poppinsRegular },
                          ]}
                        >
                          USD
                        </P>
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Exchange rate</P>
                      <P style={styles.value}>1 USD ~1600 NGN</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Bill payment</P>
                      <P style={styles.value}>Airtime</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Account</P>
                      <P style={styles.value}>USDT</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Reference number</P>
                      <View
                        style={{
                          flexDirection: "row",
                          width: 150,
                          justifyContent: "flex-end",
                          alignItems: "center",
                        }}
                      >
                        <TouchableOpacity>
                          <SvgXml
                            xml={svg.lightCopy}
                            style={{ marginRight: 10 }}
                          />
                        </TouchableOpacity>
                        <P
                          // @ts-ignore
                          style={[
                            styles.value,
                            { textAlign: "right", width: 120 },
                          ]}
                        >
                          1224556352145232093874982
                        </P>
                      </View>
                    </View>
                    <View style={[styles.items, { marginBottom: 0 }]}>
                      <P style={styles.holder}>Processing time</P>
                      <P style={styles.value}>6:00 am - 12 jul 2014</P>
                    </View>
                  </View>
                }
              />
              <View style={styles.buttonWrap}>
                <Button btnText="View transaction" onPress={()=>{navigation.navigate("PointTransactionDetails")}}/>
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.secBackground,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 100,
    marginRight: 4,
  },
});
