import React, { useEffect, useState, useCallback } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  StatusBar,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  ImageBackground,
} from "react-native";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Link from "../../components/Link";
import NoteComponent2 from "../../components/NoteComponent2";
import Constants from "expo-constants";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import H4 from "../../components/H4";
import Insight from "./Insight";
import { GetUserWallet, GetWalletById } from "../../RequestHandlers/Wallet";
import BottomSheet from "../../components/BottomSheet";
import CountrySelect from "../../components/CountrySelect";
import { WalletSkeleton } from "../../Skeletons/Skeletons";
import { GetUserDetails } from "../../RequestHandlers/User";
import { useFocusEffect } from "@react-navigation/native";
import CurrencySelect from "../../components/CurrencySelect";
import { countries } from "../../components/counties";
import NetInfo from "@react-native-community/netinfo";
import Offline from "../../components/ErrorSate/Offline";
import { useToast } from "../../context/ToastContext";
import { truncateAddress } from "../../Utils/formatEmail";
import * as Clipboard from "expo-clipboard";
import Loader from "../../components/ActivityIndicator";
import Ionicons from "@expo/vector-icons/Ionicons";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { formatToTwoDecimals } from "../../Utils/numberFormat";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import {
  getWalletAddressCache,
  saveWalletAddressCache as saveWalletCache,
  isWalletAddressCacheExpired
} from "../../Utils/walletAddressCacheUtils";

const baseHeight = 802;
const { width, height } = Dimensions.get("window");
export default function WalletScreen({ navigation }) {
  // const tabs = ["Account", "Insight"];
  const tabs = ["Account"];
  const [activeTab, setActiveTab] = useState("Account");
  const [hideBal, setHideBal] = useState(false);
  const [loader, setLoader] = useState(false);
  const [flag, setFlag] = useState(require("../../assets/turkey.png"));
  const [country, setCountry] = useState("Turkish lira");
  const [showCountries, setShowCountries] = useState(false);
  const [usBal, setUsBal] = useState(0.0);
  const [trBal, setTrBal] = useState(0.0);
  const [wallets, setWallets] = useState([]);
  const [curSymbol, setCurSymbol] = useState("₺");
  const [curCode, setCurCode] = useState("TRY");
  const [localAmount, setLocalAmount] = useState(0);
  const [homeCountry, setHomeCountry] = useState("");
  const [isOnline, setIsOnline] = useState(false);
  const { handleToast } = useToast();
  const [loading, setLoading] = useState(false);
  const [hideLearnCard, setHideLearnCard] = useState(false);
  const [userId, setUserId] = useState("");
  const [isCopied, setIsCopied] = useState(false);
  const [walletAddressCache, setWalletAddressCache] = useState<{[key: string]: {address: string, timestamp: number}}>({});
  const [loadingAddresses, setLoadingAddresses] = useState<{[key: string]: boolean}>({});

  // Memoize the handlers to prevent unnecessary re-renders
  const handleActiveCountry = useCallback((newActiveType: string | null) => {
    if (newActiveType) {
      setCountry(newActiveType);
    }
  }, []);

  const handleActiveHomeCountry = useCallback(
    (newActiveType: string | null) => {
      if (newActiveType === "DR_Congo") {
        setHomeCountry("DR Congo");
      } else if (newActiveType) {
        setHomeCountry(newActiveType);
      }
    },
    []
  );

  const handleActiveFlag = useCallback((newActiveType: any | null) => {
    if (newActiveType) {
      setFlag(newActiveType);
    }
  }, []);

  const handleActiveCurSymbol = useCallback((newActiveType: any | null) => {
    if (newActiveType) {
      setCurSymbol(newActiveType);
    }
  }, []);

  const handleActiveCurCode = useCallback((newActiveType: any | null) => {
    if (newActiveType) {
      setCurCode(newActiveType);
    }
  }, []);

  const copyAddress = useCallback(async (address: string) => {
    const copiedText = await Clipboard.setStringAsync(address);

    if (copiedText === true) {
      setIsCopied(true);
      setTimeout(() => {
        setIsCopied(false);
      }, 4000);
    }
  }, []);

  const handleHideLearnCard = useCallback(async () => {
    try {
      if (userId) {
        // Store the current timestamp with the user ID
        const timestamp = new Date().getTime();
        await AsyncStorage.setItem(
          `hideLearnCard_${userId}`,
          timestamp.toString()
        );
        setHideLearnCard(true);
      }
    } catch (error) {
      console.error("Error hiding learn card:", error);
    }
  }, [userId]);

  const checkLearnCardVisibility = useCallback(async (userId: string) => {
    try {
      const storedTimestamp = await AsyncStorage.getItem(
        `hideLearnCard_${userId}`
      );

      if (storedTimestamp) {
        const timestamp = parseInt(storedTimestamp);
        const currentTime = new Date().getTime();
        const hoursDiff = (currentTime - timestamp) / (1000 * 60 * 60);

        // If less than 24 hours have passed, hide the card
        if (hoursDiff < 24) {
          setHideLearnCard(true);
        } else {
          // If more than 24 hours have passed, remove the stored timestamp
          await AsyncStorage.removeItem(`hideLearnCard_${userId}`);
          setHideLearnCard(false);
        }
      } else {
        setHideLearnCard(false);
      }
    } catch (error) {
      console.error("Error checking learn card visibility:", error);
    }
  }, []);

  // Load wallet address cache from AsyncStorage
  const loadWalletAddressCache = useCallback(async () => {
    try {
      const cachedAddresses = await getWalletAddressCache();
      setWalletAddressCache(cachedAddresses);
    } catch (error) {
      console.error("Error loading wallet address cache:", error);
    }
  }, []);

  // Save wallet address cache to AsyncStorage
  const saveWalletAddressCache = useCallback(async (cache: {[key: string]: {address: string, timestamp: number}}) => {
    try {
      await saveWalletCache(cache);
    } catch (error) {
      console.error("Error saving wallet address cache:", error);
    }
  }, []);

  const getUserDetails = useCallback(async () => {
    try {
      const userDetails = await withApiErrorToast(
        GetUserDetails(),
        handleToast
      );
      // if (
      //   !userDetails.homeCountry ||
      //   !userDetails.username ||
      //   !userDetails?.hasPin
      // ) {
      //   navigation.navigate("AccountVerificationPromt");
      // }

      // Set the user ID and check learn card visibility
      if (userDetails.id) {
        setUserId(userDetails.id);
        checkLearnCardVisibility(userDetails.id);
      }

      const matchedCountry = countries.find(
        (item) => item.country === userDetails?.homeCountry
      );
      if (matchedCountry) {
        // Batch state updates to prevent race conditions
        setFlag(matchedCountry.flag);
        setCountry(matchedCountry.currency);
        setCurCode(matchedCountry.currencyCode);
        setCurSymbol(matchedCountry.symbol);
        setHomeCountry(matchedCountry.country);
      }
    } catch (error) {
      console.error("Error fetching user details:", error);
      handleToast("Unkown error", "error");
    }
  }, [navigation, handleToast, checkLearnCardVisibility]);

  const getWalById = useCallback(async (id: string) => {
    try {
      // Check if address is already cached and not expired (24 hours)
      const cachedData = walletAddressCache[id];
      if (cachedData) {
        // If not expired, use cached address
        if (!isWalletAddressCacheExpired(cachedData.timestamp)) {
          return cachedData.address;
        }
      }

      // Set loading state for this specific wallet
      setLoadingAddresses(prev => ({ ...prev, [id]: true }));

      const walletDetails = await withApiErrorToast(
        GetWalletById(id),
        handleToast
      );

      // Clear loading state
      setLoadingAddresses(prev => ({ ...prev, [id]: false }));

      if (!walletDetails?.wallet) {
        return "Unknown address (no wallet)";
      }
      const asset = walletDetails.wallet.assets;
      if (!Array.isArray(asset) || asset.length === 0) {
        return "Unknown address (no assets)";
      }
      const depositAddress = asset[0]?.depositAddress;

      if (depositAddress) {
        // Update cache with new address and timestamp
        const updatedCache = {
          ...walletAddressCache,
          [id]: {
            address: depositAddress,
            timestamp: new Date().getTime()
          }
        };
        setWalletAddressCache(updatedCache);
        saveWalletAddressCache(updatedCache);
      }

      return depositAddress || "Unknown address (no deposit address)";
    } catch (error) {
      console.error("Error fetching wallet:", error); // Always log errors!
      // Clear loading state on error
      setLoadingAddresses(prev => ({ ...prev, [id]: false }));
      return "Unknown address (error)";
    }
  }, [walletAddressCache, saveWalletAddressCache, handleToast]);

  const getWallet = useCallback(async () => {
    setLoading(true);
    try {
      const wallet = await withApiErrorToast(
        GetUserWallet(homeCountry),
        handleToast
      );
      if (wallet.wallets) {
        // Batch state updates
        setLoader(false);
        setUsBal(wallet.totalInUsd);
        setTrBal(wallet.totalInTRY);
        setWallets(wallet.wallets);
        setLocalAmount(wallet?.totalInLocal);

        // Fetch addresses for all wallets that aren't cached or are expired
        wallet.wallets.forEach((walletItem: any) => {
          const cachedData = walletAddressCache[walletItem.id];
          if (!cachedData) {
            getWalById(walletItem.id);
          } else {
            // Check if cache is expired (24 hours)
            if (isWalletAddressCacheExpired(cachedData.timestamp)) {
              getWalById(walletItem.id);
            }
          }
        });
      } else {
        setLoader(false);
        // handleToast("Error fetching balance", "error");
      }
    } catch (error) {
      console.error("Error fetching wallet:", error);
    } finally {
      setLoading(false);
    }
  }, [homeCountry, walletAddressCache, getWalById]);

  const checkConnection = useCallback(() => {
    // Return the unsubscribe function to properly clean up the listener
    return NetInfo.addEventListener((state) => {
      if (state.isConnected === false) {
        setIsOnline(false);
      } else if (state.isConnected === true) {
        setIsOnline(true);
      }
    });
  }, []);
  // Effect to fetch wallet data when homeCountry changes
  useEffect(() => {
    if (homeCountry) {
      setLoading(true);
      getWallet();
    }
  }, [homeCountry, getWallet]);

  // Initial data loading effect
  useEffect(() => {
    setLoader(true);
    getWallet();

    // Clean up network listener on unmount
    const unsubscribe = checkConnection();
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [getWallet, checkConnection]);

  // Effect when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      getUserDetails();
      const unsubscribe = checkConnection();

      return () => {
        if (unsubscribe) {
          unsubscribe();
        }
      };
    }, [getUserDetails, checkConnection])
  );

  // Load wallet address cache on component mount
  useEffect(() => {
    loadWalletAddressCache();
  }, [loadWalletAddressCache]);
  return (
    <View style={styles.body}>
      {loader ? (
        <Loader />
      ) : (
        <View style={{ paddingTop: Constants.statusBarHeight }}>
          <View style={{}}>
            {/* <AuthenticationHedear text="Swap" navigation={navigation}/> */}
            <View style={styles.wHeader}>
              <P style={{ marginBottom: 12 }}>Wallet</P>
            </View>
            {/* <View style={styles.tabs}>
              {tabs.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => {
                    setActiveTab(item);
                  }}
                >
                  <View
                    style={[
                      styles.tabBtn,
                      {
                        backgroundColor:
                          activeTab === item ? colors.white : "transparent",
                      },
                    ]}
                  >
                    <P
                      //@ts-ignore
                      style={[
                        styles.tabBtnP,
                        {
                          color:
                            activeTab === item ? colors.primary : colors.gray,
                        },
                      ]}
                    >
                      {item}
                    </P>
                  </View>
                </TouchableOpacity>
              ))}
            </View> */}
          </View>
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              paddingBottom: (90 / baseHeight) * height,
              marginTop: 16,
            }}
          >
            {!isOnline ? (
              <Offline
                onPress={() => {
                  checkConnection();
                }}
              />
            ) : (
              <>
                {activeTab === "Insight" ? (
                  <Insight />
                ) : (
                  <View style={styles.itemBox}>
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                        }}
                      >
                        <P style={{ fontSize: 12, lineHeight: 18 }}>
                          Total wallet balance
                        </P>
                        <TouchableOpacity onPress={() => setHideBal(!hideBal)}>
                          <SvgXml
                            xml={hideBal ? svg.eyeClose : svg.eyeOpen}
                            style={{ marginLeft: 8 }}
                          />
                        </TouchableOpacity>
                      </View>
                      <TouchableOpacity
                        onPress={() => {
                          getWallet();
                        }}
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                          gap: 8,
                        }}
                      >
                        <P
                          style={{
                            color: colors.black,
                            fontSize: 12,
                            fontFamily: fonts.poppinsRegular,
                          }}
                        >
                          Refresh balance
                        </P>
                        <Ionicons name="refresh" size={14} color="black" />
                      </TouchableOpacity>
                    </View>
                    {/* <P style={styles.secP}>Currency balance</P> */}
                    {wallets.length > 0 && (
                      <>
                        <View style={[styles.section]}>
                          <View style={styles.cardSec}>
                            {wallets?.map((item) => {
                              const cachedData = walletAddressCache[item.id];
                              const walletAddress = cachedData?.address || "";
                              const isLoadingAddress = loadingAddresses[item.id] || false;
                              return (
                                <React.Fragment key={`wallet-${item.id}`}>
                                  <TouchableOpacity
                                    key={item.id}
                                    style={[
                                      styles.card,
                                      {
                                        backgroundColor: colors.primary,
                                      },
                                    ]}
                                    onPress={() => {
                                      navigation.navigate("WalletViewScreen", {
                                        wallet: `${
                                          item.asset === "USDT"
                                            ? " Tether"
                                            : "United state dollar coin"
                                        }`,
                                        data: item,
                                      });
                                    }}
                                  >
                                    <SvgXml
                                      xml={
                                        item?.asset === "USDT"
                                          ? svg.tather
                                          : svg.usdCoin
                                      }
                                    />

                                    <View
                                      style={{
                                        width: "100%",
                                        alignItems: "flex-start",
                                      }}
                                    >
                                      {loading ? (
                                        <ActivityIndicator
                                          color={colors.white}
                                          style={{ marginTop: 16 }}
                                        />
                                      ) : (
                                        <>
                                          <P style={styles.cardAmt}>
                                            {hideBal
                                              ? "******"
                                              : formatToTwoDecimals(
                                                  Number(item?.balance)
                                                )}
                                            <P
                                              // @ts-ignore
                                              style={[
                                                styles.syCurrency,
                                                { color: colors.white },
                                              ]}
                                            >
                                              {hideBal ? "***" : `USDC`}
                                            </P>
                                          </P>
                                          <View
                                            style={{
                                              flexDirection: "row",
                                              alignItems: "center",
                                              gap: 4,
                                              marginTop: 4,
                                            }}
                                          >
                                            <P style={styles.cardText}>USDC</P>
                                            <View
                                              style={styles.bulletPoint}
                                            ></View>
                                            <P
                                              // @ts-ignore
                                              style={[
                                                styles.cardText,
                                                { marginTop: 0 },
                                              ]}
                                            >
                                              Polygon
                                            </P>
                                          </View>
                                          <View
                                            style={{
                                              flexDirection: "row",
                                              alignItems: "center",
                                              gap: 8,
                                              marginTop: 4,
                                            }}
                                          >
                                            {isLoadingAddress ? (
                                              <ActivityIndicator
                                                size="small"
                                                color={colors.white}
                                                style={{ marginRight: 8 }}
                                              />
                                            ) : (
                                              <P style={styles.cardText}>
                                                {walletAddress
                                                  ? truncateAddress(
                                                      walletAddress,
                                                      10,
                                                      12
                                                    )
                                                  : "...."}
                                              </P>
                                            )}
                                            <TouchableOpacity
                                              style={{
                                                width: 30,
                                                height: 30,
                                                alignItems: "center",
                                                justifyContent: "center",
                                              }}
                                              disabled={isCopied || isLoadingAddress}
                                              onPress={() => {
                                                copyAddress(walletAddress);
                                              }}
                                            >
                                              <SvgXml
                                                xml={
                                                  isCopied
                                                    ? svg.circleSuccess
                                                    : svg.lightCopy
                                                }
                                              />
                                            </TouchableOpacity>
                                          </View>
                                        </>
                                      )}
                                    </View>
                                  </TouchableOpacity>
                                </React.Fragment>
                              );
                            })}
                          </View>
                        </View>
                        {wallets.length > 0 && (
                          <View style={{ marginTop: 16 }}>
                            {!hideLearnCard && (
                              <ImageBackground
                                style={styles.learnCard}
                                source={require("../../assets/prFrame/pr3.png")}
                              >
                                <SvgXml xml={svg.largeCoin} />
                                <View style={{ width: "50%", marginLeft: 16 }}>
                                  <P style={styles.learnText}>
                                    The world most regulated digital dollar
                                  </P>
                                  <TouchableOpacity
                                    style={{
                                      flexDirection: "row",
                                      alignItems: "center",
                                    }}
                                    onPress={() => {
                                      navigation.navigate(
                                        "InAppBrowserScreen",
                                        {
                                          url: "https://www.usdc.com/",
                                          title: "USDC - Digital Dollar",
                                        }
                                      );
                                    }}
                                  >
                                    <P
                                      style={[
                                        styles.learnText,
                                        {
                                          marginBottom: 0,
                                          fontFamily: fonts.poppinsSemibold,
                                          textDecorationLine: "underline",
                                        },
                                      ]}
                                    >
                                      Learn more
                                    </P>
                                    <SvgXml xml={svg.bArrow} />
                                  </TouchableOpacity>
                                </View>
                                <TouchableOpacity
                                  style={styles.bCancel}
                                  onPress={handleHideLearnCard}
                                >
                                  <SvgXml xml={svg.cancelBrown} />
                                </TouchableOpacity>
                              </ImageBackground>
                            )}
                            <NoteComponent2
                              text={
                                "Your total wallet balance, in any currency of your choice, is the sum of your USD accounts"
                              }
                            />
                            <P style={styles.secP}>Currency balance</P>
                          </View>
                        )}
                      </>
                    )}
                    <View
                      style={{
                        // flexDirection: "row",
                        alignItems: "center",
                        marginTop: 16,
                      }}
                    >
                      <View
                        style={[
                          styles.cardD,
                          { backgroundColor: colors.white },
                        ]}
                      >
                        <Image
                          source={require("../../assets/usFlag.png")}
                          style={styles.imgSy}
                        />
                        {/* @ts-ignore */}
                        <P style={[styles.syText, { color: colors.dGray }]}>
                          United state dollar
                        </P>
                        <View
                          style={{ width: "100%", alignItems: "flex-start" }}
                        >
                          {loading ? (
                            <ActivityIndicator
                              color={colors.white}
                              style={{ marginTop: 16 }}
                            />
                          ) : (
                            <H4
                              // @ts-ignore
                              style={[styles.syAmount, { color: colors.black }]}
                            >
                              {hideBal
                                ? "******"
                                : `$${formatToTwoDecimals(Number(usBal))}`}
                              <P
                                // @ts-ignore
                                style={[
                                  styles.syCurrency,
                                  { color: colors.black },
                                ]}
                              >
                                {hideBal ? "***" : `USD`}
                              </P>
                            </H4>
                          )}
                        </View>
                      </View>
                      <TouchableOpacity
                        style={[
                          styles.cardD,
                          { backgroundColor: colors.white, marginTop: 16 },
                        ]}
                        onPress={() => {
                          setShowCountries(true);
                        }}
                      >
                        <View
                          style={{ flexDirection: "row", alignItems: "center" }}
                        >
                          <Image source={flag} style={styles.imgSy} />
                          <SvgXml
                            xml={svg.arrowDown}
                            style={{ marginLeft: 6 }}
                          />
                        </View>
                        <P style={styles.syText}>{country}</P>
                        <View
                          style={{ width: "100%", alignItems: "flex-start" }}
                        >
                          {loading ? (
                            <ActivityIndicator
                              color={colors.primary}
                              style={{ marginTop: 16 }}
                            />
                          ) : (
                            <H4 style={styles.syAmount}>
                              {hideBal
                                ? "******"
                                : `${curSymbol}${
                                    country
                                      .toLowerCase()
                                      .includes("turkish lira")
                                      ? formatToTwoDecimals(Number(trBal))
                                      : formatToTwoDecimals(Number(localAmount))
                                  }`}
                              <P style={styles.syCurrency}>
                                {hideBal ? "***" : curCode}
                              </P>
                            </H4>
                          )}
                        </View>
                      </TouchableOpacity>
                    </View>

                    <View style={styles.section}>
                      {/* <P style={styles.secP}>Other</P> */}
                      {/* <View style={styles.cardSec}>
                <TouchableOpacity
                  style={[styles.card, { marginBottom: 16 }]}
                  onPress={() => navigation.navigate("CardScreen")}
                >
                  <SvgXml xml={svg.cardF} />
                  <P style={styles.cardText}>Card</P>
                  <P style={styles.cardAmt}>
                    {hideBal ? "******" : `$0.00`}
                    <P style={styles.amtCur}>{hideBal ? "***" : `USD`}</P>
                  </P>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.card, {marginBottom: 40}]}
                  onPress={() => {
                    navigation.navigate("MainRewardScreen");
                  }}
                >
                  <SvgXml xml={svg.refferal} />
                  <P style={styles.cardText}>SFx point</P>
                  <P style={styles.cardAmt}>
                    {hideBal ? "******" : `0.00`}
                    <P style={styles.amtCur}>{hideBal ? "****" : `SFxp`}</P>
                  </P>
                </TouchableOpacity>
              </View> */}
                    </View>
                  </View>
                )}
              </>
            )}
          </ScrollView>
        </View>
      )}

      <BottomSheet
        isVisible={showCountries}
        showBackArrow={false}
        backspaceText="Select country"
        onClose={() => setShowCountries(false)}
        modalContentStyle={{ height: "75%" }}
        extraModalStyle={{ height: "73%" }}
        components={
          <CurrencySelect
            onActiveCountryChange={handleActiveCountry}
            onActiveFlag={handleActiveFlag}
            onCurSymbolChange={handleActiveCurSymbol}
            onCurCodeChange={handleActiveCurCode}
            onActiveHomeCountry={handleActiveHomeCountry}
            onPress={() => {
              setShowCountries(false);
            }}
            excludedCountries={"United States"}
          />
        }
      />
      {/* <Loader loading={true} visible={loader} /> */}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    width,
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  itemBox: {
    width: (90 * width) / 100,
    alignSelf: "center",
    paddingTop: 24,
    paddingBottom: (90 / baseHeight) * height,
    // backgroundColor: "red",
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    width: "80%",
    fontSize: 12,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
  wHeader: {
    width: (100 * width) / 100,
    alignItems: "flex-start",
    marginTop: 24,
    paddingLeft: "5%",
    paddingRight: "5%",
    borderBottomWidth: 1,
    borderColor: colors.stroke,
  },
  tabBtn: {
    width: 83,
    height: 24,
    marginRight: 12,
    borderRadius: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  actBtn: {
    width: 83,
    height: 24,
    backgroundColor: "#fff",
    marginRight: 12,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  tabBtnP: {
    fontSize: 10,
    lineHeight: 18,
    fontFamily: fonts.poppinsMedium,
  },
  tabs: {
    width: (100 * width) / 100,
    flexDirection: "row",
    alignItems: "center",
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    paddingLeft: "5%",
    paddingRight: "5%",
    marginTop: 12,
    paddingBottom: 16,
  },
  cardD: {
    width: "100%",
    padding: 16,
    borderRadius: 12,
  },
  imgSy: {
    width: 24,
    height: 24,
    borderRadius: 100,
  },
  syText: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
    marginTop: 16,
  },
  syAmount: {
    fontSize: 20,
    lineHeight: 30,
    fontFamily: fonts.poppinsMedium,
  },
  syCurrency: {
    lineHeight: 30,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
  },
  section: {
    marginTop: 8,
  },
  secP: {
    fontSize: 12,
    marginTop: 16,
    lineHeight: 18,
    fontFamily: fonts.poppinsRegular,
    color: colors.dGray,
  },
  cardSec: {
    marginTop: 8,
  },
  card: {
    width: "100%",
    height: 148,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
  },
  cardText: {
    fontSize: 11,
    color: "#F1EBFF",
    lineHeight: 16.5,
    fontFamily: fonts.poppinsRegular,
  },
  cardAmt: {
    fontSize: 20,
    lineHeight: 30,
    fontFamily: fonts.poppinsMedium,
    marginTop: 16,
    color: colors.white,
  },
  amtCur: {
    fontSize: 12,
    lineHeight: 30,
  },
  bulletPoint: {
    width: 8,
    height: 8,
    borderRadius: 100,
    backgroundColor: "#F1EBFF",
  },
  learnCard: {
    width: "100%",
    height: 90,
    backgroundColor: "#914C2C",
    borderRadius: 12,
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginBottom: 16,
    flexDirection: "row",
    alignItems: "center",
    overflow: "hidden",
  },
  bCancel: {
    position: "absolute",
    right: 16,
    top: 10,
  },
  learnText: {
    fontSize: 12,
    color: colors.white,
    marginBottom: 4,
  },
});
