import React from "react";
import { ScrollView, StyleSheet, View } from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import H4 from "../../components/H4";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import NoteComponent2 from "../../components/NoteComponent2";

export default function Rules({ navigation }) {
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Rules" navigation={navigation} />
        <ScrollView contentContainerStyle={{ paddingBottom: 150 }}>
          <View
            style={{
              width: "90%",
              backgroundColor: colors.white,
              padding: 24,
              alignSelf: "center",
              borderRadius: 12,
              marginBottom: 50,
            }}
          >
            <P style={{ fontSize: 16, fontFamily: fonts.poppinsMedium }}>
              SFx point
            </P>
            <P
              style={{
                fontSize: 12,
                color: colors.gray,
                marginTop: 8,
                marginBottom: 24,
                fontFamily: fonts.poppinsRegular,
              }}
            >
              SFx Point is how our users benefit from sharing the SFx money app.
              You can earn SFx Points by completing actions such as referrals
              for now.
            </P>
            <View
              style={{
                borderBottomWidth: 1,
                borderColor: colors.stroke,
                borderStyle: "dashed",
              }}
            ></View>
            <View>
              <P
                style={{
                  fontSize: 16,
                  fontFamily: fonts.poppinsMedium,
                  marginTop: 24,
                }}
              >
                The benefit of SFx point
              </P>
              <P
                style={{
                  fontSize: 12,
                  color: colors.gray,
                  marginTop: 8,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                The SFx Points system encourages users to engage more with the
                money app, and share the value of the money app with other
                friends and family.
              </P>
              <P style={{ marginTop: 24 }}>Actions that qualify</P>
              <View style={styles.textItem}>
                <P style={{ color: colors.gray }}> {"\u2022"} </P>
                {/* <P style={styles.text}>
                  Money transfer: “Send money” above $15 and Add money above
                  15$.
                </P> */}
                <P style={styles.text}>Referrals.</P>
              </View>
              {/* <View style={styles.textItem}>
                <P style={{ color: colors.gray }}> {"\u2022"} </P>
                <P style={styles.text}>Referrals: refer, fund and spend.</P>
              </View> */}
              {/* <View style={styles.textItem}>
                <P style={{ color: colors.gray }}> {"\u2022"} </P>
                <P style={styles.text}>Card spending: Any spend above $15.</P>
              </View> */}
            </View>

            <View>
              <P style={{ marginTop: 24 }}>How to earn</P>
              <View style={[styles.textItem, { flexDirection: "column" }]}>
                <View style={{ display: "flex", flexDirection: "row" }}>
                  <P style={styles.text}>1. </P>
                  <P style={styles.text}>
                    Every successful referral earns the user 50SFX points.
                  </P>
                </View>
                <View style={{ display: "flex", flexDirection: "row" }}>
                  <P style={styles.text}>2. </P>
                  <P style={styles.text}>
                    SFx point is redeemable, every 1000SFx points
                  </P>
                </View>
              </View>
              <View
                style={[
                  styles.textItem,
                  { flexDirection: "column", marginTop: 16 },
                ]}
              >
                {/* <P style={{ color: colors.gray }}> {"\u20</P> */}
                <P style={{ fontSize: 12, fontFamily: fonts.poppinsRegular }}>
                  Redeem
                </P>
                <P style={styles.text}>
                  1. Minimum redemption point: 1000SFx Points.
                </P>
                <P style={styles.text}>
                  2. The user who shared his/her referral code must be verified.
                </P>
                <P style={styles.text}>
                  3. The “New User” who used the referral code, must verify and
                  transact up to $50 for the referrer (User who shared their
                  referral code) to earn.
                </P>
              </View>
              {/* <View
                style={[
                  styles.textItem,
                  { flexDirection: "column", marginTop: 16 },
                ]}
              >
                <P style={styles.text}>Referrals</P>
                <P style={styles.text}>
                  1.Every successful referral earns the user 50SFX points
                </P>
              </View> */}
            </View>
            <View
              style={{
                borderBottomWidth: 1,
                borderColor: colors.stroke,
                borderStyle: "dashed",
                marginTop: 24,
              }}
            ></View>
            <View>
              <P style={{ marginTop: 24 }}>Terms and conditions</P>
              <View style={[styles.textItem, { flexDirection: "column" }]}>
                <P style={styles.text}>
                  Only unique and legitimate users are eligible for referral
                  rewards. Referrals made to duplicate accounts or accounts
                  created solely for the purpose of earning referral rewards
                  will be disqualified.
                  {"\n"}
                </P>
                <View style={{ display: "flex", flexDirection: "row" }}>
                  <P style={styles.text}>1. </P>
                  <P style={styles.text}>
                    Only verified users can earn on referrals
                  </P>
                </View>
                <View style={{ display: "flex", flexDirection: "row" }}>
                  <P style={styles.text}>2. </P>
                  <P style={styles.text}>
                    Every successful referral earns the user 50SFX points.
                  </P>
                </View>
                <View style={{ display: "flex", flexDirection: "row" }}>
                  <P style={styles.text}>3. </P>
                  <P style={styles.text}>
                    Each SFx point is redeemable every 1000SFx points.
                  </P>
                </View>
                {/* <P style={styles.text}>
                  4. Points can not be spent for card transactions.
                </P>
                <P style={styles.text}>
                  5. SFx Points are credited only after the referred user
                  completes their first eligible transaction within the app.
                  This ensures the quality of referrals and reduces the risk of
                  fraud
                </P>
                <P style={styles.text}>
                  6. Points earned through referrals are non-transferable and
                  can only be used within the SFx app. They cannot be converted
                  to cash or transferred to other users
                </P>
                <P style={styles.text}>
                  7. Users may refer an unlimited number of friends, but each
                  referral must meet the eligibility criteria to earn points.
                </P>
                <P style={styles.text}>
                  8. If the referred user does not complete their first
                  transaction within 30 days of signing up, the referral will
                  expire, and no points will be awarded.
                </P> */}
              </View>
            </View>

            {/* <View style={{ marginTop: 24 }}>
              <NoteComponent2
                component={
                  <View style={{ width: "93%" }}>
                    <P style={{ fontSize: 10 }}>Important Rules:</P>
                    <View style={styles.textItem}>
                      <P style={{ fontSize: 10 }}> {"\u2022"} </P>
                      <P style={styles.nText}>
                        Eligible Transactions: Only completed and non-reversed
                        transactions qualify for SFx Points.
                      </P>
                    </View>
                    <View style={styles.textItem}>
                      <P style={{ fontSize: 10 }}> {"\u2022"} </P>
                      <P style={styles.nText}>
                        Point Calculation: SFx Points are calculated based on
                        the transaction amount after discounts, taxes, and other
                        deductions.
                      </P>
                    </View>
                    <View style={styles.textItem}>
                      <P style={{ fontSize: 10 }}> {"\u2022"} </P>
                      <P style={styles.nText}>
                        Fraud Prevention: Points will be revoked if a
                        transaction is found to be fraudulent or reversed.
                      </P>
                    </View>
                  </View>
                }
              />
            </View> */}
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  textItem: {
    flexDirection: "row",
    marginTop: 8,
  },
  text: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  nText: {
    fontSize: 10,
  },
});
