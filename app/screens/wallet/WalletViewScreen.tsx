import React, { useCallback, useEffect, useState } from "react";
import {
  Dimensions,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
  ActivityIndicator,
} from "react-native";
import P from "../../components/P";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { fonts } from "../../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import { GetTransation } from "../../RequestHandlers/Wallet";
import { GetCurrencyTransactions } from "../../RequestHandlers/Wallet";
import { useFocusEffect } from "@react-navigation/native";
import { formatDate, formatDate2 } from "../../components/FormatDate";
import {
  getTransactionLabel,
  TransactionClick,
} from "../../Utils/TransactionClick";
import {
  formatNumberWithCommas,
  formatToTwoDecimals,
} from "../../Utils/numberFormat";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import { useToast } from "../../context/ToastContext";

const groupByDate = (items) => {
  return items.reduce((acc, item) => {
    const dateOnly = new Date(item.updatedAt).toISOString().split("T")[0];
    acc[dateOnly] = acc[dateOnly] || [];
    acc[dateOnly].push(item);
    return acc;
  }, {});
};
const { width, height } = Dimensions.get("window");
export default function WalletViewScreen({ navigation, route }) {
  const { wallet, data } = route.params;
  const [hideBal, setHideBal] = useState(false);
  const [transactions, setTransations] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  function capitalizeFirstLetter(word) {
    if (!word) return ""; // handle empty strings
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
  }
  const { handleToast } = useToast();
  const getTransaction = async () => {
    setLoading(true);
    try {
      const transactions = await withApiErrorToast(
        GetCurrencyTransactions(data?.asset),
        handleToast
      );
      // Sort transactions by the 'updatedAt' field in descending order (latest date first)
      const sortedTransactions = transactions.items.sort((a, b) => {
        return (
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        ); // Convert to timestamps
      });
      if (sortedTransactions) {
        setLoading(false);
        setTransations(sortedTransactions);
      }
    } catch (error) {}
  };

  useFocusEffect(
    useCallback(() => {
      getTransaction();
    }, [])
  );
  const groupedTransactions = groupByDate(transactions);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear navigation={navigation} text={wallet} />

        <View style={{ width: "90%", alignSelf: "center" }}>
          <View style={styles.amtCard}>
            <View style={{ flexDirection: "row", alignItems: "center" }}>
              <P
                style={{
                  fontSize: 12,
                  lineHeight: 18,
                  color: colors.white,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Available asset balance
              </P>
              <TouchableOpacity onPress={() => setHideBal(!hideBal)}>
                <SvgXml
                  xml={svg.eyeOpenWhite}
                  width={16}
                  height={16}
                  style={{ marginLeft: 8 }}
                />
              </TouchableOpacity>
            </View>
            <P style={styles.amount}>
              {hideBal ? "******" : formatToTwoDecimals(data?.balance)}
              <P style={styles.amtCur}>{hideBal ? "****" : data?.asset}</P>
            </P>
          </View>
        </View>
        <View style={styles.actionCard}>
          <ActionButton
            text="Add"
            svg={svg.addWallet}
            onPress={() =>
              navigation.navigate("AddMoneyP2pScreen", { asset: data })
            }
          />
          <ActionButton
            text="Send"
            svg={svg.withdraw}
            onPress={() => navigation.navigate("P2pScreen", { asset: data })}
          />
          {/* <ActionButton
            text="Swap"
            svg={svg.coinswap}
            onPress={() => {
              navigation.navigate("SwapScreen");
            }}
          /> */}
          <ActionButton
            text="Details"
            svg={svg.gWallet}
            onPress={() => {
              navigation.navigate("WalletDetails", {
                wallet: wallet,
                data: data,
              });
            }}
          />
        </View>
        <View
          style={{
            width: "90%",
            alignSelf: "center",
            flexDirection: "row",
            justifyContent: "space-between",
            marginTop: 24,
            marginBottom: 12,
          }}
        >
          <P
            style={{
              color: "rgba(165, 161, 161, 1)",
              fontSize: 12,
            }}
          >
            Recent transactions
          </P>
          {transactions.length > 20 && (
            <TouchableOpacity
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
              }}
              onPress={() => {
                navigation.navigate("History");
              }}
            >
              <P
                style={{
                  fontSize: 12,
                  marginRight: 5,
                  color: colors.primary,
                  textDecorationLine: "underline",
                }}
              >
                See all
              </P>
              {/* <SvgXml xml={svg.arroveRight} /> */}
            </TouchableOpacity>
          )}
        </View>
        <View style={{ width: "90%", alignSelf: "center" }}>
          <>
            {loading ? (
              <View
                style={{
                  width: "100%",
                  height: "50%",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <ActivityIndicator
                  size={"large"}
                  color={colors.primary}
                  style={{
                    marginTop: 16,
                  }}
                />
              </View>
            ) : Object.keys(groupedTransactions).length === 0 ? (
              <View style={styles.emptyCont}>
                <SvgXml
                  xml={svg.walletFace}
                  style={{ marginTop: (20 * height) / 100, marginBottom: 16 }}
                />
                <P
                  style={{
                    fontFamily: fonts.poppinsMedium,
                    marginBottom: 4,
                  }}
                >
                  No transaction!
                </P>
                <P
                  style={{
                    color: "#A5A1A1",
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  You have no transaction yet
                </P>
              </View>
            ) : (
              <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 500 }}
              >
                <View>
                  {Object.keys(groupedTransactions).map((date, index) => (
                    <View key={index} style={{}}>
                      <View>
                        {groupedTransactions[date].map((item, index) => (
                          <TouchableOpacity
                            key={index}
                            onPress={() => {
                              TransactionClick(item, navigation);
                            }}
                          >
                            <View style={styles.item}>
                              <SvgXml
                                xml={
                                  item?.type?.toLowerCase() ===
                                  "internal-tranfer"
                                    ? svg.smalllogo
                                    : item?.paymentGayway === "momo"
                                    ? svg.mobile
                                    : item?.paymentGayway === "bank" ||
                                      item.provider === "link"
                                    ? svg.bankGreen
                                    : item?.type === "fund-card"
                                    ? svg.ccaddFill
                                    : item?.type === "withdraw-from-card"
                                    ? svg.withdraw
                                    : item?.type === "create-card"
                                    ? svg.cardF
                                    : item?.type === "card-debit"
                                    ? svg.bb
                                    : svg.p2p
                                }
                              />
                              <View style={{ marginLeft: 12 }}>
                                <P style={styles.transactionAmount}>
                                  <P
                                    style={{
                                      fontSize: 12,
                                      fontFamily: fonts.poppinsMedium,
                                      textDecorationLine:
                                        item.status === "failed"
                                          ? "line-through"
                                          : "none",
                                    }}
                                  >
                                    {`${
                                      item?.type === "DEPOSIT" ||
                                      item?.internalTransferSender
                                        ? "+"
                                        : "-"
                                    }$${
                                      item?.amount
                                        ? formatToTwoDecimals(item?.amount)
                                        : "..."
                                    }`}
                                  </P>{" "}
                                  {getTransactionLabel(item)}{" "}
                                  {item.type !== "sfxPoint" && (
                                    <P
                                      // @ts-ignore
                                      style={[
                                        styles.transactionDate,
                                        {
                                          color: item.status
                                            .toLowerCase()
                                            .includes("completed")
                                            ? colors.green
                                            : item.status
                                                .toLowerCase()
                                                .includes("pending") ||
                                              item.status
                                                .toLowerCase()
                                                .includes("processing") ||
                                              item.status
                                                .toLowerCase()
                                                .includes("processing")
                                            ? colors.yellow
                                            : colors.red,
                                        },
                                      ]}
                                    >
                                      {item?.status === "completed"
                                        ? "Successful"
                                        : item?.status === "processing"
                                        ? "Pending"
                                        : item?.status?.includes(
                                            "awaiting-confirmation"
                                          )
                                        ? "Pending"
                                        : capitalizeFirstLetter(item?.status)}
                                    </P>
                                  )}
                                </P>
                                <P style={styles.transactionDate}>
                                  {formatDate(item?.updatedAt)}
                                </P>
                              </View>
                              <View
                                style={{
                                  position: "absolute",
                                  right: 16,
                                  top: 16,
                                  bottom: 16,
                                  alignItems: "flex-end",
                                  justifyContent: "center",
                                }}
                              >
                                <P
                                  style={{
                                    fontSize: 12,
                                    fontFamily: fonts.poppinsRegular,
                                  }}
                                >
                                  {item.fee > 0 ? "-" : ""}$
                                  {item?.type === "DEPOSIT"
                                    ? formatNumberWithCommas(
                                        item.fee / item.exchangeRate
                                      )
                                    : formatNumberWithCommas(item.fee)}
                                </P>
                                <P
                                  // @ts-ignore
                                  style={[
                                    styles.transactionDate,
                                    {
                                      color: colors.dGray,
                                    },
                                  ]}
                                >
                                  ${formatToTwoDecimals(item?.balanceAfter)}
                                </P>
                              </View>
                            </View>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </View>
                  ))}
                </View>
              </ScrollView>
            )}
          </>
        </View>
      </Div>
    </View>
  );
}

function ActionButton({ text, svg, onPress = () => {} }) {
  return (
    <TouchableOpacity style={styles.actionButton} onPress={onPress}>
      <SvgXml xml={svg} />
      <P style={styles.actionButtonText}>{text}</P>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  amtCard: {
    width: "100%",
    height: 98,
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 20,
    alignItems: "center",
    // justifyContent: "center",
  },
  amount: {
    fontSize: 24,
    lineHeight: 36,
    color: colors.white,
    marginTop: 4,
  },
  amtCur: {
    fontSize: 12,
    lineHeight: 36,
    fontFamily: fonts.poppinsRegular,
    color: colors.white,
  },
  actionCard: {
    width: "90%",
    alignSelf: "center",
    padding: 16,
    backgroundColor: colors.white,
    marginTop: 16,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-around",
  },
  actionButton: {
    alignItems: "center",
  },
  actionButtonText: {
    marginTop: 8,
    fontSize: 12,
    color: "rgba(22, 24, 23, 1)",
  },
  datCat: {
    fontSize: 12,
  },
  item: {
    width: "100%",
    padding: 16,
    backgroundColor: colors.white,
    marginTop: 8,
    borderRadius: 12,
    flexDirection: "row",
  },
  transactionAmount: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    // fontWeight: "bold",
  },
  transactionDate: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  emptyCont: {
    width: "100%",
    height: (60 * height) / 100,
    // backgroundColor: "red",
    alignItems: "center",
    // justifyContent: "center",
  },
});
