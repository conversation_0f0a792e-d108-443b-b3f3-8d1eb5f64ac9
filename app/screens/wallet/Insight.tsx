import React, { useState } from "react";
import { StyleSheet, View, Image, Dimensions } from "react-native";
import P from "../../components/P";
import Link from "../../components/Link";
import Button from "../../components/Button";
import { fonts } from "../../config/Fonts";
import { colors } from "../../config/colors";

const baseHeight = 812;
const { width, height } = Dimensions.get("window");
export default function Insight() {
  const [isWaitListJoined, setIsWaitListJoined] = useState(false);
  const [stImg, setStImg] = useState(require("../../assets/clock.png"));
  const [stText, setStText] = useState("Sent money is pending");
  return (
    <View style={styles.body}>
      <View style={styles.itemBox}>
        <Image source={stImg} style={{ width: 64, height: 64 }} />
        <P style={styles.statusState}>Insight is coming soon</P>

        <P style={styles.stTx}>
          We’re excited to announce that our wallet insight features will be
          launching soon!
        </P>
        {/* @ts-ignore */}
        {/* {!isWaitListJoined && (
          <>
            <P style={[styles.stTx, { marginTop: (32 / baseHeight) * height }]}>
            By clicking “notify me” you agree to be notify by SFx when the insight features is ready
            </P>
            <View style={{ width: "75%", marginTop: 32 }}>
              <Button
                btnText="Notify me"
                onPress={() => {
                  setIsWaitListJoined(true);
                }}
              />
            </View>
          </>
        )} */}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    // backgroundColor: "red",
  },
  itemBox: {
    width: "100%",
    alignItems: "center",
    marginTop: "40%",
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    width: "80%",
    fontSize: 12,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
});
