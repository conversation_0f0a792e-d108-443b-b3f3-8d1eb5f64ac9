import React, { useCallback } from "react";
import { Dimensions, StyleSheet, View, BackHandler } from "react-native";
import { colors } from "../config/colors";
import Div from "../components/Div";
import AuthenticationHedear from "../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "../components/P";
import { fonts } from "../config/Fonts";
import NoteComponent from "../components/NoteComponent";
import Button from "../components/Button";
import Link from "../components/Link";
import NoteComponent2 from "../components/NoteComponent2";
import { useFocusEffect } from "@react-navigation/native";

const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function BankNoticeScreen({ navigation }) {
  useFocusEffect(useCallback(() => {
    const onBackPress = () => {
      // Reset navigation stack to avoid loading issues
      // @ts-ignore
      navigation.reset({
        index: 0,
        routes: [{ name: 'BottomTabNavigator' }],
      });
      return true;
    };
    // Disable iOS swipe back gesture
    navigation.setOptions({
      gestureEnabled: false
    });
    // Handle Android back button
    BackHandler.addEventListener("hardwareBackPress", onBackPress);

    return () => {
      BackHandler.removeEventListener("hardwareBackPress", onBackPress);
    };
  }, [navigation]))
  return (
    <View style={styles.body}>
      <Div>
        {/* <AuthenticationHedear text="Bank notice" navigation={navigation} /> */}
        <View style={styles.itemCont}>
          <SvgXml xml={svg.rAlertCircle} />
          <P
            style={{
              marginTop: (24 / baseHeight) * height,
              marginBottom: 4,
              fontSize: 16,
              fontFamily: fonts.poppinsMedium,
              paddingLeft: 16,
              paddingRight: 16,
              textAlign: "center",
            }}
          >
            Please only send money from an account that match your SFx account
            name
          </P>
          <P
            style={{
              fontSize: 12,
              color: colors.gray,
              textAlign: "center",
              fontFamily: fonts.poppinsRegular,
            }}
          >
            For your security, to avoid delays and ensure a smooth transaction,
            please ensure the account name matches your SFx account when adding
            money.
          </P>
        </View>
        <View style={styles.bottomItem}>
          <View
            style={{
              width: (264 / baseWidth) * width,
              marginBottom: (16 / baseHeight) * height,
            }}
          >
            <Button btnText="Continue" onPress={() => navigation.pop()} />
          </View>
          <Link
            style={{ fontSize: 12 }}
            onPress={() => navigation.navigate("Home")}
          >
            Go back home
          </Link>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
  itemCont: {
    width: "80%",
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "center",
    marginTop: (145 / baseHeight) * height,
  },
  bottomItem: {
    width: "90%",
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "center",
    marginTop: (32 / baseHeight) * height,
  },
});
