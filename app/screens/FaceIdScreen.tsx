import React, { useState, useEffect, useContext } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Linking,
  Platform,
} from "react-native";
import { colors } from "../config/colors";
import Div from "../components/Div";
import AuthenticationHedear from "../components/AuthenticationHedear";
import P from "../components/P";
import H4 from "../components/H4";
import { fonts } from "../config/Fonts";
import Button from "../components/Button";
import Input from "../components/Input";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import * as Clipboard from "expo-clipboard";
import Link from "../components/Link";
import NoteComponent2 from "../components/NoteComponent2";
import CountrySelect from "../components/CountrySelect";
import CountryCodSelect from "../components/CountryCodSelect";
import { countries } from "../components/counties";
import { CameraView } from "expo-camera";
// import SmileIDSmartSelfieEnrollmentView from './SmileIDSmartSelfieEnrollmentView';
import {
  SmileIDDocumentCaptureView,
  SmileIDSmartSelfieCaptureView,
} from "@smile_identity/react-native";
import { GetUserDetails, VerifyDoc } from "../RequestHandlers/User";
import Loader from "../components/ActivityIndicator";
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";

import RNFS from "react-native-fs";
import { Image } from "react-native-compressor";
import { CredentailsContext } from "../RequestHandlers/CredentailsContext";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { VerifyBvn } from "../RequestHandlers/User";
import VLoader from "../components/VLoader";
import { manipulateAsync, FlipType, SaveFormat } from "expo-image-manipulator";
import { useToast } from "../context/ToastContext";
import Constants from "expo-constants";

const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
const STAGING_BASE_URL = Constants.expoConfig.extra.STAGING_BASE_URL;
const PROD_BASE_URL = Constants.expoConfig.extra.PROD_BASE_URL;
export default function FaceIdScreen({ navigation, route }) {
  const [uniqueId, setuniqueId] = useState("");
  const { data } = route?.params || {};
  const [step, setStep] = useState(1);
  const [loader, setLoader] = useState(false);
  const { storedCredentails } = useContext(CredentailsContext);
  const [tkn, setTkn] = useState("");
  const [kycFailed, setKyCFialed] = useState(false);
  const [countdown, setCountdown] = useState(10);
  const [isCounting, setIsCounting] = useState(false);
  const [isProd, setIsProd] = useState(true);
  const handleNextStep = () => {
    setStep((prevStep) => prevStep + 1);
  };
  const { handleToast } = useToast();
  // const [isAccVerified, setAccVerified] = useState(false);
  const [base64Image, setBase64Image] = useState<
    { image: string; image_type_id: number | null }[]
  >([]);

  useEffect(() => {
    async () => {
      const res = await AsyncStorage.getItem("uniqueID");
      setuniqueId(res);
    };
  }, []);

  useEffect(() => {
    if (isCounting && countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else if (countdown === 0) {
      navigation.navigate("BottomTabNavigator");
    }
  }, [isCounting, countdown, navigation]);

  const convertToBase64 = async (filePath: string, id: number) => {
    // console.log("File path:", filePath);
    try {
      const manipResult = await manipulateAsync(filePath, [], {
        compress: 0.2,
        format: SaveFormat.WEBP,
        base64: true,
      });
      const result = await Image.compress(filePath, {
        compressionMethod: "manual",
        maxWidth: 500,
        quality: 0.1,
      });
      const base64 = await RNFS.readFile(result, "base64");
      const base64String = `${manipResult.base64}`;
      if (base64String) {
      }
      const newImageObject = {
        image: String(base64String),
        image_type_id: id,
      };
      setBase64Image((prev) => [...prev, newImageObject]);
    } catch (error) {
      console.error("Error converting file to Base64:", error);
    }
  };

  const checkStatus = async () => {
    try {
      const res = await GetUserDetails();
      if (res.verified == "true") {
        navigation.navigate("AllSetScreen");
      } else if (res.verified == "pending") {
        handleToast("Verification is pending", "success");
        navigation.navigate("BottomTabNavigator");
      } else {
        setKyCFialed(true);
      }
    } catch (error) {
    } finally {
      setLoader(false);
    }
  };

  const tier = 1;
  const idType = "PASSPORT";
  const baseUrl = isProd ? PROD_BASE_URL : STAGING_BASE_URL;

  const verifyBVN = async () => {
    setLoader(true);
    const myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    myHeaders.append("Authorization", `Bearer ${tkn}`);
    myHeaders.append("x-device-id", uniqueId || "");
    const raw = JSON.stringify({
      id_number: data.idNum,
      dob: data.dob,
      phone_number: data.phone,
      detail: {
        partner_params: {
          libraryVersion: "1.0.2",
          permissionGranted: true,
        },
        images: base64Image,
      },
    });
    const requestSize = new Blob([raw]).size;
    const requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: raw,
      redirect: "follow",
    };
    // @ts-ignore
    fetch(`${baseUrl}kyc/verify-bvn`, requestOptions)
      .then((response) => {
        if (response.status === 200) {
          checkStatus();
        }
        return response.json();
      })
      .then((result) => {
        if (result.status === true) {
          checkStatus();
        } else {
          handleToast(result.message, "error");
          setKyCFialed(true);
          setIsCounting(true);
          setLoader(false);
        }
      })
      .catch((error) => {
        handleToast(error.message, "error");
        setKyCFialed(true);
        setLoader(false);
      });
  };

  const verifyPassport = async () => {
    const myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    myHeaders.append("Authorization", `Bearer ${tkn}`);
    myHeaders.append("x-device-id", uniqueId || "");
    const raw = JSON.stringify({
      detail: {
        images: base64Image,
        partner_params: {
          libraryVersion: "1.0.2",
          permissionGranted: true,
        },
      },
      id_info: {
        country: data.alphaCode,
        id_type: idType,
      },
      tier: tier,
      document_id: data.idNum,
      dob: data.dob,
      phone_number: data.phone,
    });
    const requestSize = new Blob([raw]).size;
    const requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: raw,
      redirect: "follow",
    };
    // @ts-ignore
    fetch(`${baseUrl}kyc/verify-docs`, requestOptions)
      .then((response) => {
        if (response.status === 200) {
          checkStatus();
        }
        return response.json();
      })
      .then((result) => {
        if (result.status === true) {
          checkStatus();
        } else {
          handleToast(result.message, "error");
          setKyCFialed(true);
          setIsCounting(true);
          setLoader(false);
        }
      })
      .catch((error) => {
        handleToast(error.message, "error");
        setKyCFialed(true);
        setLoader(false);
      });
  };
  useEffect(() => {
    if (data.idMethod === "BVN") {
      if (base64Image.length >= 8) {
        verifyBVN();
      }
    } else {
      if (base64Image.length >= 10) {
        verifyPassport();
      }
    }
  }, [base64Image.length >= 10, base64Image.length >= 8]);

  useEffect(() => {
    // @ts-ignore
    if (
      storedCredentails != undefined &&
      storedCredentails != null &&
      storedCredentails != ""
    ) {
      if (typeof storedCredentails === "string") {
        const newRes = JSON.parse(storedCredentails);
        setTkn(newRes.token);
      } else {
        const newRes = storedCredentails;
        // @ts-ignore
        setTkn(newRes.token);
        // @ts-ignore
      }
    }
  }, []);

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Account verification"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentCard}>
            <View style={styles.section1Wrap}>
              <View style={styles.section3Wrap}>
                <View
                  style={{
                    width: "100%",
                    height: (75 * height) / 100,
                    // backgroundColor: "red",
                  }}
                >
                  {kycFailed ? (
                    <>
                      <View
                        style={{
                          width: "100%",
                          // backgroundColor: "red",
                          alignItems: "center",
                          marginTop: (10 * height) / 100,
                        }}
                      >
                        <SvgXml width={80} height={80} xml={svg.idFailed} />
                        <P style={styles.statusState}>Account verification</P>
                        <P style={styles.stTx}>
                          Your account identity verification Failed{"\n"}
                          confirm details and try again
                        </P>
                        <View style={{ width: "75%", marginTop: 32 }}>
                          <P
                            style={{
                              alignSelf: "center",
                              color: colors.primary,
                            }}
                          >
                            {" "}
                            {countdown > 0
                              ? `You will be redirected : ${countdown}s`
                              : "Redirecting..."}
                          </P>
                          {/* <Button
                            btnText="Okay!"
                            onPress={() => {
                              navigation.pop();
                            }}
                          /> */}
                        </View>
                      </View>
                    </>
                  ) : (
                    <>
                      {data.idMethod === "BVN" ? (
                        <>
                          <SmileIDSmartSelfieCaptureView
                            style={{ width: "100%", height: "100%" }}
                            allowAgentMode={false}
                            showInstructions={true}
                            showAttribution={true}
                            showConfirmation={true}
                            onResult={(event) => {
                              try {
                                const result = JSON.parse(
                                  event.nativeEvent.result
                                );
                                setLoader(true);
                                if (result.selfieFile) {
                                  convertToBase64(result.selfieFile, 2);
                                } else {
                                  console.error(
                                    "No selfie file found in the result."
                                  );
                                }
                                if (
                                  result.livenessFiles &&
                                  Array.isArray(result.livenessFiles)
                                ) {
                                  result.livenessFiles.forEach((item) => {
                                    if (item) {
                                      convertToBase64(item, 6);
                                    } else {
                                      console.error(
                                        "Invalid liveness file path:",
                                        item
                                      );
                                    }
                                  });
                                } else {
                                  console.error(
                                    "No valid liveness files found."
                                  );
                                }
                              } catch (error) {
                                console.error("Error parsing result:", error);
                              }
                            }}
                          />
                        </>
                      ) : (
                        <>
                          {step === 1 && (
                            <SmileIDDocumentCaptureView
                              style={{ width: "100%", height: "100%" }}
                              // @ts-ignore
                              isDocumentFrontSide={true}
                              showInstructions={true}
                              showAttribution={true}
                              showConfirmation={true}
                              allowGalleryUpload={false}
                              onResult={(event) => {
                                if (event.nativeEvent.error) {
                                  console.error(
                                    "Front document error:",
                                    event.nativeEvent.error
                                  );
                                  return;
                                }
                                convertToBase64(
                                  Platform.OS === "ios"
                                    ? JSON.parse(event.nativeEvent.result)
                                        .documentFrontImage
                                    : JSON.parse(event.nativeEvent.result)
                                        .documentFrontFile,
                                  3
                                );
                                handleNextStep();
                              }}
                            />
                          )}
                          {step === 2 && (
                            <SmileIDDocumentCaptureView
                              style={{ width: "100%", height: "100%" }}
                              // @ts-ignore
                              isDocumentFrontSide={false}
                              isDocumentBackSide={true}
                              showInstructions={true}
                              showAttribution={true}
                              showConfirmation={true}
                              allowGalleryUpload={false}
                              onResult={(event) => {
                                if (event.nativeEvent.error) {
                                  console.error(
                                    "Back document error:",
                                    event.nativeEvent.error
                                  );
                                  return;
                                }
                                const mainDoc =
                                  Platform.OS === "ios"
                                    ? JSON.parse(event.nativeEvent.result)
                                        .documentBackImage
                                    : JSON.parse(event.nativeEvent.result)
                                        .documentBackFile;
                                convertToBase64(mainDoc, 7);
                                handleNextStep();
                              }}
                            />
                          )}
                          {step === 3 && (
                            <SmileIDSmartSelfieCaptureView
                              style={{ width: "100%", height: "100%" }}
                              allowAgentMode={false}
                              showInstructions={true}
                              showAttribution={true}
                              showConfirmation={true}
                              onResult={(event) => {
                                try {
                                  const result = JSON.parse(
                                    event.nativeEvent.result
                                  );
                                  setLoader(true);
                                  if (result.selfieFile) {
                                    convertToBase64(result.selfieFile, 2);
                                  } else {
                                    console.error(
                                      "No selfie file found in the result."
                                    );
                                  }
                                  if (
                                    result.livenessFiles &&
                                    Array.isArray(result.livenessFiles)
                                  ) {
                                    result.livenessFiles.forEach((item) => {
                                      if (item) {
                                        convertToBase64(item, 6);
                                      } else {
                                        console.error(
                                          "Invalid liveness file path:",
                                          item
                                        );
                                      }
                                    });
                                  } else {
                                    console.error(
                                      "No valid liveness files found."
                                    );
                                  }
                                } catch (error) {
                                  console.error("Error parsing result:", error);
                                }
                              }}
                            />
                          )}
                        </>
                      )}
                    </>
                  )}
                </View>
              </View>
            </View>
          </View>

          {/* <P
            style={{
              alignSelf: "center",
              textAlign: "center",
              marginTop: (16 / baseHeight) * height,
              fontSize: 12,
              lineHeight: (18 / baseHeight) * height,
              width: "90%",
              color: colors.gray,
              fontFamily: fonts.poppinsRegular,
            }}
          >
            This process ensures that your account remains safe and secure.
          </P> */}
          {/* <View style={styles.btnCont}>
            <Button btnText={"Continue"} onPress={() => {}} />
          </View> */}
        </ScrollView>
      </Div>
      {loader && <VLoader navigation={navigation} />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentCard: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    // marginTop: 24,
    marginTop: (2.7 * height) / 100,
    // paddingTop: (24 / baseHeight) * height,
    paddingBottom: (24 / baseHeight) * height,
    paddingLeft: (16 / baseWidth) * width,
    paddingRight: (16 / baseWidth) * width,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  holder: {
    fontSize: 12,
    lineHeight: (18 / baseHeight) * height,
    color: colors.gray,
    marginBottom: (4 / baseHeight) * height,
  },
  value: {
    fontSize: 12,
    lineHeight: (18 / baseHeight) * height,
    color: colors.black,
  },
  copyBtn: {
    paddingTop: (4 / baseHeight) * height,
    paddingBottom: (4 / baseHeight) * height,
    padding: (13 / baseWidth) * width,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: (32 / baseHeight) * height,
    marginBottom: (16 / baseHeight) * height,
  },
  amt: {
    fontSize: 16,
    // fontSize: 32,
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  amtCur: {
    lineHeight: (24 / baseHeight) * height,
    fontFamily: fonts.poppinsMedium,
  },
  statusText: {
    fontSize: 10,
    lineHeight: 16,
    fontFamily: fonts.poppinsRegular,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginTop: (24 / baseHeight) * height,
    // paddingTop: (2.7 * height) / 100,
    borderTopWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  section3Wrap: {
    width: "100%",
    marginTop: (24 / baseHeight) * height,
    borderColor: colors.stroke,
    // paddingLeft: 16,
  },
  progressDesCont: {
    flexDirection: "row",
  },
  progTextHead: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  progTextBody: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: (32 / baseHeight) * height,
    marginBottom: (64 / baseHeight) * height,
    // marginTop: 42,
    // alignItems: 'center',
    // justifyContent: 'center',
  },
  pinInput: {
    width: "40%",
    height: "100%",
    alignItems: "center",
    flexDirection: "row",
    borderRightColor: "#E6E5E5",
    borderRightWidth: 1,
  },
  pinTextInput: {
    fontSize: 14,
    textAlign: "center",
    color: "#161817",
    fontFamily: fonts.poppinsMedium,
    marginLeft: 16,
    marginRight: 8,
  },
  con: {
    flexDirection: "row",
    justifyContent: "center",
    width: "100%",
    // backgroundColor:"red",
    height: 240,
    marginBottom: 32,
    // borderRadius:1000
    // borderColor:
  },
  cameraCont: {
    width: (240 / baseWidth) * width,
    height: (240 / baseWidth) * width,
    borderRadius: 200,
    overflow: "hidden",
    borderWidth: 5,
    borderColor: colors.primary,
  },
  camera: {
    width: "100%",
    height: "100%",
    // borderRadius: width / 2,
    // overflow:'hidden',
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    fontSize: 12,
    lineHeight: 19.2,
    // paddingLeft: 50,
    // paddingRight: 50,
    // backgroundColor: 'red',
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
});
