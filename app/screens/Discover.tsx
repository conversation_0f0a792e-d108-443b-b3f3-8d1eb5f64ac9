import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import P from "../components/P";
import Link from "../components/Link";
import Button from "../components/Button";
import { fonts } from "../config/Fonts";
import { colors } from "../config/colors";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import { TextInput } from "react-native-gesture-handler";
import H4 from "../components/H4";

const baseHeight = 812;
const { width, height } = Dimensions.get("window");
export default function Discover() {
  const [searchQuery, setSearchQuery] = useState<string>("");
  const tabs = [
    { id: 1, name: "All pharmacy" },
    { id: 2, name: "Nearby pharmacy" },
  ];
  const [activeTab, setActiveTab] = useState(1);
  const [activePharmacy, setActivePharmacy] = useState(null);
  const pharmacies = [
    {
      id: 1,
      name: "Buyukada Pharmacy",
      distance: 1.62,
      phone: "02163815181",
      address:
        "Burgazada Burgazadasi Neighborhood Cinarlik street 3 Burgazada Islands",
      operation: "Open",
      time: "24 hours",
    },
    {
      id: 2,
      name: "MEVA ECZANESI",
      distance: 1.62,
      phone: "02163815181",
      address:
        "Burgazada Burgazadasi Neighborhood Cinarlik street 3 Burgazada Islands",
      operation: "Closed",
      time: "Opens 10 am",
    },
    {
      id: 3,
      name: "BURCU GOKALP ECZANESI",
      distance: 1.62,
      phone: "02163815181",
      address:
        "Burgazada Burgazadasi Neighborhood Cinarlik street 3 Burgazada Islands",
      operation: "Open",
      time: "24 hours",
    },
    {
      id: 4,
      name: "MEVA ECZANESI",
      distance: 1.62,
      phone: "02163815181",
      address:
        "Burgazada Burgazadasi Neighborhood Cinarlik street 3 Burgazada Islands",
      operation: "Closed",
      time: "Opens 10 am",
    },
    {
      id: 5,
      name: "Buyukada Pharmacy",
      distance: 1.62,
      phone: "02163815181",
      address:
        "Burgazada Burgazadasi Neighborhood Cinarlik street 3 Burgazada Islands",
      operation: "Open",
      time: "24 hours",
    },
  ];
  return (
    <View style={styles.body}>
      <View style={styles.customInput}>
        <SvgXml xml={svg.search} style={{ marginRight: 8 }} />
        <TextInput
          style={styles.input}
          placeholder="Search"
          cursorColor={colors.black}
          value={searchQuery}
          onChangeText={(text) => setSearchQuery(text)}
        />
      </View>
      <View style={styles.tabCont}>
        {tabs.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[
              styles.tab,
              {
                backgroundColor:
                  activeTab === item.id ? colors.white : "transparent",
              },
            ]}
            onPress={() => {
              setActiveTab(item.id);
            }}
          >
            <P
              style={{
                fontSize: 12,
                color: activeTab === item.id ? colors.primary : colors.dGray,
              }}
            >
              {item.name}
            </P>
          </TouchableOpacity>
        ))}
      </View>
      {pharmacies.length === 0 ? (
        <>
          <View style={styles.emptyCont}>
            <SvgXml xml={svg.largeStoreLocation} />
            <P style={{ marginTop: 16, textAlign: "center" }}>
              {activeTab === 1 ? "No pharmacy" : "No nearby pharmacy!"}{" "}
            </P>
            <P style={styles.emptySubtext}>
              {" "}
              All {activeTab === 1 ? "" : "nearby"} pharmacies will{"\n"}appear
              here
            </P>
          </View>
        </>
      ) : (
        <ScrollView
          style={{ width: "100%" }}
          contentContainerStyle={styles.contentCont}
          showsVerticalScrollIndicator={false}
        >
          {pharmacies.map((item) => (
            <TouchableOpacity
              onPress={() => {
                if (activePharmacy === item.id) {
                  setActivePharmacy(null);
                } else {
                  setActivePharmacy(item.id);
                }
              }}
              key={item.id}
              style={[styles.pharmacyItem]}
            >
              <View style={{ width: "100%", paddingHorizontal: 16 }}>
                {/* name */}
                <View style={styles.nameHolder}>
                  <P style={{ color: "#161817" }}>{item.name}</P>
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      gap: 4,
                    }}
                  >
                    <SvgXml xml={svg.location} />
                    <P style={styles.subText}>{item.distance} km</P>
                  </View>
                </View>
                <View style={{ marginTop: 8, gap: 16, flexDirection: "row" }}>
                  {/* contact number */}
                  <View
                    style={{
                      gap: 4,
                      flexDirection: "row",
                      alignItems: "center",
                    }}
                  >
                    <SvgXml xml={svg.call} />
                    <P style={styles.subText}>{item.phone}</P>
                  </View>
                  {/* opration time */}
                  <View
                    style={{
                      gap: 4,
                      flexDirection: "row",
                      alignItems: "center",
                    }}
                  >
                    <SvgXml xml={svg.time} />
                    <P
                      // @ts-ignore
                      style={[
                        styles.subText,
                        {
                          color:
                            item.operation.toLowerCase() == "open"
                              ? colors.green
                              : colors.red,
                        },
                      ]}
                    >
                      {item.operation}{" "}
                      <P
                        // @ts-ignore
                        style={[
                          styles.subText,
                          {
                            color:
                              item.operation.toLowerCase() == "open"
                                ? colors.green
                                : colors.dGray,
                          },
                        ]}
                      >
                        {item.time}
                      </P>
                    </P>
                  </View>
                </View>

                {/* address */}
                <View style={styles.address}>
                  <SvgXml xml={svg.storeLocation} />
                  <P
                    style={styles.subText}
                    numberOfLines={activePharmacy === item.id ? undefined : 1}
                  >
                    {item.address}
                  </P>
                </View>
              </View>

              {/* extra-section */}
              {activePharmacy === item.id ? (
                <>
                  <View style={styles.extraItem}>
                    <TouchableOpacity style={styles.button}>
                      <SvgXml xml={svg.googleMap} />
                      <P style={styles.btnText}>Direction</P>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.button}>
                      <SvgXml xml={svg.outlinShare} />
                      <P style={styles.btnText}>Share</P>
                    </TouchableOpacity>
                  </View>
                </>
              ) : (
                <></>
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    // backgroundColor: "red",
  },
  itemBox: {
    width: "100%",
    alignItems: "center",
    marginTop: "40%",
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    width: "80%",
    fontSize: 12,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
  customInput: {
    width: "90%",
    height: 44,
    backgroundColor: colors.white,
    alignSelf: "center",
    borderRadius: 12,
    marginTop: 10,
    paddingLeft: 16,
    paddingRight: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  input: {
    width: "80%",
    height: 44,
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
  },
  tabCont: {
    width: "90%",
    alignSelf: "center",
    marginTop: 24,
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    marginBottom: 16,
  },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 5,
    borderRadius: 100,
  },
  contentCont: {
    width: "90%",
    alignSelf: "center",
    alignItems: "center",
    paddingBottom: 150,
  },
  pharmacyItem: {
    width: "100%",
    minHeight: 105,
    padding: 16,
    paddingHorizontal: 0,
    backgroundColor: colors.white,
    marginBottom: 16,
    borderRadius: 8,
  },
  nameHolder: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  subText: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.dGray,
  },
  address: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    marginTop: 8,
  },
  extraItem: {
    width: "100%",
    borderTopWidth: 1,
    borderColor: "#F7F4FF",
    marginTop: 16,
    paddingHorizontal: 16,
    paddingTop: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  button: {
    width: "45%",
    height: 32,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: colors.stroke,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
    gap: 4,
  },
  btnText: {
    fontSize: 12,
    color: "#161817",
  },
  emptySubtext: {
    textAlign: "center",
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
    color: colors.grayText,
  },
  emptyCont: {
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    height: 400,
  },
});
