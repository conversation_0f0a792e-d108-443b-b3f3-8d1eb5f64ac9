import React, { use<PERSON><PERSON>back, useContext, useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../../config/Fonts";
import Div from "../../../components/Div";
import AuthenticationHedear from "../../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";
import P from "../../../components/P";
import MicroBtn from "../../../components/MicroBtn";
import { colors } from "../../../config/colors";
import DetailCard from "../../../components/DetailCard";
import Button from "../../../components/Button";
import Content from "../../../components/Content";
import Content2 from "../../../components/Content2";
import Input from "../../../components/Input";
import { Switch } from "react-native-gesture-handler";
import CustomSwitch from "../../../components/CustomSwitch";
import BarCodeScanner from "../../../components/BarCodeScanner";
import { SendSFXMoneyApp } from "../../../RequestHandlers/Wallet";
import {
  AddBeneficiary,
  GetBeneficiaries,
  GetUserByName,
} from "../../../RequestHandlers/User";
import ContentLoader, { Rect } from "react-content-loader/native";
import Loader from "../../../components/ActivityIndicator";
import CustomSwitch1 from "../../../components/CustomSwitch1";
import { useFocusEffect } from "@react-navigation/native";
import { ensureHttps } from "../../../components/AddHttp";
import { useToast } from "../../../context/ToastContext";
import { withApiErrorToast } from "../../../Utils/withApiErrorToast";
import { CredentailsContext } from "../../../RequestHandlers/CredentailsContext";
import NoteComponent2 from "../../../components/NoteComponent2";

const { width, height } = Dimensions.get("window");

// Turkish IBAN validation function
const validateTurkishIBAN = (iban: string): { isValid: boolean; error?: string } => {
  // Remove spaces and convert to uppercase
  const cleanIban = iban.replace(/\s/g, '').toUpperCase();

  // Check if IBAN starts with TR (Turkey country code)
  if (!cleanIban.startsWith('TR')) {
    return { isValid: false, error: 'IBAN must start with TR + 24 digitd' };
  }

  // Turkish IBAN format: TR + 2 check digits + 5 bank code + 1 reserved + 16 account number = 26 characters total
  if (cleanIban.length !== 26) {
    return { isValid: false, error: 'Turkish IBAN must be exactly 26 characters long' };
  }

  // Check if all characters after TR are numeric
  const numericPart = cleanIban.substring(2);
  if (!/^\d{24}$/.test(numericPart)) {
    return { isValid: false, error: 'IBAN must contain only numbers after TR prefix' };
  }

  // IBAN checksum validation using mod-97 algorithm
  const rearranged = cleanIban.substring(4) + cleanIban.substring(0, 4);

  // Convert letters to numbers (A=10, B=11, ..., Z=35)
  let numericString = '';
  for (let i = 0; i < rearranged.length; i++) {
    const char = rearranged[i];
    if (/[A-Z]/.test(char)) {
      numericString += (char.charCodeAt(0) - 55).toString();
    } else {
      numericString += char;
    }
  }

  // Calculate mod 97
  let remainder = 0;
  for (let i = 0; i < numericString.length; i++) {
    remainder = (remainder * 10 + parseInt(numericString[i])) % 97;
  }

  if (remainder !== 1) {
    return { isValid: false, error: 'Invalid IBAN checksum' };
  }

  return { isValid: true };
};

// Format IBAN with spaces for display
const formatIBANDisplay = (iban: string): string => {
  const cleanIban = iban.replace(/\s/g, '').toUpperCase();
  return cleanIban.replace(/(.{4})/g, '$1 ').trim();
};

// UBAN validation function (Turkish Unified Bank Account Number)
const validateTurkishUBAN = (uban: string): { isValid: boolean; error?: string } => {
  // Remove spaces and convert to uppercase
  const cleanUban = uban.replace(/\s/g, '').toUpperCase();

  // Check if UBAN starts with CT (Country code for UBAN)
  if (!cleanUban.startsWith('CT')) {
    return { isValid: false, error: 'UBAN must start with CT + 26 digits' };
  }

  // Turkish UBAN format: CT + 26 digits = 28 characters total
  if (cleanUban.length !== 28) {
    return { isValid: false, error: 'Turkish UBAN must be exactly 28 characters long' };
  }

  // Check if all characters after CT are numeric
  const numericPart = cleanUban.substring(2);
  if (!/^\d{26}$/.test(numericPart)) {
    return { isValid: false, error: 'UBAN must contain only numbers after CT prefix' };
  }

  return { isValid: true };
};

// Format UBAN with spaces for display
const formatUBANDisplay = (uban: string): string => {
  const cleanUban = uban.replace(/\s/g, '').toUpperCase();
  return cleanUban.replace(/(.{4})/g, '$1 ').trim();
};

export default function OTCAccountDetailScreen({ navigation, route }) {
  const [isEnabled, setIsEnabled] = useState(false);
  const { selectedMethod, isAddMoney } = route?.params || {};
  const { handleToast } = useToast();
  const [showQrCode, setShowQrCode] = useState(false);
  const [bene, setBene] = useState(false);
  const [accountNumberError, setAccountNumberError] = useState("");
  const [bankNameError, setBankNameError] = useState("");
  const [accountNameError, setAccountNameError] = useState("");
  const [ibanError, setIbanError] = useState("");
  const [ubanError, setUbanError] = useState("");
  const [loadng, setLoading] = useState(false);
  const [accountNumber, setAccountNumber] = useState("");
  const [bankName, setBankName] = useState("");
  const [accountName, setAccountName] = useState("");
  const [note, setNote] = useState("");
  const [details, setdetails] = useState<any>([]);
  const { data } = route?.params || "";
  const [isKycDone, setIsKycDone] = useState("false");
  const [loader, setLoader] = useState(false);
  const [ld, setLd] = useState(false);
  const { storedCredentails } = useContext(CredentailsContext);
  const [selectedAccountOption, setSelectedAccountOption] = useState("UBAN");
  const accountOption = ["UBAN", "IBAN", "Account number"];

  const isEmpty = (string: string) => {
    if (
      string == "" ||
      string == " " ||
      string == null ||
      string == undefined
    ) {
      return false;
    } else {
      return true;
    }
  };
  const handleAccountNumberChange = (text: string) => {
    setAccountNumber(text);
    setAccountNumberError(""); // Clear field-specific error
    setIbanError(""); // Clear IBAN error
    setUbanError(""); // Clear UBAN error

    // Validate based on selected account option
    if (text.trim() !== "") {
      if (selectedAccountOption === "IBAN") {
        const validation = validateTurkishIBAN(text);
        if (!validation.isValid) {
          setIbanError(validation.error || "Invalid IBAN");
        }
      } else if (selectedAccountOption === "UBAN") {
        const validation = validateTurkishUBAN(text);
        if (!validation.isValid) {
          setUbanError(validation.error || "Invalid UBAN");
        }
      }
    }
  };

  const handleBankNameChange = (text: string) => {
    setBankName(text);
    setBankNameError(""); // Clear field-specific error
  };

  const handleAccountNameChange = (text: string) => {
    setAccountName(text);
    setAccountNameError(""); // Clear field-specific error
  };

  const handleScan = (scannedData: string) => {
    setAccountNumber(scannedData);
  };

  // Handle account type selection change
  const handleAccountTypeChange = (selectedType: string) => {
    // Clear all form data when account type changes
    setAccountNumber("");
    setBankName("");
    setAccountName("");

    // Clear all error states
    setAccountNumberError("");
    setBankNameError("");
    setAccountNameError("");
    setIbanError("");
    setUbanError("");

    // Update selected account option
    setSelectedAccountOption(selectedType);
  };

  // Validation function
  const validateForm = () => {
    let isValid = true;

    // Reset all errors
    setAccountNumberError("");
    setBankNameError("");
    setAccountNameError("");
    setIbanError("");
    setUbanError("");

    // Validate account number
    if (!accountNumber.trim()) {
      setAccountNumberError(`${selectedAccountOption} is required`);
      isValid = false;
    } else if (selectedAccountOption === "IBAN") {
      // Special validation for IBAN
      const validation = validateTurkishIBAN(accountNumber);
      if (!validation.isValid) {
        setIbanError(validation.error || "Invalid IBAN");
        isValid = false;
      }
    } else if (selectedAccountOption === "UBAN") {
      // Special validation for UBAN
      const validation = validateTurkishUBAN(accountNumber);
      if (!validation.isValid) {
        setUbanError(validation.error || "Invalid UBAN");
        isValid = false;
      }
    } else if (accountNumber.trim().length < 6) {
      setAccountNumberError(
        `${selectedAccountOption} must be at least 6 characters`
      );
      isValid = false;
    }

    // Validate bank name
    if (!bankName.trim()) {
      setBankNameError("Bank name is required");
      isValid = false;
    } else if (bankName.trim().length < 2) {
      setBankNameError("Bank name must be at least 2 characters");
      isValid = false;
    }

    // Validate account name
    if (!accountName.trim()) {
      setAccountNameError("Account name is required");
      isValid = false;
    } else if (accountName.trim().length < 2) {
      setAccountNameError("Account name must be at least 2 characters");
      isValid = false;
    }

    return isValid;
  };

  // Handle navigation to OTC Transaction Screen
  const handleNext = () => {
    if (validateForm()) {
      setLd(true);

      // Prepare bank details data
      const bankDetails = {
        accountType: selectedAccountOption,
        accountNumber: accountNumber.trim(),
        bankName: bankName.trim(),
        accountName: accountName.trim(),
      };

      // Navigate to OTC Transaction Screen with all required data
      setTimeout(() => {
        setLd(false);
        navigation.navigate("OTCTransactionScreen", {
          selectedMethod,
          isAddMoney: isAddMoney || false,
          bankDetails,
          // Pass any additional data from route params
          ...route.params,
        });
      }, 500);
    } else {
      handleToast("Please fill all the details", "error");
    }
  };

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Bank details" navigation={navigation} />
        <View>
          <ScrollView automaticallyAdjustKeyboardInsets={true}>
            <View style={{ width: "90%", alignSelf: "center" }}>
              <NoteComponent2
                type="red"
                contStyle={{ backgroundColor: colors.white, marginTop: 16 }}
                text={
                  "Please fill in the bank details of the account you want to transfer funds to using the OTC Deck.This helps us process your request accurately and without delays."
                }
              />
            </View>
            <View style={styles.contentBody}>
              <View style={styles.detailWrap}>
                <View style={{ width: "100%" }}>
                  <P style={[styles.label, { marginBottom: 6 }]}>
                    Bank Details
                  </P>
                  {accountOption.map((item, index) => (
                    <TouchableOpacity
                      key={index}
                      onPress={() => handleAccountTypeChange(item)}
                      style={{
                        width: "100%",
                        height: 44,
                        borderWidth: 1,
                        borderColor:
                          selectedAccountOption === item
                            ? colors.primary
                            : colors.stroke,
                        borderRadius: 8,
                        marginBottom: 16,
                        justifyContent: "space-between",
                        flexDirection: "row",
                        alignItems: "center",
                        paddingLeft: 16,
                        paddingRight: 16,
                      }}
                    >
                      <P style={styles.label}>{item}</P>
                      {selectedAccountOption === item ? (
                        <SvgXml xml={svg.purple_check} />
                      ) : (
                        <View
                          style={{
                            width: 16,
                            height: 16,
                            borderRadius: 100,
                            borderWidth: 1,
                            borderColor: colors.stroke,
                          }}
                        ></View>
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
                <Input
                  label={selectedAccountOption}
                  placeholder={
                    selectedAccountOption === "IBAN"
                      ? `Enter ${selectedAccountOption} number (TR + 24 digits)`
                      : selectedAccountOption === "UBAN"
                      ? `Enter ${selectedAccountOption} number (CT + 26 digits)`
                      : `Enter ${selectedAccountOption.toLowerCase()}`
                  }
                  inputStyle={{ width: "85%" }}
                  // contStyle={{ marginBottom: 16 }}
                  value={accountNumber}
                  error={!!accountNumberError || !!ibanError || !!ubanError}
                  onChangeText={handleAccountNumberChange}
                  keyboardType={
                    selectedAccountOption === "Account number"
                      ? "numeric"
                      : "default"
                  }
                />
                {accountNumberError ? (
                  <P style={styles.errorText}>{accountNumberError}</P>
                ) : null}
                {ibanError ? (
                  <P style={styles.errorText}>{ibanError}</P>
                ) : null}
                {ubanError ? (
                  <P style={styles.errorText}>{ubanError}</P>
                ) : null}

                <Input
                  label="Bank name"
                  placeholder="Enter bank name"
                  inputStyle={{ width: "85%" }}
                  contStyle={{ marginTop: 16 }}
                  value={bankName}
                  error={!!bankNameError}
                  onChangeText={handleBankNameChange}
                />
                {bankNameError ? (
                  <P style={styles.errorText}>{bankNameError}</P>
                ) : null}
                <Input
                  label="Account name"
                  placeholder="Enter account name"
                  inputStyle={{ width: "85%" }}
                  contStyle={{ marginTop: 16 }}
                  value={accountName}
                  error={!!accountNameError}
                  onChangeText={handleAccountNameChange}
                />
                {accountNameError ? (
                  <P style={styles.errorText}>{accountNameError}</P>
                ) : null}
              </View>
              <View style={{ width: "80%", marginTop: 32 }}>
                <Button btnText="Next" loading={ld} onPress={handleNext} />
              </View>
            </View>
          </ScrollView>
        </View>
        {/* {showQrCode && (
          <BarCodeScanner
            visible={showQrCode}
            onScan={handleScan}
            onClose={() => {
              setShowQrCode(false);
            }}
          />
        )} */}
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    // justifyContent:"center",
    alignItems: "center",
  },
  benefeciary: {
    paddingTop: 4,
    paddingRight: 16,
    paddingBottom: 4,
    paddingLeft: 16,
    backgroundColor: "#F7F4FF",
    borderRadius: 8,
    flexDirection: "row",
    width: "100%",
    minHeight: 44,
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
    marginTop: 16,
  },
  deatilsHead: {
    width: "100%",
    height: 42,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: "5%",
  },
  detailWrap: {
    padding: 24,
    width: "90%",
    alignSelf: "center",
    // height:200,
    backgroundColor: "white",
    borderRadius: 12,
    justifyContent: "center",
    // alignItems: "center",
  },
  detailWrap2: {
    // padding: 24,
    width: "90%",
    alignSelf: "center",
    minHeight: 246,
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent: "center",
    alignItems: "center",
    marginTop: 60,
    paddingBottom: 20,
  },

  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  label: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
    textAlign: "left",
  },
});
