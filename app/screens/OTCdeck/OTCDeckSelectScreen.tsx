import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import React, { useState } from "react";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHeader from "../../components/AuthenticationHedear";
import H4 from "../../components/H4";
import P from "../../components/P";
import NoteComponent from "../../components/NoteComponent";
import NoteComponent2 from "../../components/NoteComponent2";
import { fonts } from "../../config/Fonts";
import { svg } from "../../config/Svg";
import { SvgXml } from "react-native-svg";
import Button from "../../components/Button";
import { useToast } from "../../context/ToastContext";

export default function OTCDeckSelectScreen({ navigation }) {
  const [selectedMethod, setSelectedMethod] = useState(null);
  const { handleToast } = useToast();
  const otcMethod = [
    {
      id: 1,
      text: "I would like to add USD to my sfx account from my Turkish lira account",
      type: "add",
      icon: svg.add,
    },
    {
      id: 2,
      text: "I would like to send money from my Sfx account to my Turkish lira account ",
      type: "add",
      icon: svg.send,
    },
  ];

  const submit = () => {
    if (selectedMethod === null) {
      handleToast("Select an OTC method", "error");
    } else {
      if (selectedMethod.id === 2) {
        navigation.navigate("OTCAccountDetailScreen", {
          selectedMethod,
          isAddMoney: false,
        });
      } else {
        navigation.navigate("OTCTransactionScreen", {
          selectedMethod,
          isAddMoney: selectedMethod.id === 1, // true for adding money, false for sending
        });
      }
    }
  };
  return (
    <View style={styles.container}>
      <Div>
        <AuthenticationHeader navigation={navigation} />
        <ScrollView
          contentContainerStyle={{ paddingBottom: 100 }}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.contentWrapper}>
            <H4>OTC desk</H4>
            <P style={{ marginTop: 4, fontFamily: fonts.poppinsRegular }}>
              Welcome to SFx OTC desk, select your preferred OTC method below
            </P>
            <View style={{ marginTop: 24 }}>
              <NoteComponent2
                type="red"
                contStyle={{ backgroundColor: colors.white }}
                text="The minimum amount you can add or send via SFx OTC is $1 USD. Kindly upload your transaction receipt to our official WhatsApp line immediately after payment."
              />
            </View>
            <View style={styles.selectionBox}>
              <P
                style={{
                  fontFamily: fonts.poppinsRegular,
                  color: colors.textBlack,
                  fontSize: 12,
                }}
              >
                Select OTC method
              </P>
              <View style={{ width: "100%", marginTop: 10, gap: 16 }}>
                {otcMethod.map((item) => (
                  <TouchableOpacity
                    style={[
                      styles.method,
                      {
                        borderColor:
                          item.id === selectedMethod?.id
                            ? colors.primary
                            : colors.stroke,
                      },
                    ]}
                    key={item.id}
                    onPress={() => {
                      setSelectedMethod(item);
                    }}
                  >
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        width: "80%",
                        gap: 16,
                      }}
                    >
                      <SvgXml xml={item.icon} />
                      <P style={styles.methodText}>{item.text}</P>
                    </View>
                    <SvgXml
                      xml={
                        item.id === selectedMethod?.id ? svg.ppTick : svg.check
                      }
                      width={16}
                      height={16}
                    />
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={{ marginTop: 32 }}>
              <Button
                btnText="Continue"
                onPress={() => {
                  submit();
                }}
              />
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentWrapper: {
    width: "100%",
    marginTop: 8,
    paddingHorizontal: 24,
  },
  selectionBox: {
    width: "100%",
    backgroundColor: colors.white,
    padding: 16,
    marginTop: 24,
    borderRadius: 12,
  },
  method: {
    width: "100%",
    borderWidth: 1,
    borderColor: colors.stroke,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  methodText: {
    fontFamily: fonts.poppinsRegular,
    fontSize: 12,
    lineHeight: 19.2,
  },
});
