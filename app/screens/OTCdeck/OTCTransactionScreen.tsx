import React, { useState, useEffect, useContext } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
  Linking,
  TouchableOpacity,
} from "react-native";
import { colors } from "../../config/colors";
import { fonts } from "../../config/Fonts";
import { svg } from "../../config/Svg";
import { SvgXml } from "react-native-svg";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import OTCAmountInput from "../../components/OTCAmountInput";
import OTCKeyboard from "../../components/OTCKeyboard";
import BottomSheet from "../../components/BottomSheet";
import Button from "../../components/Button";
import { useToast } from "../../context/ToastContext";
import useDebounce from "../../components/Debounce";
import {
  GetRateByCountry,
  GetRateById,
  GetUserWallet,
} from "../../RequestHandlers/Wallet";
import H4 from "../../components/H4";
import { formatToTwoDecimals } from "../../Utils/numberFormat";
import { CredentailsContext } from "../../RequestHandlers/CredentailsContext";

const baseHeight = 802;
const { width, height } = Dimensions.get("window");

export default function OTCTransactionScreen({ navigation, route }) {
  const { selectedMethod, isAddMoney, bankDetails } = route?.params;
  const { handleToast } = useToast();
  const { storedCredentails } = useContext(CredentailsContext);

  // State variables
  const [inputValue, setInputValue] = useState("0");
  const [error, setError] = useState(false);
  const [isUsdInput, setIsUsdInput] = useState(true);
  const [tryRate, setTryRate] = useState(1600); // Turkish Lira rate
  const [balance, setBalance] = useState(0);
  const [showWhatsAppModal, setShowWhatsAppModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [walletId, setWalletId] = useState("");
  const [wallbal, setWallbal] = useState("");
  const [walLoad, setWalLoad] = useState(false);
  const [tlBal, setTlBal] = useState(0);

  const debouncedValue = useDebounce(Number(inputValue), 500);

  // Format number with commas
  const formatNumber = (value) => {
    value = value?.toString();
    return value?.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  // Format number with decimal places
  const formatNumberWithDecimal = (value, decimalPlaces = 2) => {
    if (!isNaN(value)) {
      return Number(value)
        .toFixed(decimalPlaces)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    return "0.00";
  };

  // Handle keyboard input
  const handleKeyPress = (key) => {
    setError(false);
    if (key === "←") {
      setInputValue((prev) => (prev.length > 1 ? prev.slice(0, -1) : "0"));
    } else if (key === "00") {
      setInputValue((prev) => {
        if (prev === "0") return "0";
        return prev + "00";
      });
    } else {
      setInputValue((prev) => {
        let newValue = prev === "0" && key !== "." ? key : prev + key;
        newValue = newValue.replace(/[^0-9.]/g, ""); // Remove any non-numeric characters
        return newValue;
      });
    }
  };

  // Toggle between USD and TRY
  const toggleCurrency = () => {
    setIsUsdInput(!isUsdInput);
    setInputValue("0");
  };

  // Calculate converted value based on current input and currency
  const convertedValue = isUsdInput
    ? formatToTwoDecimals((Number(inputValue) || 0) * (tryRate || 0))
    : formatToTwoDecimals((Number(inputValue) || 0) / (tryRate || 0));

  // Get user wallet balance
  const getWallet = async () => {
    setWalLoad(true);
    try {
      const bal = await GetUserWallet();
      setBalance(bal.totalInUsd);
      setTlBal(bal.totalInTRY);
      setWalletId(bal.wallets[0].id);
      setWallbal(bal.wallets[0].balance);
    } catch (error) {
      console.error("Error fetching wallet balance:", error);
    } finally {
      setWalLoad(false);
    }
  };

  const getRateById = async () => {
    try {
      const sfxRate = await GetRateByCountry("TRY", "tcmb");
      sfxRate.map((item, index) => {
        if (isAddMoney) {
          if (item.type === "buy") {
            setTryRate(item.amount);
          }
        } else {
          if (item.type === "sell") {
            setTryRate(item.amount);
          }
        }
      });
    } catch (error) {}
  };
  // Handle Enter button press
  const handleEnterPress = () => {
    // Calculate USD equivalent amount regardless of input currency
    const usdAmount = isUsdInput
      ? Number(inputValue)
      : Number(inputValue) / (tryRate || 1);

    // Check minimum amount in USD
    if (usdAmount < 1) {
      setError(true);
      const minAmountInCurrentCurrency = isUsdInput
        ? "$1 USD"
        : `₺${formatToTwoDecimals(tryRate || 0)} TRY`;
      handleToast(`Minimum amount is ${minAmountInCurrentCurrency}`, "error");
      return;
    }

    // If this is not an "add money" operation, navigate to OTCConfirmTransactionScreen
    if (!isAddMoney) {
      // Check if amount exceeds balance
      const enteredAmount = Number(inputValue);
      const userBalance = isUsdInput ? Number(balance) : Number(tlBal) || 0;

      if (enteredAmount > userBalance) {
        setError(true);
        handleToast("Insufficient balance", "error");
        return;
      }

      // Prepare transaction data for confirmation screen
      const transactionData = {
        wallbal,
        walletId,
        amount: enteredAmount,
        tryAmount: isUsdInput
          ? formatToTwoDecimals(enteredAmount * tryRate)
          : formatToTwoDecimals(Number(inputValue)),
        selectedMethod,
        bankDetails,
        isUsdInput,
        tryRate,
        currency: isUsdInput ? "USD" : "TRY",
        // Pass any additional route parameters
        ...route.params,
      };

      // Navigate to OTCConfirmTransactionScreen
      navigation.navigate("OTCConfirmTransactionScreen", transactionData);
    } else {
      // For "add money" operations, show WhatsApp modal
      setShowWhatsAppModal(true);
    }
  };

  // Open WhatsApp with OTC request
  const openWhatsApp = () => {
    setLoading(true);

    const phoneNumber = "+************";
    const usdAmount = isUsdInput
      ? formatToTwoDecimals(Number(inputValue))
      : convertedValue;
    const tryAmount = isUsdInput
      ? convertedValue
      : formatToTwoDecimals(Number(inputValue));
    const messageString = isAddMoney
      ? `Hello SFx OTC, \n\nI would like to add $${usdAmount} USD (₺${tryAmount} TRY) to my sfx account with username ${storedCredentails?.user?.username}. I will transfer the money from my Tukish lira account. \n\nThank you!`
      : `Hello SFx OTC, \n\nI would like to send $${usdAmount} USD (₺${tryAmount} TRY) from my sfx account to my Tukish lira account. \n\nThank you!`;

    const message = encodeURIComponent(messageString);
    const url = `https://wa.me/${phoneNumber}?text=${message}`;
    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          handleToast("WhatsApp is not installed on your device", "error");
        }
      })
      .catch((err) => {
        console.error("Error opening WhatsApp:", err);
        handleToast("Could not open WhatsApp", "error");
      })
      .finally(() => {
        setLoading(false);
        setShowWhatsAppModal(false);
      });
  };

  // Load wallet balance on component mount
  useEffect(() => {
    getWallet();
    getRateById();
  }, []);

  return (
    <View style={styles.container}>
      <Div>
        <AuthenticationHedear text="Amount" navigation={navigation} />
        <ScrollView
          contentContainerStyle={{
            paddingBottom: 100,
            flexGrow: 1, // Allow content to grow to fill available space
          }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled" // Prevent keyboard from dismissing on tap
        >
          <View style={styles.contentWrapper}>
            {/* Top Section with Amount Input */}
            <View style={styles.topSection}>
              <View style={styles.inputCardWrap}>
                <OTCAmountInput
                  headerText={`How much do you want to ${
                    isAddMoney ? "add" : "send"
                  }`}
                  onTogglePress={toggleCurrency}
                  error={error}
                  amountValue={
                    <>
                      <P
                        numberOfLines={1}
                        style={{
                          textAlign: "center",
                          fontSize: 24,
                          lineHeight: 48,
                          marginRight: 4,
                        }}
                      >
                        {isUsdInput
                          ? `$${formatNumber(inputValue)}`
                          : `₺${formatNumber(inputValue)}`}
                        <P style={{ lineHeight: 48 }}>
                          {isUsdInput ? "USD" : "TRY"}
                        </P>
                      </P>
                    </>
                  }
                  convertedValue={
                    <>
                      <P
                        numberOfLines={1}
                        style={{
                          textAlign: "center",
                          fontSize: 16,
                          lineHeight: 24,
                          marginRight: 4,
                          color: colors.dGray,
                        }}
                      >
                        {isUsdInput
                          ? `₺${convertedValue}`
                          : `$${convertedValue}`}
                        <P
                          style={{
                            lineHeight: 24,
                            fontSize: 12,
                            color: colors.dGray,
                          }}
                        >
                          {isUsdInput ? "TRY" : "USD"}
                        </P>
                      </P>
                    </>
                  }
                  loading={walLoad}
                  balance={
                    isUsdInput
                      ? `$${formatToTwoDecimals(Number(balance) || 0)}`
                      : `₺${formatToTwoDecimals(Number(tlBal) || 0)}`
                  }
                  rate={`1USD = ₺${tryRate}TRY`}
                />
              </View>
            </View>

            {/* Bottom Section with Keyboard */}
            <View style={styles.bottomSection}>
              <View style={styles.keyboardContainer}>
                <OTCKeyboard
                  onKeyPress={handleKeyPress}
                  onEnterPress={handleEnterPress}
                />
              </View>
            </View>
          </View>
        </ScrollView>

        {/* WhatsApp Modal */}
        <BottomSheet
          isVisible={showWhatsAppModal}
          showBackArrow={false}
          onClose={() => setShowWhatsAppModal(false)}
          modalContentStyle={{ height: "85%" }}
          extraModalStyle={{ height: "82%" }}
          components={
            <ScrollView contentContainerStyle={{ paddingBottom: 100 }}>
              <View style={styles.whatsAppModalContent}>
                <View style={styles.whatsAppIconContainer}>
                  <SvgXml xml={svg.whatsApp} />
                </View>

                <H4 style={styles.whatsAppTitle}>
                  Proceed to SFx OTC desk on WhatsApp
                </H4>
                <P style={styles.whatsAppSubtitle}>
                  To complete your OTC transaction, please continue on WhatsApp
                </P>

                <View style={styles.checkItemContainer}>
                  <SvgXml xml={svg.checkGreen} width={20} height={20} />
                  <P style={styles.checkItemText}>
                    Share your request with otc on whatsapp{" "}
                    <P style={{ fontFamily: fonts.poppinsBold, fontSize: 12 }}>
                      +************
                    </P>{" "}
                    is the only OTC channel
                  </P>
                </View>

                <View style={styles.checkItemContainer}>
                  <SvgXml xml={svg.checkGreen} width={20} height={20} />
                  <P style={styles.checkItemText}>
                    Receive USD in your SFx wallet after transacting with OTC
                  </P>
                </View>

                <View style={styles.reminderContainer}>
                  <View style={styles.reminderHeader}>
                    <SvgXml xml={svg.alertCircle2} width={16} height={16} />
                    <P style={styles.reminderTitle}>Reminder:</P>
                  </View>
                  <P style={styles.reminderItem}>• Minimum amount: $1 USD</P>
                  <P style={styles.reminderItem}>
                    • Upload your payment receipt for confirmation
                  </P>
                  <P style={styles.reminderItem}>
                    • Your wallet will be credited after verification
                  </P>
                </View>
                <Button
                  btnText="Start OTC on WhatsApp"
                  onPress={openWhatsApp}
                  loading={loading}
                  style={styles.whatsAppButton}
                />
              </View>
            </ScrollView>
          }
        />
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentWrapper: {
    width: "100%",
    marginTop: 8,
    paddingHorizontal: 24,
    flex: 1,
    justifyContent: "space-between", // Distribute space between components
    minHeight: height * 0.75, // Ensure minimum height for proper spacing
  },
  inputCardWrap: {
    width: "100%",
    marginTop: 24,
  },
  keyboardContainer: {
    width: "100%",
    marginTop: Math.max(16, height * 0.03), // Dynamic margin based on screen height (min 16px, 3% of height)
  },
  whatsAppModalContent: {
    paddingTop: 24,
    paddingBottom: 24,
  },
  whatsAppIconContainer: {
    alignItems: "center",
    marginBottom: 16,
  },
  whatsAppTitle: {
    fontSize: 18,
    fontFamily: fonts.poppinsBold,
    textAlign: "center",
    marginBottom: 8,
  },
  whatsAppSubtitle: {
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    textAlign: "center",
    color: colors.gray,
    marginBottom: 24,
  },
  checkItemContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 16,
    paddingHorizontal: 8,
  },
  checkItemText: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    marginLeft: 8,
    flex: 1,
  },
  reminderContainer: {
    backgroundColor: colors.lowOpPrimary3,
    borderLeftWidth: 4,
    borderColor: colors.primary,
    padding: 16,
    borderRadius: 8,
    marginVertical: 16,
  },
  reminderHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  reminderTitle: {
    fontSize: 14,
    fontFamily: fonts.poppinsBold,
    marginLeft: 8,
  },
  reminderItem: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    marginBottom: 4,
  },
  whatsAppButton: {
    marginTop: 16,
  },
  topSection: {
    width: "100%",
    flex: 1,
    justifyContent: "flex-start",
  },
  bottomSection: {
    width: "100%",
    flex: 1,
    justifyContent: "flex-end",
    paddingBottom: Math.max(5, height * 0.02),
  },
});
