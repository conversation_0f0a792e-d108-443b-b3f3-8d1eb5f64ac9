import React, { useState } from "react";
import { View, StyleSheet, Dimensions, ScrollView, Image } from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import InputCard from "../../components/InputCard";
import Keyboard from "../../components/Keyboard";
import Button from "../../components/Button";
import { colors } from "../../config/colors";

const { width, height } = Dimensions.get("window");

export default function AirtimeAmountScreen({ navigation }) {
  const [inputValue, setInputValue] = useState("0");
  const [error, setError] = useState(false);
  const [isUsdInput, setIsUsdInput] = useState(true); // Track which currency is being input
  const ngnRate = 33; // Example exchange rate

  const formatNumber = (value) => {
    value = value.toString();
    return value.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const formatNumberWithDecimal = (value, decimalPlaces = 2) => {
    if (!isNaN(value)) {
      return Number(value)
        .toFixed(decimalPlaces)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    return "0.00";
  };

  const handleKeyPress = (key) => {
    setError(false); // Reset error state on key press

    if (key === "←") {
      setInputValue((prev) => (prev.length > 1 ? prev.slice(0, -1) : "0"));
    } else if (key === "Enter") {
    
      // Handle enter key press
    } else {
      setInputValue((prev) => {
        let newValue = prev === "0" && key !== "." ? key : prev + key;
        newValue = newValue.replace(/[^0-9.]/g, ""); // Remove any non-numeric characters
        return newValue;
      });
    }
  };

  const toggleCurrency = () => {
    setIsUsdInput(!isUsdInput);
    setInputValue("0"); // Reset input value when toggling
  };

  const convertedValue = isUsdInput
    ? formatNumberWithDecimal(Number(inputValue) * ngnRate)
    : formatNumberWithDecimal(Number(inputValue) / ngnRate);

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Amount" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.inputCardWrap}>
              <View style={{ alignItems: "center", marginBottom: 16 }}>
                <Image
                  source={require("../../assets/gtb.png")}
                  style={{ width: 32, height: 32, marginBottom: 8 }}
                />
                <P style={{ fontSize: 12 }}>John Doe</P>
                <P
                  style={{
                    fontSize: 12,
                    color: colors.gray,
                    fontFamily: fonts.poppinsRegular,
                    textAlign: "center",
                  }}
                >
                  905338833199 | prepaid
                </P>
              </View>
              <InputCard
                headerText="How much do you want to send"
                onTogglePress={toggleCurrency}
                amountValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 32,
                        lineHeight: 48,
                        marginRight: 4,
                      }}
                    >
                      $
                      {isUsdInput
                        ? `${formatNumber(inputValue)}`
                        : `${formatNumber(inputValue)}`}
                      <P style={{ lineHeight: 48 }}>
                        {isUsdInput ? "USD" : "NGN"}
                      </P>
                    </P>
                  </>
                }
                convertedValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 16,
                        lineHeight: 24,
                        marginRight: 4,
                      }}
                    >
                      ₺
                      {isUsdInput
                        ? `${formatNumberWithDecimal(
                            Number(inputValue) * ngnRate
                          )}`
                        : `${formatNumberWithDecimal(
                            Number(inputValue) / ngnRate
                          )}`}
                      <P style={{ lineHeight: 24, fontSize: 12 }}>
                        {isUsdInput ? "TRY" : "USD"}
                      </P>
                    </P>
                  </>
                }
                text1={`1 USD ~ ${ngnRate} TRY`}
                text2="Available balance: $1,000,000"
                error={error}
              />
            </View>
            <View style={styles.bottom}>
              <View style={{ width: "90%", alignSelf: "center" }}>
                <Keyboard onKeyPress={handleKeyPress} />
              </View>
              <View
                style={{ width: "80%", alignSelf: "center", marginTop: 16 }}
              >
                <Button
                  btnText="Next"
                  onPress={() => {
                    inputValue == "0"
                      ? setError(true)
                      : navigation.navigate("AirtimeTransactionDetails");
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    height: (92 * height) / 100,
    backgroundColor: "rgba(247, 244, 255, 1)",
    paddingTop: 16,
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
  },
  bottom: {
    width,
    top: 30,
  },
});
