import React, { useEffect, useState, useCallback } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  FlatList,
  ActivityIndicator,
} from "react-native";
import { fonts } from "../config/Fonts";
import Div from "../components/Div";
import AuthenticationHedear from "../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "../components/P";
import MicroBtn from "../components/MicroBtn";
import { colors } from "../config/colors";
import ListItem from "../components/ListItem";
import { GetNotifications } from "../RequestHandlers/notification";
import { useFocusEffect, useTheme } from "@react-navigation/native";
import { formatDate2, formatDate3 } from "../components/FormatDate";
import { MarkAllAsRead, MarkAsRead } from "../RequestHandlers/notification";
import FailedToLoad from "../components/ErrorSate/FailedToLoad";
import NetInfo from "@react-native-community/netinfo";
import Offline from "../components/ErrorSate/Offline";
import { TransactionClick } from "../Utils/TransactionClick";
import Loader from "../components/ActivityIndicator";
import { withApiErrorToast } from "../Utils/withApiErrorToast";
import { useToast } from "../context/ToastContext";

const { width, height } = Dimensions.get("window");

const groupByDate = (items) => {
  return items.reduce((acc, item) => {
    const dateOnly = new Date(item.createdAt).toISOString().split("T")[0];
    acc[dateOnly] = acc[dateOnly] || [];
    acc[dateOnly].push(item);

    return acc;
  }, {});
};

export default function NotificationScreen({ navigation }) {
  const [acctiveTab, setActiveTab] = useState("All");
  const tabs = ["All", "Payment", "Message"];
  const [isNewNoti, setIsNewNoti] = useState(false);
  const [isNewMNoti, setIsNewMNoti] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);
  const [notifications, setNotifications] = useState<any>([]);
  const [loading1, setLoading1] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [totalItem, setTotalItem] = useState(0);
  const [length, setLength] = useState(0);
  const [loading, setLoading] = useState(false);
  const [fetchError, setfetchError] = useState(false);
  const [isOnline, setIsOnline] = useState(false);
  const { handleToast } = useToast();
  // const notifications = [];
  const getNoti = async (loadMore = false) => {
    setLoading1(true);
    try {
      const res = await withApiErrorToast(
        GetNotifications(1, limit, acctiveTab.toLowerCase()),
        handleToast
      );
      if (res.error) {
        setfetchError(true);
      } else {
        setfetchError(false);
      }
      if (res) {
        setLoading(false);
        setLoading1(false);
        setTotalItem(res.meta.totalItems);
        setLength(res.items.length);
      }
      if (res.items.length === 0) {
        setHasMoreData(false);
      } else {
        // Sort transactions
        const sortedTransactions = res.items.sort((a, b) => {
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        });
        // Append new transactions if loading more, otherwise replace the list
        setNotifications((prevTransactions) =>
          loadMore ? sortedTransactions : sortedTransactions
        );
      }
    } catch (error) {}
  };

  const markAsRead = async (id) => {
    try {
      // Make API call to mark as read
      const res = await withApiErrorToast(MarkAsRead(id), handleToast);

      // Update local state without fetching all notifications again
      setNotifications((prevNotifications) => {
        // Create a new array with the updated notification
        return prevNotifications.map((notification) => {
          if (notification.id === id) {
            // Return a new object with status set to "read"
            return { ...notification, status: "read" };
          }
          return notification;
        });
      });

      // Update tab indicators without fetching all notifications
      updateTabIndicators();
    } catch (error) {}
  };

  // Helper function to update tab indicators based on current notifications
  const updateTabIndicators = () => {
    let hasUnreadPayment = false;
    let hasUnreadMessage = false;

    notifications.forEach((notification) => {
      if (notification.path === "payment" && notification.status === "unread") {
        hasUnreadPayment = true;
      }
      if (notification.path === "message" && notification.status === "unread") {
        hasUnreadMessage = true;
      }
    });

    setIsNewNoti(hasUnreadPayment);
    setIsNewMNoti(hasUnreadMessage);
  };

  const markAllAsRead = async () => {
    try {
      const res = await withApiErrorToast(MarkAllAsRead(), handleToast);
      if (res.status === true) {
        // Update all notifications in local state to be read
        setNotifications((prevNotifications) => {
          return prevNotifications.map((notification) => {
            return { ...notification, status: "read" };
          });
        });

        // Since all notifications are read, we can directly set indicators to false
        setIsNewNoti(false);
        setIsNewMNoti(false);
      }
    } catch (error) {}
  };

  const getAllNoti = async () => {
    try {
      const res = await GetNotifications(1, 20, "all");
      let hasUnreadPayment = false;
      let hasUnreadMessage = false;
      res.items.forEach((notification) => {
        if (
          notification.path === "payment" &&
          notification.status === "unread"
        ) {
          hasUnreadPayment = true;
        }
        if (
          notification.path === "message" &&
          notification.status === "unread"
        ) {
          hasUnreadMessage = true;
        }
      });
      setIsNewNoti(hasUnreadPayment);
      setIsNewMNoti(hasUnreadMessage);
    } catch (error) {}
  };

  const handlepaymentClick = (item) => {
    TransactionClick(item, navigation);
  };
  const checkConnection = () => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      if (state.isConnected === false) {
        setIsOnline(false);
      } else if (state.isConnected === true) {
        setIsOnline(true);
      }
    });
  };
  useEffect(() => {
    checkConnection();
  }, []);
  useFocusEffect(
    useCallback(() => {
      setLoading(true);
      setNotifications([]);
      setLimit(20);
      getNoti();
      getAllNoti();
      // We'll still call getAllNoti() on focus to ensure tab indicators are up-to-date
    }, [acctiveTab?.toLowerCase()])
  );
  const groupedNotifications = groupByDate(notifications);
  const fetchMoreTransactions = () => {
    if (!loading && hasMoreData) {
      const newLimit = limit + 20;
      setLimit(newLimit);
      getNoti(true);
    }
  };
  const renderTransactionItem = ({ item, index }) => (
    <TouchableOpacity
      key={index}
      onPress={() => {
        if (item.path === "payment") {
          // navigation.navigate("History");
          handlepaymentClick(item.transaction);
        } else if (item.type === "kyc") {
          navigation.navigate("AccountVerificationPromt");
        } else if (item.type === "card") {
          // navigation.navigate("CardScreen");
        } else if (item.type === "sfx-point") {
          navigation.navigate("ReferralListScreen");
        } else {
          // navigation.navigate("AccountVerificationPromt");
        }
        markAsRead(item.id);
      }}
    >
      <View style={styles.item} key={index}>
        <View>
          {item.status === "unread" && <View style={styles.dot1}></View>}

          <SvgXml xml={svg.notiOutline} />
        </View>
        <View style={{ marginLeft: 12 }}>
          <P style={styles.transactionAmount}>
            {item.title === "Add money"
              ? "You receive USD"
              : item.title === "Sent money"
              ? "You sent USD"
              : item?.title}
          </P>
          {/* @ts-ignore */}
          <P style={[styles.transactionDate, { width: (60 * width) / 100 }]}>
            {item?.body}
          </P>
        </View>
        <View
          style={{
            position: "absolute",
            right: 16,
            top: 16,
            bottom: 16,
            alignItems: "flex-end",
            justifyContent: "center",
          }}
        >
          <P
            style={{
              fontSize: 12,
              fontFamily: fonts.poppinsMedium,
              color: colors.gray,
            }}
          >
            {formatDate3(item?.createdAt)}
          </P>
          {/* {item.type != "sfxPoint" && (
            <P
              // @ts-ignore
              style={[
                styles.transactionDate,
                {
                  color: item.status.toLowerCase().includes("successful")
                    ? colors.green
                    : item.status.toLowerCase().includes("pending")
                    ? colors.yellow
                    : colors.red,
                },
              ]}
            >
              {item.status}
            </P>
          )} */}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderDateGroup = ({ item: date }) => (
    <View style={{ marginTop: 24 }}>
      <P style={styles.datCat}>{formatDate2(date)}</P>
      <FlatList
        data={groupedNotifications[date]} // Transactions for this date
        renderItem={renderTransactionItem}
        keyExtractor={(item, index) => `${date}-${index}`}
      />
    </View>
  );

  const dateKeys = Object.keys(groupedNotifications);

  if (loading) {
    return <Loader />;
  }
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Notification"
          navigation={navigation}
          navStyle={{ justifyContent: "space-between" }}
          iconComp={
            <TouchableOpacity
              onPress={() => {
                markAllAsRead();
              }}
            >
              <P
                style={{
                  color: colors.primary,
                  textDecorationLine: "underline",
                  fontSize: 12,
                }}
              >
                Mark all as read
              </P>
            </TouchableOpacity>
          }
        />
        <View style={styles.tabCont}>
          {tabs.map((item, index) => (
            <TouchableOpacity key={index} onPress={() => setActiveTab(item)}>
              {item?.toLowerCase() === "payment" && isNewNoti && (
                <View style={styles.dot}></View>
              )}
              {item?.toLowerCase() === "message" && isNewMNoti && (
                <View style={styles.dot}></View>
              )}
              <View
                style={{
                  padding: 16,
                  paddingTop: 3.5,
                  paddingBottom: 3.5,
                  borderRadius: 100,
                  backgroundColor:
                    item === acctiveTab ? colors.white : "transparent",
                }}
              >
                <P
                  style={{
                    fontSize: 12,

                    color: item === acctiveTab ? colors.primary : colors.gray,
                  }}
                >
                  {item}
                </P>
              </View>
            </TouchableOpacity>
          ))}
        </View>
        {!isOnline ? (
          <Offline />
        ) : (
          <>
            {fetchError ? (
              <FailedToLoad
                onPress={() => {
                  setLoading(true);
                  getNoti();
                }}
              />
            ) : (
              <>
                <View style={styles.contentBody}>
                  {notifications.length === 0 ? (
                    <View style={styles.emptyCont}>
                      <SvgXml xml={svg.noTransaction} />
                      <P
                        style={{
                          fontFamily: fonts.poppinsMedium,
                          lineHeight: 21,
                          marginTop: 16,
                          fontSize: 12,
                        }}
                      >
                        No Notification!
                      </P>
                      <P
                        style={{
                          fontSize: 12,
                          fontFamily: fonts.poppinsRegular,
                          color: colors.gray2,
                        }}
                      >
                        You have no notification yet
                      </P>
                    </View>
                  ) : (
                    <View>
                      {Object.keys(groupedNotifications).length === 0 ? (
                        <View style={styles.emptyCont}>
                          <SvgXml xml={svg.emptyNoti} />
                          <P
                            style={{
                              fontFamily: fonts.poppinsMedium,
                              lineHeight: 21,
                              marginTop: 16,
                            }}
                          >
                            No history
                          </P>
                          <P
                            style={{
                              fontSize: 13,
                              fontFamily: fonts.poppinsRegular,
                              color: colors.gray2,
                            }}
                          >
                            You have no transaction history yet
                          </P>
                        </View>
                      ) : (
                        // <ScrollView>
                        //   <View style={{ width: "90%", alignSelf: "center" }}>
                        //     {Object.keys(groupedNotifications).map((date, index) => (
                        //       <View
                        //         key={index}
                        //         style={{
                        //           marginTop: index === 0 ? 0 : 24,
                        //         }}
                        //       >
                        //         <P style={styles.datCat}>{date}</P>
                        //         <View>
                        //           {groupedNotifications[date].map((item, index) => (

                        //           ))}
                        //         </View>
                        //       </View>
                        //     ))}
                        //   </View>
                        // </ScrollView>
                        <View
                          style={{
                            width: "90%",
                            alignSelf: "center",
                            // paddingBottom: 300
                            // marginBottom: 300,
                          }}
                        >
                          <FlatList
                            data={Object.keys(groupedNotifications)}
                            renderItem={renderDateGroup}
                            keyExtractor={(date, index) => `group-${index}`}
                            showsVerticalScrollIndicator={false}
                            onEndReached={() => {
                              fetchMoreTransactions();
                              console.log("wowowo");
                            }}
                            onEndReachedThreshold={0.3}
                            contentContainerStyle={{ paddingBottom: 300 }}
                            ListFooterComponent={
                              loading1 && length < totalItem ? (
                                <ActivityIndicator
                                  color={colors.primary}
                                  style={{ marginTop: 16 }}
                                />
                              ) : null
                            }
                          />
                        </View>
                      )}
                    </View>
                  )}
                </View>
              </>
            )}
          </>
        )}
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    // height: (100 * height) / 100,
    backgroundColor: colors.secBackground,
  },
  btnCard: {
    width: "90%",
    minHeight: 156,
    backgroundColor: "white",
    alignSelf: "center",
    marginTop: 24,
    borderRadius: 12,
    paddingTop: 16,
    paddingBottom: 16,
    // paddingLeft: 16,
    // paddingRight: 16,
  },
  btnSec1: {
    width: "100%",
    justifyContent: "space-around",
    flexDirection: "row",
    marginBottom: 24,
    // paddingHorizontal: 18.33
  },
  emptyCont: {
    width: "100%",
    height: "80%",
    alignItems: "center",
    justifyContent: "center",
  },
  datCat: {
    fontSize: 12,
  },
  item: {
    width: "100%",
    padding: 16,
    backgroundColor: colors.white,
    marginTop: 8,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
  },
  transactionAmount: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    // fontWeight: "bold",
  },
  transactionDate: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  tabCont: {
    width: "100%",
    padding: 24,
    paddingTop: 12,
    paddingBottom: 12,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    flexDirection: "row",
  },
  dot: {
    width: 8,
    height: 8,
    backgroundColor: colors.red,
    borderRadius: 99,
    position: "absolute",
    zIndex: 100,
    right: 0,
    top: -5,
  },
  dot1: {
    width: 8,
    height: 8,
    backgroundColor: colors.red,
    borderRadius: 99,
    position: "absolute",
    zIndex: 100,
    right: 0,
    top: 0,
  },
});
