import React, { useCallback, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
  Pressable,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { colors } from "../../config/colors";
import Button from "../../components/Button";
import Input from "../../components/Input";
import BottomSheet from "../../components/BottomSheet";
import LocalGovSelect from "../../components/LocalGovSelect";
import Content1 from "../../components/Content1";
import CustomSwitch from "../../components/CustomSwitch";
import CustomSwitch1 from "../../components/CustomSwitch1";
import { GetUserDetails, UpdateUser } from "../../RequestHandlers/User";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../../components/ActivityIndicator";
import { useToast } from "../../context/ToastContext";

const { width, height } = Dimensions.get("window");
export default function NotificationIndex({ navigation }) {
  const [isEmailOn, setIsEmail] = useState(false);
  const [isPushOn, setIsPushOn] = useState(false);
  const [loader, setLoader] = useState(false);
  const { handleToast } = useToast();

  const getPushStatus = async () => {
    setLoader(true);
    try {
      const res = await GetUserDetails();
      if (res.pushnotification) {
        setIsPushOn(res.pushnotification);
        setIsEmail(res.emailnotification);
      }
    } catch (error) {
      handleToast("Unknown error", "error");
      console.log();
    } finally {
      setLoader(false);
    }
  };

  const updateNotificationStatus = async (type, value) => {
    try {
      const body =
        type === "push"
          ? { pushnotification: value }
          : { emailnotification: value };
      const res = await UpdateUser(body);
      if (res.message === "User Updated Successfully!") {
        handleToast("Notification status updated", "success");
      } else {
        handleToast(res.message, "error");
      }
    } catch (error) {}
  };

  const handlePushSwitch = async () => {
    if (isPushOn) {
      setIsPushOn(false);
      updateNotificationStatus("push", false);
    } else {
      setIsPushOn(true);
      updateNotificationStatus("push", true);
    }
  };
  const handleEmailSwitch = async () => {
    if (isEmailOn) {
      setIsEmail(false);
      updateNotificationStatus("Email", false);
    } else {
      setIsEmail(true);
      updateNotificationStatus("Email", true);
    }
  };
  useFocusEffect(
    useCallback(() => {
      getPushStatus();
    }, [])
  );

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Notification" navigation={navigation} />
        <View style={styles.contentBody}>
          <View style={styles.SubHCont}>
            <SvgXml xml={svg.megaBadge} />
            <View style={{ width: "70%" }}>
              <View
                style={{ flexDirection: "row", alignItems: "center", gap: 8 }}
              >
                <P style={{ fontSize: 12 }}>Notification settings</P>
              </View>
              <P
                style={{
                  fontSize: 12,
                  marginTop: 2,
                  fontFamily: fonts.poppinsRegular,
                  color: colors.dGray,
                }}
              >
                Receive notification base on your account preferred settings
              </P>
            </View>
          </View>
          <View style={styles.detailWrap}>
            <View style={[styles.card, { marginTop: 16 }]}>
              <View>
                <P style={{ fontSize: 12, fontFamily: fonts.poppinsMedium }}>
                  Push notification
                </P>
                <P style={styles.subText}>Notify me via push notification</P>
              </View>
              <CustomSwitch1 onToggle={handlePushSwitch} isOn={isPushOn} />
            </View>
            <View
              style={[styles.card, { borderBottomWidth: 0, marginBottom: 0 }]}
            >
              <View>
                <P style={{ fontSize: 12, fontFamily: fonts.poppinsMedium }}>
                  Email notification
                </P>
                <P style={styles.subText}>Notify me via email notification</P>
              </View>
              <CustomSwitch1 onToggle={handleEmailSwitch} isOn={isEmailOn} />
            </View>
          </View>
        </View>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 16,
    // marginTop: -16,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    alignItems: "center",
  },
  card: {
    height: 50,
    width: "90%",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottomWidth: 1,
    borderBottomColor: colors.stroke,
    marginBottom: 16,
    paddingBottom: 16,
  },
  SubHCont: {
    width: "90%",
    alignSelf: "center",
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  subText: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.dGray,
  },
});
