import React, { useState, useEffect } from "react";
import { View, TouchableOpacity, StyleSheet, ScrollView } from "react-native";
import P from "./P";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import ListItemSelect from "./ListItemSelect";
import { svg } from "../config/Svg";
import { SvgXml } from "react-native-svg";
interface PProps {
  onPress?: any;
  onActiveAcc?: (index: String | null) => void;
  onActiveAccIndex?: (index: Number | null) => void;
}
export default function AccountSelect({
  onPress,
  onActiveAcc,
  onActiveAccIndex,
}: PProps) {
  const [activeType, setActiveType] = useState(null);
  const [activeAcc, setActiveAcc] = useState("");
  const [activeAccIndex, setActiveAccIndex] = useState(null);
  const paymentType = [
    {
      type: "Tether",
      text1: "USDT",
      icon: svg.tather,
    },
    {
      type: "USD coin",
      text1: "USDC",
      icon: svg.usdCoin,
    },
  ];
  useEffect(() => {
    if (onActiveAcc) {
      onActiveAcc(activeAcc);
    }
  }, [activeType]);
  useEffect(() => {
    if (onActiveAccIndex) {
      onActiveAccIndex(activeType);
    }
  }, [activeType]);

  return (
    <ScrollView>
      <View style={styles.viewContent}>
        <P
          style={{
            fontSize: 12,
            color: colors.gray,
            font: fonts.poppinsRegular,
            marginBottom: 8,
          }}
        >
          Select an account you want to add money
        </P>
        {paymentType.map((item, index) => {
          return (
            <ListItemSelect
              text1={item.type}
              text2={item.text1}
              icon={item.icon}
              key={index}
              isActive={activeType === index}
              onPress={() => {
                setActiveType(index);
                setActiveAcc(item.type);
                setTimeout(() => {
                  onPress();
                }, 1000);
              }}
              containerStyle={{ marginBottom: 16 }}
            />
          );
        })}
        <View style={styles.info}>
          <SvgXml xml={svg.alertCircle} style={{ marginRight: 8 }} />
          <P style={{ fontSize: 10, lineHeight: 16 }}>
            Please note that your money will be added to as an asset of the
            account you selected
          </P>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  viewContent: {
    width: "100%",
    paddingTop: 24,
  },
  info: {
    width: "100%",
    backgroundColor: colors.lowOpPrimary2,
    padding: 16,
    paddingTop: 8,
    paddingBottom: 8,
    borderRadius: 8,
    marginTop: 16,
    flexDirection: "row",
    alignItems: "center",
  },
});
