import React, { CSSProperties, useEffect, useState, useRef } from "react";
import { Animated, Dimensions, Platform, StyleSheet, View } from "react-native";
import { SvgXml } from "react-native-svg";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import { svg } from "../config/Svg";
import P from "./P";

const { width, height } = Dimensions.get("window");
const baseHeight = 802;

interface Tprops {
  popCardStyle?: CSSProperties;
}
let showToastFn: (text: string, type?: "error" | "success") => void;

export function handleToast(text: string, type?: "error" | "success") {
  if (showToastFn) {
    showToastFn(text, type || "success");
  }
}

interface ToastProps {
  text: string;
  type: "error" | "success";
}

export default function Toast({ text, type }: ToastProps) {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(100)).current;
  const toastTimeout = useRef<number | null>(null);

  useEffect(() => {
    fadeAnim.setValue(0);
    translateY.setValue(100);

    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
    // @ts-ignore
    toastTimeout.current = setTimeout(() => {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: 100,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }, 5000);

    return () => {
      if (toastTimeout.current) clearTimeout(toastTimeout.current);
    };
  }, [text, type]);

  return (
    <Animated.View
      style={[styles.popBody, { opacity: fadeAnim }]}
      pointerEvents="box-none"
    >
      <Animated.View
        style={[
          styles.popCard,
          {
            transform: [{ translateY }],
            backgroundColor: type === "error" ? colors.red : colors.green,
          },
        ]}
      >
        {type === "error" ? (
          <SvgXml xml={svg.warning} style={{ marginLeft: 5 }} />
        ) : (
          <SvgXml xml={svg.tick} style={{ marginLeft: 5 }} />
        )}
        <P style={styles.headerText}>{text}</P>
      </Animated.View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  popBody: {
    width,
    height,
    flex: 1,
    backgroundColor: "transparent",
    position: "absolute",
    alignItems: "center",
    justifyContent: "flex-start",
    zIndex: 8000000,
  },
  popCard: {
    width: "90%",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    paddingLeft: 16,
    paddingRight: 16,
    position: "absolute",
    bottom:
      Platform.OS == "ios"
        ? (70 / baseHeight) * height
        : (50 / baseHeight) * height,
    flexDirection: "row",
    minHeight: 44,
    paddingTop: 5,
    paddingBottom: 5,
  },
  headerText: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    textAlign: "left",
    marginLeft: 12,
    color: colors.white,
  },
});
