import React from "react";
import {
  View,
  TouchableOpacity,
  Platform,
  StyleSheet,
  Share,
} from "react-native";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "./P";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import { opacity } from "react-native-reanimated/lib/typescript/Colors";
interface PProps {
  disabled?: boolean;
  code?: string;
}
export default function ReferralShare({ disabled, code }: PProps) {
  const onShare = async () => {
    try {
      const result = await Share.share({
        //       message: `Hi, there! ${firstName} has invited you to open a money account on the SFx money app, https://www.sfxchange.co/en
        // Download the Money app and join with this code:${refCode}.`,
        message: code,
      });
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
        } else {
        }
      } else if (result.action === Share.dismissedAction) {
      }
    } catch (error) {}
  };
  return (
    <View
      style={[
        styles.bottomFloat,
        Platform.OS === "ios"
          ? {
              shadowColor: colors.gray,
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: 0.5,
              shadowRadius: 4,
            }
          : { elevation: 20 },
      ]}
    >
      <View style={styles.shareComp}>
        <View style={styles.textSection}>
          <P style={{ fontSize: 11, color: colors.dGray }}>
            Your referral code
          </P>
          <P style={{ fontFamily: fonts.poppinsSemibold, fontSize: 13 }}>
            {code}
          </P>
        </View>
        <TouchableOpacity
          disabled={disabled}
          style={[styles.buttonSection, { opacity: disabled ? 0.5 : 1 }]}
          onPress={onShare}
        >
          <SvgXml xml={svg.shareWhite} />
        </TouchableOpacity>
        <View style={styles.overLayBorder}></View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  bottomFloat: {
    width: "100%",
    height: 90,
    backgroundColor: colors.white,
    position: "absolute",
    bottom: 0,
    alignItems: "center",
    justifyContent: "center",
  },
  shareComp: {
    width: "80%",
    height: 48,
    borderRadius: 8,
    overflow: "hidden",
    flexDirection: "row",
    position: "relative",
  },
  textSection: {
    width: "80%",
    height: "100%",
    backgroundColor: colors.secBackground,
    paddingLeft: 16,
    justifyContent: "center",
  },
  buttonSection: {
    width: "20%",
    height: "100%",
    backgroundColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
  },
  overLayBorder: {
    width: "100%",
    position: "absolute",
    height: 48,
    borderWidth: 1.2,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    pointerEvents: "none",
  },
});
