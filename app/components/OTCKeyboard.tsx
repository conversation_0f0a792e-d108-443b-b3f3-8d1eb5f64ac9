import React from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Dimensions,
} from "react-native";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import { fonts } from "../config/Fonts";
import { colors } from "../config/colors";

const baseHeight = 802;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");

interface OTCKeyboardProps {
  onKeyPress: (key: string) => void;
  onEnterPress: () => void;
  onCancelPress?: () => void; // Optional callback for cancel button
}

const OTCKeyboard = ({
  onKeyPress,
  onEnterPress,
  onCancelPress,
}: OTCKeyboardProps) => {
  const keys = [
    ["1", "2", "3"],
    ["4", "5", "6"],
    ["7", "8", "9"],
    [".", "0", "00"], // Restored original keys with "00"
  ];

  const handleKeyPress = (key: string) => {
    // Pass all keys to the parent component
    onKeyPress(key);
  };

  return (
    <View style={styles.container}>
      <View style={styles.keyboardContainer}>
        {keys.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.keyRow}>
            {row.map((key) => (
              <TouchableOpacity
                key={key}
                style={styles.key}
                onPress={() => handleKeyPress(key)}
              >
                {key === "←" ? (
                  <SvgXml xml={svg.backSpace} width={24} height={24} />
                ) : (
                  <Text style={styles.keyText}>{key}</Text>
                )}
              </TouchableOpacity>
            ))}
          </View>
        ))}
      </View>
      <View style={styles.buttonsContainer}>
        {/* Backspace Button */}
        <TouchableOpacity
          style={styles.backspaceButton}
          onPress={() => handleKeyPress("←")}
        >
          <SvgXml xml={svg.backSpace} width={24} height={24} />
        </TouchableOpacity>

        {/* Enter Button */}
        <TouchableOpacity
          style={styles.enterButton}
          onPress={onEnterPress}
        >
          <Text style={styles.enterButtonText}>Enter</Text>
        </TouchableOpacity>

        {/* Optional Cancel Button */}
        {onCancelPress && (
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={onCancelPress}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    width: "100%",
    justifyContent: "space-between",
  },
  keyboardContainer: {
    width: "75%",
  },
  keyRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: (4.5 / baseHeight) * height,
  },
  key: {
    width: (75 / baseWidth) * width,
    height: (50 / baseHeight) * height,
    backgroundColor: "#fff",
    borderRadius: 18,
    alignItems: "center",
    justifyContent: "center",
  },
  keyText: {
    fontSize: 18,
    color: colors.black,
    lineHeight: (27 / baseHeight) * height,
    fontFamily: fonts.poppinsMedium,
  },
  buttonsContainer: {
    width: "22%",
    height: ((4 * 50 + 3 * 4.5) / baseHeight) * height, // Height of 4 keys + 3 margins
    justifyContent: "space-between",
  },
  backspaceButton: {
    width: "100%",
    backgroundColor: colors.white,
    borderRadius: 18,
    alignItems: "center",
    justifyContent: "center",
    height: (50 / baseHeight) * height, // Same height as regular keys
    marginBottom: (4.5 / baseHeight) * height,
  },
  enterButton: {
    width: "100%",
    backgroundColor: colors.primary,
    borderRadius: 18,
    alignItems: "center",
    justifyContent: "center",
    height: ((3 * 50 + 2 * 4.5) / baseHeight) * height, // Height of 3 keys + 2 margins
  },
  enterButtonText: {
    fontSize: 16,
    color: "#fff",
    fontFamily: fonts.poppinsMedium,
  },
  cancelButton: {
    width: "100%",
    backgroundColor: colors.red,
    borderRadius: 18,
    alignItems: "center",
    justifyContent: "center",
    marginTop: (4.5 / baseHeight) * height,
    height: (50 / baseHeight) * height,
  },
  cancelButtonText: {
    fontSize: 16,
    color: "#fff",
    fontFamily: fonts.poppinsMedium,
  },
});

export default OTCKeyboard;
