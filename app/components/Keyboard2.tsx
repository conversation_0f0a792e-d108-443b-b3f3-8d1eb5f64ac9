import React, { useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from "react-native";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";


const baseHeight = 802
const baseWidth = 360
const {width, height} = Dimensions.get("window")
const Keyboard = ({ onKeyPress }) => {
  const keys = [
    ["1", "2", "3"],
    ["4", "5", "6"],
    ["7", "8", "9"],
    [".", "0", "←"],
  ];

  const handleKeyPress = (key) => {
    onKeyPress(key);
  };

  return (
    <View style={styles.keyboardContainer}>
      {keys.map((row, rowIndex) => (
        <View key={rowIndex} style={styles.keyRow}>
          {row.map((key) => (
            <TouchableOpacity
              key={key}
              style={styles.key}
              onPress={() => handleKeyPress(key)}
            >
              {key == "←" ? (
                <SvgXml xml={svg.backSpace} />
              ) : (
                <Text style={styles.keyText}>{key}</Text>
              )}
            </TouchableOpacity>
          ))}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  keyboardContainer: {
    width: "100%",
  },
  keyRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16 / baseHeight * height,
  },
  key: {
    width: 99 / baseWidth * width,
    height: 44 / baseHeight * height,
    backgroundColor: "#fff",
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  keyText: {
    fontSize: 18,
    color: "#000",
    lineHeight: 27 / baseHeight * height,
    fontFamily: fonts.poppinsMedium,
  },
});

export default Keyboard;
