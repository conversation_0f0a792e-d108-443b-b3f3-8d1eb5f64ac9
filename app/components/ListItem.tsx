import React from "react";
import { StyleSheet, View, Image } from "react-native";
import { colors } from "../config/colors";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "./P";
import { fonts } from "../config/Fonts";

interface PProps {
  image?: any;
  text1?: string;
  text2?: string;
  centerText?: string;
  rightComponent?: any;
}
export default function ListItem({
  image,
  text1,
  text2,
  centerText,
  rightComponent,
}: PProps) {
  return (
    <View style={styles.container}>
      {image && <Image source={image} style={styles.image} />}
      {!image && (
        <View
          style={{
            width: 32,
            height: 32,
            marginRight: 16,
            // backgroundColor: "red",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <SvgXml xml={svg.bell2} />
        </View>
      )}
      <View>
        {text1 && <P style={styles.text1}>{text1}</P>}
        {centerText && <P style={styles.text2}>{centerText}</P>}
        {text2 && <P style={styles.text2}>{text2}</P>}
      </View>
      {rightComponent && <View style={styles.rightCont}>{rightComponent}</View>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    height: "auto",
    padding: 16,
    backgroundColor: colors.white,
    borderRadius: 12,
    flexDirection: "row",
  },

  image: {
    width: 32,
    height: 32,
    borderRadius: 100,
    marginRight: 16,
  },
  text1: {
    fontSize: 12,
    lineHeight: 18,
    color: "#000",
    fontFamily: fonts.poppinsMedium,
    marginBottom: 4,
  },
  text2: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
    marginBottom: 4,
  },
  rightCont: {
    position: "absolute",
    right: 16,
    top: 16,
  },
});
