export const countries = [
  {
    country: "United States",
    flag: require("../assets/usa.png"),
    flagcode: "🇺🇸",
    countryCode: "+1",
    alphaCode: "USA",
    addRate: 1,
    sendRate: 1,
    currency: "US Dollar",
    symbol: "$",
    currencyCode: "USD",
    code2: "US",
    YellowCardCode: "US",
  },
  {
    country: "Turkey",
    flag: require("../assets/turkey.png"),
    flagcode: "🇹🇷",
    countryCode: "+90",
    alphaCode: "TUR",
    addRate: 1620,
    sendRate: 1620,
    currency: "Turkish Lira",
    symbol: "₺",
    currencyCode: "TRY",
    code2: "TR",
    YellowCardCode: "TR",
  },
  // {
  //   country: "North-Cyprus",
  //   flag: require("../assets/Cyprus.png"),
  //   flagcode: "🇹🇷",
  //   countryCode: "+90",
  //   alphaCode: "TUR",
  //   addRate: 1620,
  //   sendRate: 1620,
  //   currency: "Turkish Lira",
  //   symbol: "₺",
  //   currencyCode: "TRY", // Added currency code
  //   code2: "TRY",
  //   YellowCardCode: "TR",
  // },
  {
    country: "Nigeria",
    flag: require("../assets/nigeria.png"),
    flagcode: "🇳🇬",
    countryCode: "+234",
    code2: "NG",
    alphaCode: "NGA",
    addRate: 1620,
    sendRate: 1620,
    currency: "Nigerian Naira",
    symbol: "₦",
    currencyCode: "NGN",
    YellowCardCode: "NG",
  },
  {
    country: "South Africa",
    flag: require("../assets/sr.png"),
    flagcode: "🇿🇦",
    countryCode: "+27",
    alphaCode: "ZAF",
    code2: "ZA",
    addRate: 1620,
    sendRate: 1620,
    currency: "South African Rand",
    symbol: "R",
    currencyCode: "ZAR", // Added currency code
    YellowCardCode: "ZA",
  },
  {
    country: "Kenya",
    flag: require("../assets/kenya.png"),
    flagcode: "🇰🇪",
    countryCode: "+254",
    alphaCode: "KEN",
    code2: "KE",
    addRate: 1620,
    sendRate: 1620,
    currency: "Kenyan Shilling",
    symbol: "KSh",
    currencyCode: "KES", // Added currency code
    YellowCardCode: "KE",
  },
  {
    country: "Uganda",
    flag: require("../assets/uganda.png"),
    flagcode: "🇺🇬",
    countryCode: "+256",
    alphaCode: "UGA",
    code2: "UG",
    addRate: 1620,
    sendRate: 1620,
    currency: "Ugandan Shilling",
    symbol: "USh",
    currencyCode: "UGX", // Added currency code
    YellowCardCode: "UG",
  },
  {
    country: "Tanzania",
    flag: require("../assets/tanzania.png"),
    flagcode: "🇹🇿",
    countryCode: "+255",
    alphaCode: "TZA",
    code2: "TZ",
    addRate: 1620,
    sendRate: 1620,
    currency: "Tanzanian Shilling",
    symbol: "TSh",
    currencyCode: "TZS", // Added currency code
    YellowCardCode: "TZ",
  },
  {
    country: "Rwanda",
    flag: require("../assets/rwanda.png"),
    flagcode: "🇷🇼",
    countryCode: "+250",
    alphaCode: "RWA",
    code2: "RW",
    addRate: 1620,
    sendRate: 1620,
    currency: "Rwandan Franc",
    symbol: "FRw",
    currencyCode: "RWF", // Added currency code
    YellowCardCode: "RW",
  },
  {
    country: "Zambia",
    flag: require("../assets/zambia.png"),
    flagcode: "🇿🇲",
    countryCode: "+260",
    alphaCode: "ZMB",
    code2: "ZM",
    addRate: 1620,
    sendRate: 1620,
    currency: "Zambian Kwacha",
    symbol: "ZK",
    currencyCode: "ZMW", // Added currency code
    YellowCardCode: "ZM",
  },
  {
    country: "Ghana",
    flag: require("../assets/ghana.png"),
    flagcode: "🇬🇭",
    countryCode: "+233",
    alphaCode: "GHA",
    code2: "GH",
    addRate: 1620,
    sendRate: 1620,
    currency: "Ghanaian Cedi",
    symbol: "₵",
    currencyCode: "GHS",
    YellowCardCode: "GH",
  },
  {
    country: "Cameroon",
    flag: require("../assets/cameroon.png"),
    flagcode: "🇨🇲",
    countryCode: "+237",
    alphaCode: "CMR",
    code2: "CM",
    addRate: 1620,
    sendRate: 1620,
    currency: "Cameroon",
    symbol: "FCFA",
    currencyCode: "XAF", // Added currency code
    YellowCardCode: "CM",
  },
  {
    country: "Botswana",
    flag: require("../assets/botswana.png"),
    flagcode: "🇧🇼",
    countryCode: "+267",
    alphaCode: "BWA",
    code2: "BW",
    addRate: 1620,
    sendRate: 1620,
    currency: "Botswana Pula",
    symbol: "P",
    currencyCode: "BWP", // Added currency code
    YellowCardCode: "BW",
  },
  {
    country: "Malawi",
    flag: require("../assets/malawi.webp"),
    flagcode: "🇲🇼",
    countryCode: "+265",
    alphaCode: "MWI",
    code2: "MW",
    addRate: 1620,
    sendRate: 1620,
    currency: "Malawian Kwacha",
    symbol: "MK",
    currencyCode: "MWK",
    YellowCardCode: "MW",
  },
  {
    country: "Gabon",
    flag: require("../assets/gabon.webp"),
    flagcode: "🇬🇦",
    countryCode: "+241",
    alphaCode: "GAB",
    code2: "GA",
    addRate: 1620,
    sendRate: 1620,
    currency: "Central African CFA Franc",
    symbol: "FCFA",
    currencyCode: "XAF",
    YellowCardCode: "GA",
  },
  {
    country: "Congo_Brazzaville",
    flag: require("../assets/congo.png"),
    flagcode: "🇨🇬",
    countryCode: "+242",
    alphaCode: "COG",
    code2: "CG",
    addRate: 1620,
    sendRate: 1620,
    currency: "Congo",
    symbol: "FCFA",
    currencyCode: "XAF",
    YellowCardCode: "CG",
  },
  {
    country: "DR Congo",
    flag: require("../assets/drc.png"),
    flagcode: "🇨🇩",
    countryCode: "+243",
    alphaCode: "COD",
    code2: "CD",
    addRate: 2000,
    sendRate: 2000,
    currency: "Congolese Franc",
    symbol: "CDF",
    currencyCode: "CDF",
    YellowCardCode: "CD",
  },
];
