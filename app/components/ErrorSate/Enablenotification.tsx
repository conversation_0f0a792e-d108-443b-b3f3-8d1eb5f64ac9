import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import { SvgXml } from "react-native-svg";
import P from "../P";
import Button from "../Button";
import { colors } from "../../config/colors";
import { fonts } from "../../config/Fonts";
import { svg } from "../../config/Svg";
import { UpdateUser } from "../../RequestHandlers/User";
import Link from "../Link";
import { useToast } from "../../context/ToastContext";

const { width, height } = Dimensions.get("window");

interface PProps {
  onclose?: any;
}
export default function EnableNotification({ onclose }: PProps) {
  const {handleToast} = useToast()
  const [loading, setLoading] = useState(false);
  const handleEnableNotification = async () => {
    setLoading(true);
    try {
      const body = { pushnotification: true };
      const res = await UpdateUser(body);
      if (res.message === "User Updated Successfully!") {
        handleToast("Notification status updated", "success");
        onclose();
      } else {
        handleToast(res.message, "error");
      }
    } catch (error) {
    } finally {
      setLoading(true);
    }
  };
  return (
    <View style={styles.reqContainer}>
      <TouchableOpacity
        style={{ position: "absolute", top: 100, right: "5%" }}
        onPress={() => {
          onclose();
        }}
      >
        <SvgXml xml={svg.xClose} />
      </TouchableOpacity>
      <SvgXml
        xml={svg.bignotification}
        style={{ marginBottom: 16 }}
      />
      <P>Notification permission</P>
      <Text style={styles.message}>
        Stay informed! Get real-time updates on{"\n"}transactions, offers and
        security alerts
      </Text>
      <View style={{ width: "50%", marginTop: 32 }}>
        <Button
          loading={loading}
          btnText="Enable"
          onPress={handleEnableNotification}
        />
        <Link
          onPress={() => {
            onclose();
          }}
          style={{
            textAlign: "center",
            marginTop: 16,
            fontSize: 12,
          }}
        >
          Skip for now
        </Link>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  reqContainer: {
    width,
    position: "absolute",
    alignSelf: "center",
    height: (100 * height) / 100,
    backgroundColor: colors.white,
    alignItems: "center",
    paddingTop: 200,
    // justifyContent: "center",
    flex: 1,
  },
  container: {
    flex: 1,
    justifyContent: "center",
    position: "absolute",
  },
  message: {
    textAlign: "center",
    fontSize: 12,
    marginTop: 4,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
  },
  camera: {
    flex: 1,
    alignItems: "center",
  },
});
