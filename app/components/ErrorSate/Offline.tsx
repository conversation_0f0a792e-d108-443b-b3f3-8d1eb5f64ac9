import React from "react";
import { Dimensions, StyleSheet, TouchableOpacity, View } from "react-native";
import P from "../P";
import { fonts } from "../../config/Fonts";
import { color } from "react-native-reanimated";
import { colors } from "../../config/colors";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";

interface PProps {
  subText?: string;
  onPress?: any;
}
const { width, height } = Dimensions.get("window");
export default function Offline({ subText, onPress }: PProps) {
  return (
    <View style={styles.cont}>
      <SvgXml xml={svg.noInternet} />
      <View style={{ marginTop: 16 }}>
        <P style={{ textAlign: "center" }}>No internet connection!</P>
        <P
          style={{
            fontSize: 12,
            fontFamily: fonts.poppinsRegular,
            color: colors.gray,
            lineHeight: 18,
            textAlign: "center",
            marginTop: 4,
          }}
        >
          Please check your connection{"\n"}and try again
        </P>
      </View>
      <TouchableOpacity onPress={onPress} style={styles.TryBtn}>
        <P style={{ fontSize: 12, color: colors.primary }}>
          Try again
        </P>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  cont: {
    width: (90 * width) / 100,
    height: (60 * height) / 100,
    alignItems: "center",
    justifyContent: "center",
    // backgroundColor: "red",
    alignSelf: "center",
  },
  TryBtn: {
    width: 89,
    height: 32,
    borderWidth: 1,
    borderColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 16,
    borderRadius: 100,
  },
});
