import React, { useEffect, useState } from "react";
import { StyleSheet, View, Animated, Image } from "react-native";
import P from "../P";
import { colors } from "../../config/colors";
import NetInfo from "@react-native-community/netinfo";
import { stat } from "react-native-fs";
interface PProps {
  isOffLine?: boolean;
}
export default function OffLineToast({ isOffLine }: PProps) {
  const [isVisible, setIsVisible] = useState(false);
  const slideAnim = new Animated.Value(-200);
  useEffect(() => {
    Animated.timing(slideAnim, {
      toValue: isVisible ? 0 : -200,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, [isVisible]);
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      if (state.isConnected === false) {
        setIsVisible(true);
        console.log("offline");
      } else if (state.isConnected === true) {
        setIsVisible(false);
        console.log("online");
      }
    });
  }, []);
  return isVisible ? (
    <Animated.View
      style={[styles.offLinePromt, { transform: [{ translateY: slideAnim }] }]}
    >
      <Image
        source={require("../../../assets/icon.png")}
        style={{ width: 20, height: 20 }}
        borderRadius={3}
      />
      <P
        style={{
          fontSize: 10,
          color: colors.black,
        }}
      >
        SFx is Offline
      </P>
    </Animated.View>
  ) : (
    <></>
  );
}

const styles = StyleSheet.create({
  offLinePromt: {
    paddingLeft: 16,
    paddingRight: 16,
    paddingBottom:8,
    paddingTop:8,
    backgroundColor: colors.stroke,
    position: "absolute",
    alignSelf: "center",
    zIndex: 1000000,
    alignItems: "center",
    justifyContent: "center",
    top: 60,
    borderRadius: 100,
    elevation: 5,
    shadowColor: colors.gray,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.5,
    shadowRadius: 4,
    gap: 10,
    flexDirection: "row",
  },
});
