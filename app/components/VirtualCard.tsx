import React, { useState, useRef, CSSProperties } from "react";
import {
  View,
  ImageBackground,
  Dimensions,
  Image,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from "react-native";
import P from "./P";
import { fonts } from "../config/Fonts";
import { colors } from "../config/colors";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import { Grayscale } from "react-native-color-matrix-image-filters";

interface PProps {
  cardColor?: string;
  name?: string;
  cardNumber?: any;
  ccStyle?: CSSProperties;
  contStyle?: CSSProperties;
  CardText1?: CSSProperties;
  CardText2?: CSSProperties;
  cvv?: number;
  validDate?: string;
  isCardFreezed?: Boolean;
}

const baseHeight = 800;
const { width, height } = Dimensions.get("window");

export default function VirtualCard({
  cardColor,
  name,
  cardNumber,
  ccStyle,
  contStyle,
  CardText1,
  CardText2,
  cvv = 123,
  validDate = "06/24",
  isCardFreezed,
}: PProps) {
  const [flipCard, setFlipCard] = useState(false);
  const [hideNum, setHideNum] = useState(true);

  const flipAnimation = useRef(new Animated.Value(0)).current;

  const frontInterpolate = flipAnimation.interpolate({
    inputRange: [0, 180],
    outputRange: ["0deg", "180deg"],
  });

  const backInterpolate = flipAnimation.interpolate({
    inputRange: [0, 180],
    outputRange: ["180deg", "360deg"],
  });

  const frontOpacity = flipAnimation.interpolate({
    inputRange: [89, 90],
    outputRange: [1, 0],
  });

  const backOpacity = flipAnimation.interpolate({
    inputRange: [89, 90],
    outputRange: [0, 1],
  });

  const flipToBack = () => {
    Animated.spring(flipAnimation, {
      toValue: 180,
      friction: 8,
      tension: 10,
      useNativeDriver: true,
    }).start();
  };

  const flipToFront = () => {
    Animated.spring(flipAnimation, {
      toValue: 0,
      friction: 8,
      tension: 10,
      useNativeDriver: true,
    }).start();
  };

  const toggleCardFlip = () => {
    if (flipCard) {
      flipToFront();
    } else {
      flipToBack();
    }
    setFlipCard(!flipCard);
  };

  return (
    <View
      style={[
        {
          width: "95%",
          alignItems: "center",
          marginTop: (24 / baseHeight) * height,
          marginBottom: (24 / baseHeight) * height,
        },
        // @ts-ignore
        contStyle,
      ]}
    >
      <Animated.View
        style={[
          styles.card,
          // @ts-ignore
          ccStyle,
          {
            transform: [{ rotateY: frontInterpolate }],
            opacity: frontOpacity,
            zIndex: flipCard ? 0 : 1,
          },
        ]}
      >
        <ImageBackground
          // @ts-ignore
          style={[styles.card, { opacity: isCardFreezed ? 0.5 : 1 }, ccStyle]}
          borderRadius={12}
          resizeMode="cover"
          source={
            cardColor === "purple"
              ? require("../assets/Cards/pCardFront.png")
              : cardColor === "brown"
              ? require("../assets/Cards/bCardFront.png")
              : cardColor === "red"
              ? require("../assets/Cards/rCardFront.png")
              : cardColor === "green"
              ? require("../assets/Cards/gCardFront.png")
              : require("../assets/Cards/pCardFront.png")
          }
        >
          <View style={{ width: "90%", height: "100%" }}>
            <Image
              source={require("../assets/Cards/Sfxl.png")}
              style={{ width: 56, height: 20 }}
            />
            <View style={{ marginTop: (40 / baseHeight) * height }}>
              <View>
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                    width: "50%",
                    // backgroundColor: "red",
                  }}
                >
                  {/* @ts-ignore */}

                  <P style={[styles.ownerName, CardText1]}>{name}</P>
                  <P
                    // @ts-ignore
                    style={[
                      styles.flipCard,
                      { fontFamily: fonts.poppinsRegular },
                      // @ts-ignore
                      CardText1,
                    ]}
                  >
                    {hideNum ? "*****" : validDate}
                  </P>
                </View>
                {/* @ts-ignore */}
                <P style={[styles.cardNumber, CardText2]}>
                  {hideNum ? "**** **** **** ****" : cardNumber}
                </P>
                <View style={{ position: "absolute", right: "40%" }}>
                  <TouchableOpacity
                    onPress={() => setHideNum(!hideNum)}
                    style={{
                      zIndex: 100,
                      width: 20,
                      height: 20,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <SvgXml
                      xml={hideNum ? svg.eyeCloseWhite : svg.eyeOpenWhite}
                      width={16}
                      height={16}
                    />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            <View style={styles.cardBottom}>
              <TouchableOpacity
                style={{ flexDirection: "row", alignItems: "center" }}
                onPress={toggleCardFlip}
              >
                <SvgXml xml={svg.flipCard} style={{ marginRight: 4 }} />
                {/* @ts-ignore */}
                <P style={[styles.flipCard, CardText1]}>Flip card</P>
              </TouchableOpacity>
              <SvgXml xml={svg.payPass} />
            </View>
          </View>
        </ImageBackground>
        {/* @ts-ignore */}
      </Animated.View>

      <Animated.View
        style={[
          styles.card,
          styles.backCard,
          {
            transform: [{ rotateY: backInterpolate }],
            opacity: backOpacity,
          },
          // @ts-ignore
          ccStyle,
        ]}
      >
        <ImageBackground
          // @ts-ignore
          style={[styles.card, { opacity: isCardFreezed ? 0.5 : 1 }, ccStyle]}
          borderRadius={12}
          resizeMode="cover"
          source={
            cardColor === "purple"
              ? require("../assets/Cards/pCardBack.png")
              : cardColor === "brown"
              ? require("../assets/Cards/bCardBack.png")
              : cardColor === "red"
              ? require("../assets/Cards/rCardBack.png")
              : cardColor === "green"
              ? require("../assets/Cards/gCardBack.png")
              : require("../assets/Cards/pCardBack.png")
          }
        >
          <View style={{ width: "90%", height: "100%" }}>
            {/* @ts-ignore */}
            <P style={[styles.flipCard, CardText1]}>www.sfxchange.co</P>
            <View style={{ position: "absolute", bottom: 39 }}>
              <P
                // @ts-ignore
                style={[
                  styles.flipCard,
                  { fontFamily: fonts.poppinsRegular },
                  // @ts-ignore
                  CardText1,
                ]}
              >
                This card can only be used for online{"\n"}shopping or online
                POS
              </P>
            </View>
            <View style={styles.cardBottom}>
              <TouchableOpacity
                style={{ flexDirection: "row", alignItems: "center" }}
                onPress={toggleCardFlip}
              >
                <SvgXml xml={svg.flipCard} style={{ marginRight: 4 }} />
                <P style={styles.flipCard}>Flip card</P>
              </TouchableOpacity>
              <View style={{ flexDirection: "row", marginRight: 16 }}>
                <P
                  // @ts-ignore
                  style={[
                    styles.flipCard,
                    { fontFamily: fonts.poppinsRegular, marginRight: 16 },
                    // @ts-ignore
                    CardText1,
                  ]}
                >
                  Valid : {hideNum ? "*****" : validDate}
                </P>
                <P
                  // @ts-ignore
                  style={[
                    styles.flipCard,
                    { fontFamily: fonts.poppinsRegular },
                    // @ts-ignore
                    CardText1,
                  ]}
                >
                  CVV: {hideNum ? "***" : cvv}
                </P>
              </View>
            </View>
          </View>
        </ImageBackground>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    width: "100%",
    height: (158 / baseHeight) * height,
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "center",
    paddingTop: 16,
    paddingBottom: 16,
    backfaceVisibility: "hidden",
  },
  backCard: {
    position: "absolute",
    top: 0,
  },
  ownerName: {
    fontSize: 8,
    lineHeight: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.white,
  },
  flipCard: {
    fontSize: 8,
    lineHeight: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.white,
    marginTop: -2,
  },
  cardBottom: {
    position: "absolute",
    bottom: 0,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
  },
  cardNumber: {
    fontSize: 13,
    lineHeight: 20,
    fontFamily: fonts.poppinsRegular,
    color: colors.white,
  },
});
