import React, { useState } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  TextInput,
} from "react-native";
import P from "./P";
import { svg } from "../config/Svg";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import ListItemSelect from "./ListItemSelect";
import { SvgXml } from "react-native-svg";
import { countries } from "./counties";
import { Identification } from "./Identification";
import ListItemSelect2 from "./ListItemSelect2";

interface PProps {
  onPress?: any;
}

const { width, height } = Dimensions.get("window");

export default function IdentificationSelect({ onPress }: PProps) {
  const [activeType, setActiveType] = useState(null);
  const [searchQuery, setSearchQuery] = useState<string>("");

  const filteredCountries = Identification;
  return (
    <View style={styles.viewContent}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {filteredCountries.map((item, index) => (
          <ListItemSelect2
            text1={item}
            image={""}
            key={index}
            isActive={activeType === index}
            onPress={() => {
              setActiveType(index);
              setTimeout(() => {
                onPress(index);
              }, 200);
            }}
            containerStyle={{
              marginBottom: index === filteredCountries.length - 1 ? 300 : 16,
            }}
          />
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  viewContent: {
    width: "100%",
    paddingTop: 24,
  },
  search: {
    width: "100%",
    height: (5.5 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    marginBottom: 24,
    borderWidth: 1,
    borderRadius: 99,
    borderColor: colors.stroke,
    padding: 12,
    paddingLeft: 14,
    paddingRight: 14,
    flexDirection: "row",
  },
  searchInput: {
    width: "90%",
    height: 24,
    // backgroundColor: 'red',
    fontFamily: fonts.poppinsRegular,
    lineHeight: 24,
    fontSize: 14,
    // paddingLeft: 16,
  },
});
