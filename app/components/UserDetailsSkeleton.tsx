import React from 'react';
import ContentLoader, { Rect } from 'react-content-loader/native';
import { View, StyleSheet } from 'react-native';
import { colors } from '../config/colors';

const UserDetailsSkeleton = () => (
  <View style={styles.container}>
    <ContentLoader 
      width={400}
      height={20}
      backgroundColor={colors.stroke}
      foregroundColor={colors.secBackground}
    >
      {/* Full name skeleton */}
      <Rect x="0" y="0" rx="5" ry="5" width="200" height="20" />
      {/* Username skeleton */}
      {/* <Rect x="0" y="40" rx="5" ry="5" width="150" height="20" /> */}
    </ContentLoader>
  </View>
);

const styles = StyleSheet.create({
  container: {
    marginBottom: 4,
  },
});

export default UserDetailsSkeleton; 