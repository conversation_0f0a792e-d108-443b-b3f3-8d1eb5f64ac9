import type { ViewProps } from 'react-native';
import type { DirectEventHandler } from 'react-native/Libraries/Types/CodegenTypes';
import codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';
import type { HostComponent } from 'react-native';

export interface SmileIDConsentViewProps extends ViewProps {
  onResult?: DirectEventHandler<{
    result: string;
    error?: string;
  }>;
}

export default codegenNativeComponent<SmileIDConsentViewProps>(
  'SmileIDConsentView'
) as HostComponent<SmileIDConsentViewProps>;
