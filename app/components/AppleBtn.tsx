import React, { useState, CSSProperties, useEffect, useContext } from "react";
import { View, TouchableOpacity, StyleSheet, Dimensions } from "react-native";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "./P";
import { colors } from "../config/colors";
import i18n from "../../i18n";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import {
  ValidateAppleToken,
  ValidateGoogleToken,
} from "../RequestHandlers/Authentication";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "../RequestHandlers/CredentailsContext";
import * as AppleAuthentication from "expo-apple-authentication";
import { appleAuth } from "@invertase/react-native-apple-authentication";
import { useToast } from "../context/ToastContext";
import { withApiErrorToast } from "../Utils/withApiErrorToast";

interface PProps {
  contStyle?: CSSProperties;
  onPress?: any;
  navigation?: any;
}
const baseHeight = 800;
const { width, height } = Dimensions.get("window");
export default function AppleBtn({ contStyle, onPress, navigation }: PProps) {
  const { handleToast } = useToast();
  const [activeDot, setActiveDot] = useState(0);
  const [loading, setLoading] = useState(false);
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);
  const dotsArray = [1, 2, 3];
  useEffect(() => {
    if (loading) {
      const interval = setInterval(() => {
        setActiveDot((prevDot) => (prevDot + 1) % dotsArray.length);
      }, 200);
      return () => clearInterval(interval);
    }
  }, [loading]);

  const [isAppleSupported, setIsAppleSupported] = useState(false);
  useEffect(() => {
    if (appleAuth.isSupported === true) {
      setIsAppleSupported(true);
    } else {
      setIsAppleSupported(false);
    }
  }, []);
  const persistLogin = (credentail: any, idToken: string) => {
    AsyncStorage.setItem("cookies", JSON.stringify(credentail))
      .then(() => {
        // @ts-ignore
        setStoredCredentails(credentail);
      })
      .catch((err) => {
        console.error("Error in Apple login process:", err);
      });
  };
  const persistLogin2 = (credentail: any, idToken: string) => {
    AsyncStorage.setItem("cookies", JSON.stringify(credentail))
      .then(() => {
        console.log("Credentials saved to AsyncStorage");
        // @ts-ignore
        setStoredCredentails(credentail);
        setTimeout(() => {
          navigation.reset({
            index: 0,
            routes: [
              {
                name: "BottomTabNavigator",
              },
            ],
          });
        }, 500);
      })
      .catch((err) => {
        console.error("Error in login process:", err);
      });
  };
  const validateAppleToken = async (firstname, lastname, idToken) => {
    try {
      const validateToken = await withApiErrorToast(ValidateAppleToken({
        firstname: firstname,
        lastname: lastname,
        idToken: idToken,
      }), handleToast);
      AsyncStorage.setItem(
        "AppleLogin",
        JSON.stringify({
          firstname: firstname,
          lastname: lastname,
          idToken: idToken,
        })
      )
        .then(() => console.log("Apple token saved successfully!"))
        .catch((error) => console.error("Error saving Google token:", error));
      if (validateToken.newAccount === true) {
        await AsyncStorage.removeItem("hasLoggedInBefore");
        await AsyncStorage.setItem("newUser", "true");
      } else {
        await AsyncStorage.setItem("hasLoggedInBefore", "true");
      }
      if (validateToken.token) {
        if (validateToken.user._2faEnabled === true) {
          navigation.reset({
            index: 0,
            routes: [
              {
                name: "TwofactorAuthScreen2",
                params: {
                  type: "login",
                  tkn: validateToken.token,
                  ActivityFunction: () => persistLogin2(validateToken, idToken),
                },
              },
            ],
          });
        } else {
          setTimeout(() => {
            persistLogin(validateToken, idToken);
          }, 500);
        }
      } else {
        handleToast(validateToken.message, "error");
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const SignIn = async () => {
    try {
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });
      if (credential.identityToken) {
        setLoading(true);
        validateAppleToken(
          credential.fullName.familyName,
          credential.fullName.givenName,
          credential.identityToken
        );
      }
    } catch (e) {
      if (e.code === "ERR_REQUEST_CANCELED") {
        handleToast("Authentication canceled", "error");
      } else {
        handleToast(e.message, "error");
      }
    }
  };
  return (
    // @ts-ignore
    <View style={[{ alignItems: "center" }, contStyle]}>
      <TouchableOpacity
        style={{
          flexDirection: "row",
          alignItems: "center",
          width: "100%",
          justifyContent: "center",
          borderWidth: 1,
          borderColor: colors.stroke,
          borderRadius: 99,
          height: (44 / baseHeight) * height,
        }}
        onPress={SignIn}
      >
        {loading ? (
          <View style={styles.loaderContainer}>
            {dotsArray.map((dot, index) => (
              <View
                key={dot}
                style={[
                  styles.dot,
                  {
                    backgroundColor:
                      activeDot === index ? colors.black : colors.gray,
                  },
                ]}
              />
            ))}
          </View>
        ) : (
          <>
            <SvgXml xml={svg.Applelogo} style={{ marginRight: 4 }} />
            <P style={{ color: colors.black, fontSize: 12 }}>
              {i18n.t("apple")}
            </P>
          </>
        )}
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  loaderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: 30, // Space for the 3 dots
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 100,
    backgroundColor: colors.gray, // Default inactive color
    marginHorizontal: 2,
  },
});
