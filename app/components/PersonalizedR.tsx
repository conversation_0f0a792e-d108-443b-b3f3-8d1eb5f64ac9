import React, { useState, useEffect, useRef } from "react";
import {
  View,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableOpacity,
  ImageBackground,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { SvgXml } from "react-native-svg";
import AsyncStorage from "@react-native-async-storage/async-storage"; // Install if not already
import { colors } from "../config/colors";
import { svg } from "../config/Svg";
import P from "./P";
import { fonts } from "../config/Fonts";
import { GetUserDetails } from "../RequestHandlers/User";
import { TouchableWithoutFeedback } from "react-native-gesture-handler";
import { useToast } from "../context/ToastContext";
import { withApiErrorToast } from "../Utils/withApiErrorToast";

interface PProps {
  isCardRegisterd?: true | false;
  navigation: any;
  countdownSeconds?: number;
}
const isTablet = Dimensions.get("window").width >= 600;
const { width, height } = Dimensions.get("window");
export default function PersonalizedR({
  navigation,
  countdownSeconds,
}: PProps) {
  const [isCardRegisterd, setIsCardRegisterd] = useState(false);
  const { handleToast } = useToast();
  const [isReferralProgram, setIsReferralProgram] = useState(true);

  const getUserDetails = async () => {
    try {
      const userDetails = await withApiErrorToast(
        GetUserDetails(),
        handleToast
      );
      if (userDetails?.cards?.length === 0) {
        setIsCardRegisterd(false);
      } else {
        setIsCardRegisterd(true);
      }
    } catch (error) {}
  };
  const [frameData, setFrameData] = useState([
    {
      id: "1", // Add unique IDs for tracking
      text2: "Refer and earn SFx point on every eligible referral today!",
      image: require("../assets/image2.png"),
      url: isReferralProgram
        ? "ReferralContestScreen"
        : "ReferralProgramScreen",
      icon: svg.referBrown,
      close: true,
      background: require("../assets/prFrame/pr3.png"),
      closeIcon: svg.cancelBrown,
      textColor: "#FDE0D3",
    },
    {
      id: "2",
      text2: "Send USDC to your local bank account or mobile money",
      image: require("../assets/image2.png"),
      url: "SendMoneyScreen",
      icon: svg.walletGreen,
      close: true,
      background: require("../assets/prFrame/pr1.png"),
      closeIcon: svg.cancelGreen,
      textColor: "#D3FDE7",
    },
    // {
    //   id: "3",
    //   text2: "Get your SFx virtual MasterCard now and start shopping!",
    //   image: require("../assets/image2.png"),
    //   url: "CardGetStartedScreen",
    //   icon: svg.redCard,
    //   close: true,
    //   background: require("../assets/prFrame/pr2.png"),
    //   closeIcon: svg.cancelRed,
    //   textColor: "#FDD3D4",
    // },
    {
      id: "4",
      text2: "Add USDC to your wallet with your local currency",
      image: require("../assets/image2.png"),
      url: "AddMoneyScreen",
      icon: svg.walletGreen,
      close: false,
      background: require("../assets/prFrame/pr1.png"),
      closeIcon: svg.cancelGreen,
      textColor: "#D3FDE7",
    },
  ]);

  const [activeIndex, setActiveIndex] = useState(0);
  const flatListRef = useRef(null);
  const screenWidth = Dimensions.get("window").width;

  useEffect(() => {
    getUserDetails();
    // Load removed items from storage on component mount
    const loadRemovedItems = async () => {
      const removedData = await AsyncStorage.getItem("removedItems");
      const removedItems = removedData ? JSON.parse(removedData) : [];

      // Filter out removed items
      const now = Date.now();
      const validRemovedItems = removedItems.filter(
        (item) => now - item.timestamp < 24 * 60 * 60 * 1000 // 24 hours
      );

      const validRemovedIds = validRemovedItems.map((item) => item.id);
      const updatedFrameData = frameData.filter(
        (item) => !validRemovedIds.includes(item.id)
      );

      setFrameData(updatedFrameData);

      // Save valid removed items back to storage
      await AsyncStorage.setItem(
        "removedItems",
        JSON.stringify(validRemovedItems)
      );
    };

    loadRemovedItems();
  }, []);

  useEffect(() => {
    if (frameData.length > 1) {
      const intervalId = setInterval(() => {
        const nextIndex =
          activeIndex === frameData.length - 1 ? 0 : activeIndex + 1;
        setActiveIndex(nextIndex);

        flatListRef.current?.scrollToIndex({
          index: nextIndex,
          animated: nextIndex !== 0, // No animation when resetting to the first item
        });
      }, 5000);

      return () => clearInterval(intervalId);
    }
  }, [activeIndex, frameData.length]);

  const handleRemoveItem = async (indexToRemove) => {
    const removedItem = frameData[indexToRemove];

    setFrameData((prevData) => {
      const updatedData = prevData.filter(
        (_, index) => index !== indexToRemove
      );

      // Ensure activeIndex is valid
      if (activeIndex >= updatedData.length) {
        setActiveIndex((prevIndex) => Math.max(0, prevIndex - 1));
      }

      return updatedData;
    });

    // Save removed item with timestamp
    const removedData = await AsyncStorage.getItem("removedItems");
    const removedItems = removedData ? JSON.parse(removedData) : [];
    removedItems.push({ id: removedItem.id, timestamp: Date.now() });
    await AsyncStorage.setItem("removedItems", JSON.stringify(removedItems));
  };

  const renderItem = ({ item, index }) => (
    <TouchableOpacity
      onPress={() => {
        if (item.id === "3" && isCardRegisterd === true) {
          navigation.navigate("CardScreen");
        } else if (item.id === "1" && isReferralProgram) {
          navigation.navigate(item.url, { countdownSeconds: countdownSeconds });
        } else {
          navigation.navigate(item.url);
        }
      }}
    >
      <View style={{ width, alignItems: "center" }}>
        <ImageBackground
          source={item.background}
          resizeMode="cover"
          style={[
            styles.card,
            {
              width: screenWidth * 0.9,
              marginBottom: frameData.length === 1 ? 24 : 8,
            },
          ]}
        >
          <View style={styles.recommendation}>
            <SvgXml xml={item?.icon} style={{ marginRight: 8 }} />
            <View style={styles.recommendationText}>
              <P
                // @ts-ignore
                style={[
                  styles.recommendationMessage,
                  { width: 210, color: item.textColor },
                ]}
              >
                {item.text2}
              </P>
            </View>
          </View>
          {item.close && (
            <View
              style={{
                position: "absolute",
                alignSelf: "center",
                justifyContent: "center",
                right: 8,
                top: 8,
              }}
            >
              <TouchableOpacity onPress={() => handleRemoveItem(index)}>
                <SvgXml xml={item?.closeIcon} />
              </TouchableOpacity>
            </View>
          )}
        </ImageBackground>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={{ width, alignSelf: "center" }}>
      <FlatList
        data={frameData}
        ref={flatListRef}
        renderItem={renderItem}
        horizontal
        pagingEnabled
        keyExtractor={(item) => item.id}
        showsHorizontalScrollIndicator={false}
        scrollEnabled={frameData.length > 1}
        onScrollToIndexFailed={() => {}}
        onMomentumScrollEnd={(event) => {
          const index = Math.round(
            event.nativeEvent.contentOffset.x / screenWidth
          );
          setActiveIndex(index);
        }}
      />
      {frameData.length > 1 && (
        <View style={styles.indicatorContainer}>
          {frameData.map((_, index) => (
            <View
              key={index}
              style={[
                styles.indicator,
                {
                  backgroundColor: colors.primary,
                  opacity: activeIndex === index ? 1 : 0.5,
                },
              ]}
            />
          ))}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    // marginRight: "5%",
    backgroundColor: "rgba(238, 255, 235, 1)",
    borderRadius: 12,
    minHeight: 80,
    paddingLeft: 8,
    // padding: 16,
    marginBottom: 8,
    flexDirection: "row",
    alignItems: "center",
    overflow: "hidden",
  },
  recommendation: {
    flexDirection: "row",
    alignItems: "center",
  },
  recommendationText: {},
  recommendationMessage: {
    fontSize: 12,
    color: colors.white,
    width: 218,
    fontFamily: fonts.poppinsSemibold,
  },
  indicatorContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 24,
  },
  indicator: {
    width: 7,
    height: 7,
    marginRight: 5,
    borderRadius: 100,
  },
});
