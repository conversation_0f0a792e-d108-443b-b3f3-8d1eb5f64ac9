import {
  StyleSheet,
  Text,
  View,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import React, { useEffect, useState } from "react";
import { CSSProperties } from "react";
import { fonts } from "../config/Fonts";
import { colors } from "../config/colors";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import Link from "./Link";
import P from "./P";
const { width, height } = Dimensions.get("window");

interface PProps {
  setResend?: () => void;
}

export default function ResendOtp({ setResend }: PProps) {
  const [timeLeft, setTimeLeft] = useState(60); // 6 minutes in seconds
  const [count, setCount] = useState(true);

  useEffect(() => {
    if (timeLeft > 0) {
      const timerId = setInterval(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);

      return () => clearInterval(timerId);
    }
  }, [timeLeft]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? "0" : ""}${remainingSeconds}`;
  };
  useEffect(() => {
    if (timeLeft == 0) {
      setCount(true);
    }
  }, [timeLeft]);

  useEffect(() => {
    setCount(false);
    setTimeLeft(60);
  }, []);
  return (
    <View
      // @ts-ignore
      style={[styles.con]}
    >
      {count ? <SvgXml xml={svg.error} /> : <SvgXml xml={svg.good} />}
      <P
        style={{
          fontSize: 14,
          fontFamily: fonts.poppinsRegular,
          lineHeight: 21,
          marginLeft: 8,
        }}
      >
        Didn’t get the code?{" "}
      </P>
      {count ? (
        <Link
          onPress={() => {
            setCount(false);
            setTimeLeft(60);
            setResend();
          }}
          style={{
            fontSize: 14,
            lineHeight: 21,
            color: "#CC2125",
            textDecorationLine: "underline",
            textDecorationColor: "#A5A1A1",
          }}
        >
          Resend
        </Link>
      ) : (
        <P style={{ fontSize: 14, lineHeight: 21, color: "#22C26E" }}>
          {" "}
          {formatTime(timeLeft)}
        </P>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  con: {
    width: "100%",
    height: 44,
    borderRadius: 8,
    backgroundColor: "#F1EBFF",
    marginBottom: 32,
    flexDirection: "row",
    alignItems: "center",
    paddingLeft: 11,
  },
});
