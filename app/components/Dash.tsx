import { Dimensions, StyleSheet, View, ViewStyle } from "react-native";
import { colors } from "../config/colors";

const { width, height } = Dimensions.get("window");

interface PProps {
  style?: ViewStyle;
}
export default function Dash({ style }: PProps) {
  return <View style={[styles.dash, style]}></View>;
}

const styles = StyleSheet.create({
  dash: {
    width: "100%",
    height: 1,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    marginTop: (2.7 * height) / 100,
  },
});
