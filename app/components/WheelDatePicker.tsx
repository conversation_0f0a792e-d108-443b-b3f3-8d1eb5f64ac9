import React, { useState, useEffect, useRef } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Text,
  Dimensions,
  Platform,
  SafeAreaView,
  ScrollView,
  NativeSyntheticEvent,
  NativeScrollEvent,
  Animated,
} from "react-native";
import DatePicker from "react-native-date-picker";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import P from "./P";

const { height, width } = Dimensions.get("window");

// Month names for conversion
const monthNames = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

interface DatePickerProps {
  isVisible?: boolean;
  onClose?: () => void;
  onDateSelected: (date: { month: string; day: number; year: number }) => void;
  initialDate?: { month: string; day: number; year: number };
  useModal?: boolean;
  dateOfBirth?: boolean;
}

const WheelDatePicker: React.FC<DatePickerProps> = ({
  isVisible = true,
  onClose = () => { },
  onDateSelected,
  initialDate,
  useModal = true,
  dateOfBirth = false,
}) => {
  // Refs for ScrollViews
  const monthScrollRef = useRef<ScrollView>(null);
  const dayScrollRef = useRef<ScrollView>(null);
  const yearScrollRef = useRef<ScrollView>(null);

  // Convert initialDate to Date object for the date picker
  const getInitialDate = (): Date => {
    if (initialDate) {
      const monthIndex = monthNames.indexOf(initialDate.month);
      if (monthIndex !== -1) {
        return new Date(initialDate.year, monthIndex, initialDate.day);
      }
    }
    // Default to current date
    return new Date();
  };

  // Calculate min and max dates for date of birth
  const getMinMaxDates = () => {
    if (dateOfBirth) {
      const maxDate = new Date(); // Today
      const minDate = new Date();
      minDate.setFullYear(1900); // Set minimum year to 1900
      return { minDate, maxDate };
    }
    // Default date range (current year +/- 10 years)
    const currentYear = new Date().getFullYear();
    const minDate = new Date();
    minDate.setFullYear(currentYear - 10);
    const maxDate = new Date();
    maxDate.setFullYear(currentYear + 1); // Next year
    return { minDate, maxDate };
  };

  const { minDate, maxDate } = getMinMaxDates();

  // State for selected date
  const [selectedDate, setSelectedDate] = useState<Date>(getInitialDate());

  // Generate arrays for days, months, and years
  const days = Array.from({ length: 31 }, (_, i) => i + 1);
  const years = Array.from(
    { length: maxDate.getFullYear() - minDate.getFullYear() + 1 },
    (_, i) => minDate.getFullYear() + i
  );

  // Create infinite arrays for months and days
  const infiniteMonths = [...monthNames, ...monthNames, ...monthNames];
  const infiniteDays = [...days, ...days, ...days];

  // Update state when initialDate changes
  useEffect(() => {
    if (initialDate) {
      setSelectedDate(getInitialDate());
    }
  }, [initialDate]);

  // Scroll to initial positions
  useEffect(() => {
    if (Platform.OS === 'android') {
      const monthIndex = selectedDate.getMonth();
      const dayIndex = selectedDate.getDate() - 1;
      const yearIndex = years.indexOf(selectedDate.getFullYear());

      setTimeout(() => {
        // Scroll to middle section for infinite scroll
        monthScrollRef.current?.scrollTo({ y: (monthIndex + monthNames.length) * 50, animated: false });
        dayScrollRef.current?.scrollTo({ y: (dayIndex + days.length) * 50, animated: false });
        yearScrollRef.current?.scrollTo({ y: yearIndex * 50, animated: false });
      }, 100);
    }
  }, []);

  const handleMonthScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const y = event.nativeEvent.contentOffset.y;
    const monthIndex = Math.round(y / 50) % monthNames.length;
    if (monthIndex >= 0 && monthIndex < monthNames.length) {
      const newDate = new Date(selectedDate);
      newDate.setMonth(monthIndex);
      setSelectedDate(newDate);

      // Reset scroll position when reaching the end of middle section
      const currentY = event.nativeEvent.contentOffset.y;
      const totalHeight = monthNames.length * 50;
      if (currentY < totalHeight || currentY > totalHeight * 2) {
        monthScrollRef.current?.scrollTo({ y: totalHeight + (monthIndex * 50), animated: false });
      }
    }
  };

  const handleDayScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const y = event.nativeEvent.contentOffset.y;
    const dayIndex = Math.round(y / 50) % days.length;
    if (dayIndex >= 0 && dayIndex < days.length) {
      const newDate = new Date(selectedDate);
      newDate.setDate(dayIndex + 1);
      setSelectedDate(newDate);

      // Reset scroll position when reaching the end of middle section
      const currentY = event.nativeEvent.contentOffset.y;
      const totalHeight = days.length * 50;
      if (currentY < totalHeight || currentY > totalHeight * 2) {
        dayScrollRef.current?.scrollTo({ y: totalHeight + (dayIndex * 50), animated: false });
      }
    }
  };

  const handleYearScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const y = event.nativeEvent.contentOffset.y;
    const yearIndex = Math.round(y / 50);
    if (yearIndex >= 0 && yearIndex < years.length) {
      const newDate = new Date(selectedDate);
      newDate.setFullYear(years[yearIndex]);
      setSelectedDate(newDate);
    }
  };

  const handleApply = () => {
    // Convert the Date object to the expected format
    const month = monthNames[selectedDate.getMonth()];
    const day = selectedDate.getDate();
    const year = selectedDate.getFullYear();

    onDateSelected({
      month,
      day,
      year,
    });

    onClose();
  };

  // The actual date picker content
  const renderDatePickerContent = () => (
    <View style={styles.modalContainer}>
      <ScrollView
        contentContainerStyle={{ paddingBottom: 50 }}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.pickerContainer}>
          {Platform.OS === 'ios' ? (
            <DatePicker
              date={selectedDate}
              onDateChange={setSelectedDate}
              mode="date"
              minimumDate={minDate}
              maximumDate={maxDate}
              dividerColor={colors.primary}
              theme="light"
              style={styles.datePicker}
              is24hourSource="locale"
            />
          ) : (
            <View style={styles.androidPickerContainer}>
              {/* Selection highlight in the center */}
              <View style={styles.selectionHighlight} />

              <View style={styles.androidPickerColumn}>
                <Animated.ScrollView
                  ref={monthScrollRef}
                  showsVerticalScrollIndicator={false}
                  onMomentumScrollEnd={handleMonthScroll}
                  snapToInterval={50}
                  decelerationRate="fast"
                  contentContainerStyle={styles.scrollContentContainer}
                >
                  {infiniteMonths.map((month, index) => (
                    <TouchableOpacity
                      key={`${month}-${index}`}
                      style={[
                        styles.androidPickerItem,
                        selectedDate.getMonth() === (index % monthNames.length) && styles.androidPickerItemSelected,
                      ]}
                      onPress={() => {
                        const newDate = new Date(selectedDate);
                        newDate.setMonth(index % monthNames.length);
                        setSelectedDate(newDate);
                        monthScrollRef.current?.scrollTo({ 
                          y: (monthNames.length + (index % monthNames.length)) * 50, 
                          animated: true 
                        });
                      }}
                    >
                      <P style={[
                        styles.androidPickerItemText,
                        selectedDate.getMonth() === (index % monthNames.length) && styles.androidPickerItemTextSelected,
                      ]}>
                        {month}
                      </P>
                    </TouchableOpacity>
                  ))}
                </Animated.ScrollView>
              </View>
              <View style={styles.androidPickerColumn}>
                <Animated.ScrollView
                  ref={dayScrollRef}
                  showsVerticalScrollIndicator={false}
                  onMomentumScrollEnd={handleDayScroll}
                  snapToInterval={50}
                  decelerationRate="fast"
                  contentContainerStyle={styles.scrollContentContainer}
                >
                  {infiniteDays.map((day, index) => (
                    <TouchableOpacity
                      key={`${day}-${index}`}
                      style={[
                        styles.androidPickerItem,
                        selectedDate.getDate() === day && styles.androidPickerItemSelected,
                      ]}
                      onPress={() => {
                        const newDate = new Date(selectedDate);
                        newDate.setDate(day);
                        setSelectedDate(newDate);
                        dayScrollRef.current?.scrollTo({ 
                          y: (days.length + (index % days.length)) * 50, 
                          animated: true 
                        });
                      }}
                    >
                      <P style={[
                        styles.androidPickerItemText,
                        selectedDate.getDate() === day && styles.androidPickerItemTextSelected,
                      ]}>
                        {day}
                      </P>
                    </TouchableOpacity>
                  ))}
                </Animated.ScrollView>
              </View>
              <View style={styles.androidPickerColumn}>
                <ScrollView
                  ref={yearScrollRef}
                  showsVerticalScrollIndicator={false}
                  onMomentumScrollEnd={handleYearScroll}
                  snapToInterval={50}
                  decelerationRate="fast"
                  contentContainerStyle={styles.scrollContentContainer}
                >
                  {years.map((year, index) => (
                    <TouchableOpacity
                      key={year}
                      style={[
                        styles.androidPickerItem,
                        selectedDate.getFullYear() === year && styles.androidPickerItemSelected,
                      ]}
                      onPress={() => {
                        const newDate = new Date(selectedDate);
                        newDate.setFullYear(year);
                        setSelectedDate(newDate);
                        yearScrollRef.current?.scrollTo({ y: index * 50, animated: true });
                      }}
                    >
                      <P style={[
                        styles.androidPickerItemText,
                        selectedDate.getFullYear() === year && styles.androidPickerItemTextSelected,
                      ]}>
                        {year}
                      </P>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>
          )}
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
            <P style={styles.cancelButtonText}>Cancel</P>
          </TouchableOpacity>

          <TouchableOpacity style={styles.applyButton} onPress={handleApply}>
            <P style={styles.applyButtonText}>Apply</P>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );

  // Conditionally render with or without modal
  return useModal ? (
    <Modal
      visible={isVisible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <SafeAreaView style={styles.modalOverlay}>
        {renderDatePickerContent()}
      </SafeAreaView>
    </Modal>
  ) : (
    renderDatePickerContent()
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: Platform.OS === "ios" ? 30 : 20,
    paddingTop: Platform.OS === "ios" ? 15 : (5 * height) / 100,
    paddingLeft: 0,
    paddingRight: 0,
    width: "100%",
    maxHeight: Platform.OS === "ios" ? height * 0.8 : height * 0.7,
    backgroundColor: colors.white,
  },
  pickerContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 20,
  },
  datePicker: {
    width: Math.min(width * 0.9, 350),
    height: 220,
  },
  androidPickerContainer: {
    flexDirection: "row",
    width: Math.min(width * 0.9, 350),
    height: 150,
    backgroundColor: colors.white,
    borderRadius: 12,
    overflow: "hidden",
    position: "relative",
  },
  selectionHighlight: {
    position: "absolute",
    left: 0,
    right: 0,
    height: 50,
    top: "50%",
    marginTop: -25,
    // backgroundColor: "#F5F2FF",
    zIndex: -1,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: colors.primary,

  },
  scrollContentContainer: {
    paddingTop: 50,
    paddingBottom: 50,
  },
  androidPickerColumn: {
    flex: 1,
  },
  androidPickerItem: {
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 10,
  },
  androidPickerItemSelected: {
    backgroundColor: "transparent",
  },
  androidPickerItemText: {
    fontSize: 16,
    color: colors.dGray,
    fontFamily: fonts.poppinsRegular,
  },
  androidPickerItemTextSelected: {
    color: colors.primary,
    fontFamily: fonts.poppinsMedium,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 25,
    borderWidth: 1,
    borderColor: "#E6E6E6",
    marginRight: 10,
  },
  cancelButtonText: {
    color: colors.primary,
  },
  applyButton: {
    flex: 1,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 25,
    backgroundColor: colors.primary,
    marginLeft: 10,
  },
  applyButtonText: {
    color: "white",
  },
});

export default WheelDatePicker; 