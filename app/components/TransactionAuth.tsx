import * as LocalAuthentication from "expo-local-authentication";
import { GetUserDetails } from "../RequestHandlers/User";

// Utility function to handle biometric authentication
export const TransactionAuth = async (
  func: () => void, 
  handleToast: (message: string, type: "success" | "error") => void
) => {
  try {
    // Check if device supports biometrics
    const isCompatible = await LocalAuthentication.hasHardwareAsync();
    if (!isCompatible) {
      handleToast("Your device isn't compatible with biometrics.", "error");
      return;
    }

    // Check if the user has biometrics enrolled
    const isEnrolled = await LocalAuthentication.isEnrolledAsync();
    if (!isEnrolled) {
      handleToast("No biometric authentication found. Please set up Face ID or Fingerprint.", "error");
      return;
    }

    // Perform biometric authentication
    const biometricAuthResult = await LocalAuthentication.authenticateAsync({
      promptMessage: "Authenticate",
      fallbackLabel: "Use PIN instead",
    });

    if (!biometricAuthResult.success) {
      handleToast("Biometric authentication failed. Please try again or use PIN.", "error");
      return;
    }

    // If successful, call the function passed
    func();
  } catch (error) {
    handleToast("An unexpected error occurred during authentication.", "error");
    console.error("Biometric Authentication Error:", error);
  }
};
