import React, { useEffect, useState, useRef } from "react";
import {
  StyleSheet,
  View,
  Dimensions,
  Modal,
  Animated,
  Text,
  Easing,
  TouchableOpacity,
} from "react-native";
import { colors } from "../config/colors";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "./P";
import Button from "./Button";
import { navigationRef } from "../navigation/navigationRef";
import { fonts } from "../config/Fonts";

const { width, height } = Dimensions.get("window");

interface PProps {
  visible?: any;
  loading?: boolean;
  showRetry?: boolean; // New prop to show the retry button
  onRetry?: () => void; // Callback function for the retry button
  navigation?: any;
}

export default function VLoader({
  visible,
  loading = true,
  showRetry = false,
  onRetry,
  navigation,
}: PProps) {
  const [activeDot, setActiveDot] = useState(0);
  const dotsArray = [1, 2, 3];

  // Create animated values for each dot
  const animations = useRef(dotsArray.map(() => new Animated.Value(1))).current;
  const [showCancelBtn, setShowCancelBtn] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowCancelBtn(true);
    }, 60 * 1500);

    return () => clearTimeout(timer);
  }, []);
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    let rotationAnimation: Animated.CompositeAnimation | null = null;
    let pulseAnimation: Animated.CompositeAnimation | null = null;
    let scaleAnimation: Animated.CompositeAnimation | null = null;

    if (loading && !showRetry && visible) {
      // Fade in animation
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Rotation animation - continuous 360 degree rotation
      rotationAnimation = Animated.loop(
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 2000,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      );
      rotationAnimation.start();

      // Pulse animation - subtle size pulsing
      pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 0.95,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();

      // Scale animation - subtle bounce effect
      scaleAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.05,
            duration: 500,
            easing: Easing.out(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 0.95,
            duration: 500,
            easing: Easing.in(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      );
      scaleAnimation.start();
    }

    return () => {
      // Stop and reset animations when component unmounts
      if (rotationAnimation) rotationAnimation.stop();
      if (pulseAnimation) pulseAnimation.stop();
      if (scaleAnimation) scaleAnimation.stop();

      rotateAnim.setValue(0);
      scaleAnim.setValue(1);
      opacityAnim.setValue(0);
      pulseAnim.setValue(1);
    };
  }, [loading, showRetry, visible]);

  // Interpolate rotation value to create a rotation string
  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  });
  return (
    <Modal
      transparent={true}
      style={styles.body}
      statusBarTranslucent={true}
      visible={visible}
    >
      <View style={styles.item}>
        {showRetry ? (
          <View style={styles.retryContainer}>
            <Text style={styles.retryText}>
              Request timed out. Please try again.
            </Text>
            <TouchableOpacity onPress={onRetry} style={styles.retryButton}>
              <Text style={styles.retryButtonText}>Try Again</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            <View style={styles.loaderContainer}>
              <Animated.View
                style={[
                  styles.logoContainer,
                  {
                    opacity: opacityAnim,
                    transform: [{ rotate: spin }, { scale: scaleAnim }],
                  },
                ]}
              >
                {/* Outer pulsing circle */}
                <Animated.View
                  style={[
                    styles.pulseCircle,
                    {
                      transform: [{ scale: pulseAnim }],
                    },
                  ]}
                />

                {/* Logo in the center */}
                <View style={styles.logoWrapper}>
                  <SvgXml
                    xml={svg.smalllogo}
                    width={40}
                    height={40}
                  />
                </View>
              </Animated.View>

              <Animated.Text
                style={[styles.loadingText, { opacity: opacityAnim }]}
              >
                Account verification in progress{"\n"}this will take a while
              </Animated.Text>
            </View>

            {showCancelBtn && (
              <View
                style={{ width: "60%", alignSelf: "center", marginTop: 16 }}
              >
                <Button
                  btnText="Cancel"
                  onPress={() => {
                    navigation.pop();
                  }}
                />
              </View>
            )}
          </>
        )}
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  body: {
    width,
    flex: 1,
    height: (105 * height) / 100,
    position: "absolute",
    top: 0,
    alignItems: "center",
    justifyContent: "center",
  },
  item: {
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
  },

  dot: {
    width: 16,
    height: 16,
    borderRadius: 50,
    marginHorizontal: 4,
  },
  retryContainer: {
    alignItems: "center",
  },
  retryText: {
    fontSize: 16,
    color: colors.primary,
    marginBottom: 10,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: colors.primary,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.white,
    fontWeight: "bold",
    fontSize: 16,
  },
  loaderContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  logoContainer: {
    width: 120,
    height: 120,
    alignItems: "center",
    justifyContent: "center",
    position: "relative",
  },
  logoWrapper: {
    width: 60,
    height: 60,
    borderRadius: 100,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    zIndex: 2,
    overflow: "hidden", // Ensure the logo doesn't overflow the container
  },
  pulseCircle: {
    position: "absolute",
    width: 80,
    height: 80,
    borderRadius: 100,
    borderWidth: 2,
    borderColor: colors.primary,
    opacity: 0.5,
    zIndex: 1,
  },
  loadingText: {
    marginTop: 20,
    fontSize: 16,
    color: colors.dGray,
    fontFamily: fonts.poppinsMedium
  },
});
