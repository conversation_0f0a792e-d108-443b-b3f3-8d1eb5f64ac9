import React, { useEffect, useRef } from "react";
import { View, TouchableOpacity, Animated, StyleSheet } from "react-native";

interface PProps {
  onToggle?: (state: boolean) => void;
  disabled?: boolean;
  isOn?: boolean; // Accept the initial switch state from the parent
}

const CustomSwitch1 = ({ onToggle, disabled, isOn = false }: PProps) => {
  // Use useRef to persist the Animated.Value across renders
  const animationValue = useRef(new Animated.Value(isOn ? 1 : 0)).current;

  useEffect(() => {
    // Animate to the current isOn state when it changes
    Animated.timing(animationValue, {
      toValue: isOn ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [isOn]);

  const toggleSwitch = () => {
    // Toggle the state by calling the onToggle prop function
    if (onToggle) {
      onToggle(!isOn);
    }
  };

  const translateX = animationValue.interpolate({
    inputRange: [0, 1],
    outputRange: [2, 15], // Adjust these values to match the size of your thumb button
  });

  const trackColor = animationValue.interpolate({
    inputRange: [0, 1],
    outputRange: ["#E6E6E6", "#8B52FF"],
  });

  const thumbColor = animationValue.interpolate({
    inputRange: [0, 1],
    outputRange: ["#FFFFFF", "#FFFFFF"],
  });

  return (
    <TouchableOpacity
      style={styles.container}
      disabled={disabled}
      onPress={toggleSwitch}
    >
      <Animated.View style={[styles.track, { backgroundColor: trackColor }]}>
        <Animated.View
          style={[
            styles.thumb,
            { transform: [{ translateX }], backgroundColor: thumbColor },
          ]}
        />
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 33,
    height: 18,
    justifyContent: "center",
  },
  track: {
    width: 33,
    height: 18,
    borderRadius: 9,
    padding: 2,
  },
  thumb: {
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#FFFFFF',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 2,
  },
});

export default CustomSwitch1;
