import React from "react";
import { View, Image, StyleSheet } from "react-native";
import P from "./P";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";

interface PProps {
  img?: any;
  text1?: string;
  text2?: string;
  svg?: any;
}
export default function BenediciaryComponent({
  img,
  text1,
  text2,
  svg,
}: PProps) {
  return (
    <View style={styles.cont}>
      {img && (
        <Image
          source={img}
          style={{
            width: 24,
            height: 24,
            objectFit: "contain",
            marginRight: 8,
          }}
        />
      )}

      {svg && <SvgXml xml={svg} style={{ marginRight: 8 }} />}

      <View style={{}}>
        <P style={{ fontSize: 12, lineHeight: 18 }}>{text1}</P>
        <P
          style={{
            fontSize: 12,
            lineHeight: 18,
            color: colors.gray,
            fontFamily: fonts.poppinsRegular,
          }}
        >
          {text2}
        </P>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  cont: {
    flexDirection: "row",
    paddingLeft: 24,
    marginTop: 26,
    alignItems: "center",
  },
});
