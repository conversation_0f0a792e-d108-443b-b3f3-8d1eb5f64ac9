import React, { CSSProperties } from "react";
import { StyleSheet, View, Image, TouchableOpacity } from "react-native";
import { colors } from "../config/colors";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "./P";
import { fonts } from "../config/Fonts";

interface PProps {
  image?: any;
  text1?: string;
  text2?: string;
  centerText?: string;
  rightComponent?: any;
  icon?: any;
  onPress?: any;
  isActive?: any;
  containerStyle?: CSSProperties;
  imgStyle?: CSSProperties;
  iconComp?: any;
}
export default function ListItemSelect({
  image,
  text1,
  text2,
  centerText,
  rightComponent,
  icon,
  onPress,
  isActive,
  containerStyle,
  imgStyle,
  iconComp,
}: PProps) {
  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.7}
      style={{ width: '100%' }}
    >
      <View
        style={[
          styles.container,
          { backgroundColor: isActive ? colors.lowOpPrimary2 : "transparent" },
          // @ts-ignore
          containerStyle,
        ]}
      >
        {/* @ts-ignore */}
        {image && <Image source={image} style={[styles.image, imgStyle]} />}
        {iconComp}
        {icon && <SvgXml xml={icon} style={{ marginRight: 8 }} />}
        <View>
          {text1 && <P style={styles.text1}>{text1}</P>}
          {centerText && <P style={styles.text2}>{centerText}</P>}
          {text2 && <P style={styles.text2}>{text2}</P>}
        </View>
        {isActive && (
          <View style={styles.rightCont}>
            <SvgXml xml={svg.Circlecheck} />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    height: "auto",
    padding: 8,
    paddingLeft: 16,
    paddingRight: 16,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
  },

  image: {
    width: 32,
    height: 32,
    borderRadius: 100,
    marginRight: 16,
    objectFit: "cover",
  },
  text1: {
    fontSize: 12,
    lineHeight: 18,
    color: "#000",
    fontFamily: fonts.poppinsMedium,
    marginBottom: 4,
  },
  text2: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
    marginBottom: 4,
  },
  rightCont: {
    position: "absolute",
    right: 16,
    alignSelf: "center",
  },
});
