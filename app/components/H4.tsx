import { Text, TextStyle, StyleProp, Dimensions } from "react-native";
import React from "react";
import { fonts } from "../config/Fonts";
import { colors } from "../config/colors";
const { height } = Dimensions.get("window");

interface PProps {
  children: React.ReactNode;
  style?: StyleProp<TextStyle>;
}

const baseHeight = 800;
export default function H4({ children, style }: PProps) {
  return (
    <Text
      style={[
        {
          fontFamily: fonts.poppinsBold,
          fontSize: 20,
          color: colors.black,
          lineHeight: 30,
          letterSpacing: 0,
        },
        style,
      ]}
    >
      {children}
    </Text>
  );
}
