import {
  StyleSheet,
  Text,
  View,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import React from "react";
import { CSSProperties } from "react";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "../components/P";

const { width, height } = Dimensions.get("window");

interface PProps {
  text: any;
  navigation: any;
  back?: any;
  boxstyle?: CSSProperties;
  textstyle?: CSSProperties;
}

export default function SignupHeader({
  text,
  boxstyle,
  navigation,
  textstyle,
  back = false,
}: PProps) {
  return (
    <View
      style={[
        {
          width: "100%",
          marginBottom: 35,
          marginTop: 23,
          flexDirection: "row",
          justifyContent: "space-between",
          paddingHorizontal: "5%",
        },
        // @ts-ignore
        boxstyle,
      ]}>
      {back ? (
        <TouchableOpacity onPress={() => navigation.pop()}>
          <SvgXml xml={svg.goBackIcon} />
        </TouchableOpacity>
      ) : (
        <Text> </Text>
      )}
      <P
        // @ts-ignore
        style={[
          {
            fontSize: 12,
            lineHeight: 22,
            //   textAlign: "right",
            fontFamily: fonts.poppinsMedium,
          },
          textstyle,
        ]}>
        {text}
      </P>
    </View>
  );
}

const styles = StyleSheet.create({});
