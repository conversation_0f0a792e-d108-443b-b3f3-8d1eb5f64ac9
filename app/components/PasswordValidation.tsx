import React from "react";
import { View, StyleSheet } from "react-native";
import { SvgXml } from "react-native-svg";
import P from "./P";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";

interface PasswordValidationProps {
  password: string;
}

interface ValidationRule {
  id: string;
  label: string;
  isValid: (password: string) => boolean;
}

const PasswordValidation: React.FC<PasswordValidationProps> = ({
  password,
}) => {
  const validationRules: ValidationRule[] = [
    {
      id: "alphabets",
      label: "Alphabets",
      isValid: (pwd) => /[a-zA-Z]/.test(pwd),
    },
    {
      id: "number",
      label: "Number",
      isValid: (pwd) => /[0-9]/.test(pwd),
    },
    {
      id: "uppercase",
      label: "Uppercase",
      isValid: (pwd) => /[A-Z]/.test(pwd),
    },
    {
      id: "lowercase",
      label: "Lowercase",
      isValid: (pwd) => /[a-z]/.test(pwd),
    },
    {
      id: "length",
      label: "8 characters above",
      isValid: (pwd) => pwd.length >= 8,
    },
    {
      id: "special",
      label: "Special character",
      isValid: (pwd) => /[*?!#$%&@^()\-_=+\\|[\]{};:/?.>]/.test(pwd),
    },
  ];

  const getValidationIcon = (isValid: boolean) => {
    if (isValid) {
      return (
        <SvgXml
          xml={`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="8" cy="8" r="8" fill="#027A48"/>
            <path d="M5 8L7 10L11 6" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>`}
        />
      );
    } else {
      return (
        <SvgXml
          xml={`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="8" cy="8" r="8" fill="#BEBBBB"/>
            <path d="M6 6L10 10M10 6L6 10" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>`}
        />
      );
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.grid}>
        {validationRules.map((rule, index) => {
          const isValid = rule.isValid(password);
          return (
            <View
              key={rule.id}
              style={[
                styles.validationItem,
                {
                  backgroundColor: isValid
                    ? "#ECFDF3"
                    : colors.lowOpPrimary2,
                },
              ]}
            >
              {getValidationIcon(isValid)}
              <P
                style={[
                  styles.validationText,
                  { color: isValid ? "#037143" : "#161817" },
                ]}
              >
                {rule.label}
              </P>
            </View>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 12,
    marginBottom: 8,
  },
  grid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 6
    // justifyContent: "space-between",
  },
  validationItem: {
    width: "auto",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
    paddingHorizontal: 8,
    paddingVertical: 4.5,
    backgroundColor: colors.lowOpPrimary2,
    borderRadius: 10,
  },
  validationText: {
    fontSize: 10,
    fontFamily: fonts.poppinsMedium,
    marginLeft: 4,
  },
});

export default PasswordValidation;
