import React, { useState } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  TextInput,
  Image,
  FlatList
} from "react-native";
import P from "./P";
import { svg } from "../config/Svg";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";
import { CountryList } from "./CountryList";
import { AfricanCountriesList } from "./AfricanCountriesList";
interface CountryCodeSelectProps {
  onPress?: (country: {
    name: string;
    code: string;
    alpha_code3: string;
    tel_code: string;
  }) => void;
  excludedCountries?: string[];
  offHeader?: boolean;
}

const { height } = Dimensions.get("window");

export default function CountryCodeSelect({
  onPress,
  excludedCountries = [],
  offHeader = false,
}: CountryCodeSelectProps) {
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Filter countries based on search query and excluded countries
  const filteredCountries = searchQuery
    ? AfricanCountriesList.filter(
        (country) =>
          (country.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
           country.tel_code.includes(searchQuery)) &&
          !excludedCountries.includes(country.name)
      )
    : AfricanCountriesList.filter(
        (country) => !excludedCountries.includes(country.name)
      );

  const renderCountryItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.countryItem,
        selectedCountry === item.name && styles.selectedCountryItem,
      ]}
      onPress={() => {
        setSelectedCountry(item.name);
        if (onPress) {
          onPress(item);
        }
      }}
    >
      <View style={styles.countryItemContent}>
        <Image
          source={{
            uri: `https://flagcdn.com/w2560/${item.code.toLowerCase()}.png`,
          }}
          style={[
            styles.flagImage,
            { objectFit: item.code === "NG" ? "fill" : "cover" },
          ]}
        />
        <View style={styles.countryInfo}>
          <P style={styles.countryName}>{item.name}</P>
          <P style={styles.countryCode}>{item.tel_code}</P>
        </View>
      </View>
      {selectedCountry === item.name && (
        <View style={styles.checkmarkContainer}>
          <SvgXml xml={svg.Circlecheck} />
        </View>
      )}
    </TouchableOpacity>
  );

  // Log the number of countries for debugging
  console.log(`Filtered countries for dial codes: ${filteredCountries.length}`);

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <SvgXml xml={svg.search} style={{ marginRight: 8 }} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search country or dial code"
          placeholderTextColor={colors.dGray}
          cursorColor={colors.black}
          value={searchQuery}
          onChangeText={(text) => setSearchQuery(text)}
        />
      </View>

      {!offHeader && (
        <P style={styles.headerText}>
          Select your phone number
        </P>
      )}

      {filteredCountries.length > 0 ? (
        <FlatList
          data={filteredCountries}
          renderItem={renderCountryItem}
          keyExtractor={(item) => item.code}
          showsVerticalScrollIndicator={true}
          contentContainerStyle={styles.listContainer}
          initialNumToRender={20}
          windowSize={10}
        />
      ) : (
        <View style={styles.noResultsContainer}>
          <P style={styles.noResultsText}>No countries found</P>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    paddingTop: 24,
    flex: 1,
  },
  searchContainer: {
    width: "100%",
    height: (5.5 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    marginBottom: 24,
    borderWidth: 1,
    borderRadius: 99,
    borderColor: colors.stroke,
    padding: 12,
    paddingLeft: 14,
    paddingRight: 14,
    flexDirection: "row",
    alignItems: "center",
  },
  searchInput: {
    flex: 1,
    height: 24,
    fontFamily: fonts.poppinsRegular,
    lineHeight: 24,
    fontSize: 14,
  },
  headerText: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
    marginBottom: 16,
  },
  listContainer: {
    paddingBottom: 100,
  },
  countryItem: {
    width: "100%",
    padding: 12,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  selectedCountryItem: {
    backgroundColor: colors.lowOpPrimary2,
  },
  countryItemContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  flagImage: {
    width: 30,
    height: 30,
    borderRadius: 100,
    marginRight: 12,
  },
  countryInfo: {
    flexDirection: "column",
  },
  countryName: {
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    color: colors.black,
  },
  countryCode: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.dGray,
    marginTop: 2,
  },
  checkmarkContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 20,
  },
  noResultsText: {
    fontSize: 16,
    color: colors.dGray,
    fontFamily: fonts.poppinsRegular,
  },
});
