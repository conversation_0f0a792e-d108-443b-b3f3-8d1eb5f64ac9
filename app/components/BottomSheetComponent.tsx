import React, { CSSProperties, useState, useEffect } from "react";
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Dimensions,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import Modal from "react-native-modal";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import { fonts } from "../config/Fonts";
import { colors } from "../config/colors";

interface PProps {
  isVisible?: any;
  onClose?: any;
  components?: any;
  backspaceText?: string;
  showBackArrow?: Boolean;
  onBackArrowPress?: any;
  extraModalStyle?: CSSProperties;
  modalContentStyle?: CSSProperties;
  topSection?: any;
  topSectionStyle?: any;
}

const { width, height } = Dimensions.get("window");
export default function BottomSheetComponent({
  isVisible,
  onClose,
  components,
  backspaceText,
  showBackArrow = false,
  onBackArrowPress,
  extraModalStyle,
  modalContentStyle,
  topSection,
  topSectionStyle,
}: PProps) {
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  return (
    <Modal
      isVisible={isVisible}
      style={styles.modal}
      statusBarTranslucent={true}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        {/* @ts-ignore */}
        <View style={[styles.bodyWrapper, { topSectionStyle }]}>
          {topSection}
        </View>
      </TouchableWithoutFeedback>
      {/* @ts-ignore*/}
      <View
        style={[
          styles.extraModal,
          { top: keyboardVisible ? (19.5 * height) / 100 : "" },
          extraModalStyle,
        ]}
      ></View>
      <View
        style={[
          styles.modalContent,
          { marginBottom: keyboardVisible ? 200 : -40 },
          // @ts-ignore
          modalContentStyle,
        ]}
      >
        <View style={styles.section1}>
          {showBackArrow && (
            <TouchableOpacity onPress={onBackArrowPress}>
              <SvgXml xml={svg.goBackIcon} style={{ marginRight: 12 }} />
            </TouchableOpacity>
          )}

          <Text
            style={{
              fontSize: 14,
              fontFamily: fonts.poppinsMedium,
              lineHeight: 21,
            }}
          >
            {backspaceText}
          </Text>
          <TouchableOpacity
            style={{ position: "absolute", right: 24 }}
            onPress={onClose}
          >
            <SvgXml xml={svg.xClose} />
          </TouchableOpacity>
        </View>
        {components}
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modal: {
    // backgroundColor: 'red',
    justifyContent: "flex-end",
    margin: 0,
    height,
    position: "absolute",
  },
  modalContent: {
    height: "30%",
    width,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    // padding: 24,
    paddingTop: 0,
  },
  extraModal: {
    width: "80%",
    height: "28%",
    position: "absolute",
    alignSelf: "center",
    borderRadius: 20,
    backgroundColor: "#E6E5E5",
  },
  section1: {
    height: 61,
    // backgroundColor: 'red',
    width: "100%",
    borderBottomColor: "rgba(139, 144, 154, 0.25)",
    borderBottomWidth: 1,
    flexDirection: "row",
    alignItems: "center",
    marginTop: 11,
    paddingTop: 0,
    paddingBottom: 0,
    padding: 24,
  },
  bodyWrapper: {
    height: "50%",
    //    backgroundColor: 'red',
  },
});
