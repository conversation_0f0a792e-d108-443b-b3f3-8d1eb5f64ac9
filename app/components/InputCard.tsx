import React, { CSSProperties } from "react";
import { Dimensions, StyleSheet, TouchableOpacity, View } from "react-native";
import { colors } from "../config/colors";
import P from "./P";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import { ActivityIndicator } from "react-native";

const { width, height } = Dimensions.get("window");
const baseHeight = 812;
interface PProps {
  amountValue?: any;
  convertedValue?: any;
  rate?: any;
  error?: Boolean;
  headerText?: string;
  onTogglePress?: any;
  text1?: string;
  text2?: string;
  toggleStyle?: CSSProperties;
  extraComponent?: any;
  extraComponent1?: any;
  children?: any;
  loading?: boolean;
}
export default function InputCard({
  amountValue,
  convertedValue,
  rate,
  error,
  headerText = "How much do you want to add",
  onTogglePress,
  text1,
  text2,
  toggleStyle,
  extraComponent,
  extraComponent1,
  children,
  loading,
}: PProps) {
  return (
    <View style={styles.cont}>
      <View>
        <P style={styles.headerText}>{headerText}</P>
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {amountValue}
        </View>
        <View
          style={[
            styles.line,
            { backgroundColor: error ? colors.red : colors.stroke },
          ]}
        ></View>
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
            paddingTop: 4,
          }}
        >
          {convertedValue}
        </View>
      </View>
      <TouchableOpacity
        onPress={onTogglePress}
        style={[
          { position: "absolute", right: "10%", top: "50%" },
          // @ts-ignore
          toggleStyle,
        ]}
      >
        {onTogglePress && <SvgXml xml={svg.upDown} />}
      </TouchableOpacity>
      {extraComponent1}
      {text1 && (
        <View style={[styles.exchangeRate, { backgroundColor: "" }]}>
          {/* @ts-ignore */}
          <P style={[styles.rate, { color: colors.gray }]}>{text1}</P>
        </View>
      )}
      {text2 && (
        <View style={styles.exchangeRate}>
          {text2 && (
            <P style={styles.rate}>
              {text2} {rate}
            </P>
          )}
          {loading && (
            <ActivityIndicator
              size={4}
              color={colors.primary}
              style={{ marginLeft: 4, marginRight: 8}}
            />
          )}
        </View>
      )}
      {extraComponent}
    </View>
  );
}

const styles = StyleSheet.create({
  cont: {
    width: "100%",
    paddingTop: (16 / baseHeight) * height,
    paddingBottom: (16 / baseHeight) * height,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
  },
  headerText: {
    color: colors.gray,
    lineHeight: 19.2,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    textAlign: "center",
  },
  line: {
    width: (60 * width) / 100,
    height: 1,
    alignSelf: "center",
  },
  exchangeRate: {
    height: 24,
    backgroundColor: colors.lowOpPrimary2,
    borderRadius: 99,
    marginTop: 8,
    justifyContent: "center",
    paddingHorizontal: 8,
    paddingTop: 4,
    paddingBottom: 6,
    alignItems: "center",
    flexDirection: "row",
  },
  rate: {
    fontSize: 10,
    lineHeight: 16,
    fontFamily: fonts.poppinsMedium,
  },
});
