import React from "react";
import { StyleSheet, View, TextStyle } from "react-native";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "./P";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";

interface PProps {
  text?: any;
  textStyle?: TextStyle;
  component?: any;
  contStyle?: TextStyle;
  component2?: any;
  type?: string;
}
export default function NoteComponent2({
  text,
  textStyle,
  component,
  contStyle,
  component2,
  type,
}: PProps) {
  return (
    // @ts-ignore
    <View
      style={[
        styles.info,
        contStyle,
        { borderColor: type === "red" ? colors.red : colors.primary },
      ]}
    >
      <View
        style={{
          flexDirection: "row",
          alignItems: type === "red" ? "center" : "flex-start",
        }}
      >
        <SvgXml
          xml={type === "red" ? svg.infoCircleOutLine : svg.alertCircle2}
          style={{ marginRight: 8 }}
        />
        {text && (
          <P
            // @ts-ignore
            style={[
              {
                fontSize: 10,
                lineHeight: 16,
                fontFamily: fonts.poppinsRegular,
                color: colors.black,
              },
              textStyle,
            ]}
          >
            {text}
          </P>
        )}
        {component}
      </View>
      {component2}
    </View>
  );
}

const styles = StyleSheet.create({
  info: {
    width: "100%",
    backgroundColor: colors.lowOpPrimary3,
    borderLeftWidth: 4,
    borderColor: colors.primary,
    padding: 16,
    paddingRight: 50,
    paddingTop: 8,
    paddingBottom: 8,
    borderRadius: 8,
    flexDirection: "row",
    // alignItems: "center",
  },
});
