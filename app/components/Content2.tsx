import {
  StyleSheet,
  Text,
  View,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import React, { useState } from "react";
import { CSSProperties } from "react";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "./P";
const { width, height } = Dimensions.get("window");

interface PProps {
  header: any;
  style?: CSSProperties;
  body?: string;
  svgg?: any;
  onPress?: any;
  bottomBorder?: boolean;
  headerStyle?: CSSProperties;
  textStyle?: CSSProperties;
  containerStyle?: CSSProperties;
  itemWrapper?: CSSProperties;
  rightComponent?: any;
  onclick?: any;
  ClickedMe?: any;
}

export default function Content2({
  header,
  body,
  svgg,
  onPress,
  bottomBorder = true,
  headerStyle,
  textStyle,
  containerStyle,
  itemWrapper,
  rightComponent,
  onclick,
  ClickedMe,
}: PProps) {
  // const [onclick, setOnclick] = useState(false);
  // const ClickedMe = () => {
  //   setOnclick(!onclick);
  //   onPress == null ? null : onPress();
  // };
  return (
    <TouchableOpacity
      style={[
        styles.content,
        // @ts-ignore
        containerStyle,
        { borderColor: onclick ? "#8C52FF" : "#E6E5E5" },
      ]}
      onPress={ClickedMe}
    >
      <SvgXml xml={svgg} />
      <View
        style={[
          {
            width:
              onclick == true && rightComponent
                ? "40%"
                : onclick
                ? "65%"
                : rightComponent
                ? "40%"
                : "80%",
            height: "50%",
            // backgroundColor: "red",
            justifyContent: "space-around",
          },
          // @ts-ignore
          itemWrapper,
        ]}
      >
        {/* @ts-ignore */}
        <P style={[styles.contentHeader, headerStyle]}>{header}</P>
        {/*@ts-ignore  */}
        <P style={[styles.contentText, { textStyle }]}>{body}</P>
      </View>
      {rightComponent}
      {onclick == true && rightComponent ? (
        <></>
      ) : onclick ? (
        <SvgXml
          xml={svg.purple_check}
          style={{ position: "absolute", right: "5%" }}
        />
      ) : (
        <></>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  content: {
    width: width * 0.8,
    height: 72,
    borderWidth: 1,
    borderColor: "#E6E5E5",
    // backgroundColor:"red",
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    borderRadius: 6,
    marginBottom: 16,
  },
  contentHeader: { fontSize: 12, fontFamily: fonts.poppinsSemibold },
  contentText: { fontSize: 12, color: "#A5A1A1" },
});
