import React, { CSSProperties } from "react";
import {
  Dimensions,
  ImageBackground,
  Platform,
  StyleSheet,
  View,
} from "react-native";
import { colors } from "../config/colors";
import P from "./P";
import { fonts } from "../config/Fonts";
import { string } from "yup";
import DashedLine from "./DashedLine";

interface PProps {
  headText?: any;
  amount?: any;
  convertedAmount?: any;
  bottomComponent?: any;
  timer?: any;
  lineStyle?: CSSProperties;
  image?: any;
  type?: string;
  bottomComponent2?: any;
}
const { width, height } = Dimensions.get("window");
const CIRCLE_SIZE = 24;
const GAP = 8;

export default function DetailCard({
  headText,
  amount,
  convertedAmount,
  bottomComponent,
  timer,
  lineStyle,
  image,
  type,
  bottomComponent2,
}: PProps) {
  const numCircles = Math.floor(width / (CIRCLE_SIZE + GAP));
  const arr = Array.from({ length: numCircles - 1 });
  return type === "reciept" ? (
    <View
      style={[
        styles.cont,
        { paddingTop: CIRCLE_SIZE, paddingBottom: CIRCLE_SIZE },
      ]}
    >
      <View style={[styles.cutCont, { top: -CIRCLE_SIZE / 2 }]}>
        {arr.map((_, idx) => (
          <View key={idx} style={[styles.cutCircle]} />
        ))}
      </View>
      <ImageBackground
        source={require("../assets/RecieptBackground.png")}
        style={{ width: "100%", height: "auto" }}
        resizeMode="cover"
      >
        <View style={{ width: "100%", alignItems: "center" }}>
          {headText && <P style={styles.p1}>{headText}</P>}
          {image}
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {amount}
          </View>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {convertedAmount}
          </View>
          {timer}
          {/* @ts-ignore */}
          {Platform.OS === "ios" ? (
            // @ts-ignore
            <DashedLine
              color={colors.stroke}
              style={{ marginVertical: 8 }}
              width={(80 * width) / 100}
            />
          ) : (
            // @ts-ignore
            <DashedLine
              color={colors.stroke}
              style={{ marginVertical: 8 }}
              width={(80 * width) / 100}
            />
          )}
          <View style={{ width: "100%" }}>{bottomComponent}</View>
        </View>
      </ImageBackground>
      <View style={[styles.cutCont, { bottom: -CIRCLE_SIZE / 2 }]}>
        {arr.map((_, idx) => (
          <View key={idx} style={styles.cutCircle} />
        ))}
      </View>
    </View>
  ) : (
    <>
      <View style={styles.cont}>
        <View style={{ width: "100%", alignItems: "center" }}>
          {headText && <P style={styles.p1}>{headText}</P>}
          {image}
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {amount}
          </View>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {convertedAmount}
          </View>
          {timer}
          {/* @ts-ignore */}
          {Platform.OS === "ios" ? (
            // @ts-ignore
           <DashedLine
              color={colors.stroke}
              style={{ marginVertical: 8 }}
              width={(80 * width) / 100}
            />
          ) : (
            // @ts-ignore
           <DashedLine
              color={colors.stroke}
              style={{ marginVertical: 8 }}
              width={(90 * width) / 100}
            />
          )}
          <View style={{ width: "100%" }}>{bottomComponent}</View>
        </View>
      </View>
      {bottomComponent2 && (
        <View
          style={{
            width: "100%",
            backgroundColor: colors.white,
            marginTop: 8,
            borderRadius: 12,
            padding: 24,
          }}
        >
          {bottomComponent2}
        </View>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  cont: {
    width: "100%",
    padding: 24,
    backgroundColor: colors.white,
    borderRadius: 12,
    alignItems: "center",
  },
  p1: {
    fontSize: 12,
    color: colors.dGray,
    lineHeight: 19.2,
    fontFamily: fonts.poppinsRegular,
  },
  line: {
    width: "100%",
    // height: 1,
    borderWidth: 1,
    borderStyle: "dashed",
    borderColor: colors.stroke,
    marginTop: 16,
    marginBottom: 16,
    // margin: -2,
  },
  line2: {
    width: "100%",
    // height: 1,
    borderBottomWidth: 1,
    borderStyle: "dashed",
    borderColor: colors.stroke,
    marginTop: 16,
    marginBottom: 16,
    // margin: -2,
  },
  cutCont: {
    width: "100%",
    height: CIRCLE_SIZE,
    // backgroundColor: "red",
    position: "absolute",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    overflow: "hidden",
  },
  cutCircle: {
    width: CIRCLE_SIZE,
    height: CIRCLE_SIZE,
    backgroundColor: colors.secBackground,
    borderRadius: 100,
  },
});
