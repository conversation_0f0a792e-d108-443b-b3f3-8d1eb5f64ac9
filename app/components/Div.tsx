import React, { CSSProperties, useEffect, useState } from "react";
import { SafeAreaView, StyleSheet, StatusBar, KeyboardAvoidingView, Platform, Keyboard } from "react-native";
import Constants from "expo-constants";

interface PProps {
  children: any;
  style?: CSSProperties;
  disableKeyboardAvoidingView?: boolean;
}

function Div({ children, style, disableKeyboardAvoidingView = false }: PProps) {
  return (
    <>
      {/* @ts-ignore */}
      <StatusBar style="light" />
      {/* @ts-ignore */}
      <SafeAreaView style={[styles.screen, style]}>
        {disableKeyboardAvoidingView ? (
          children
        ) : (
          <KeyboardAvoidingView
            style={styles.keyboardAvoidingView}
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
          >
            {children}
          </KeyboardAvoidingView>
        )}
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  screen: {
    paddingTop: Constants.statusBarHeight,
    flex: 1,
    backgroundColor: "transparent",
    height: "100%",
  },
  keyboardAvoidingView: {
    flex: 1,
  },
});
export default Div;
