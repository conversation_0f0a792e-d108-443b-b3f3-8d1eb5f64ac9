import React, { useState, useEffect } from "react";
import { View, StyleSheet, ScrollView } from "react-native";
import P from "./P";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import ListItemSelect from "./ListItemSelect";
import { svg } from "../config/Svg";

interface PProps {
  onPress?: any;
  onMemoPress?: any;
  onP2pTrans?: any;
  onActiveTypeChange?: (index: number | null) => void;
  onActiveTypeNameChange?: (index: String | null) => void; // Add this line
}

export default function SelectPaymentType({
  onPress,
  onMemoPress,
  onP2pTrans,
  onActiveTypeChange,
  onActiveTypeNameChange,
}: PProps) {
  const [activeType, setActiveType] = useState(null);
  const [activeTypeName, setActiveTypeName] = useState("");

  useEffect(() => {
    if (onActiveTypeChange) {
      onActiveTypeChange(activeType);
    }
  }, [activeType]);

  useEffect(() => {
    if (onActiveTypeNameChange) {
      onActiveTypeNameChange(activeTypeName);
    }
  }, [activeTypeName]);

  const paymentType = [
    {
      type: "Bank transfer",
      text1: "Add money via bank transfer",
      icon: svg.bank,
    },
    {
      type: "Mobile money",
      text1: "Add money via mobile money",
      icon: svg.userSquare,
    },
    {
      type: "P2P transfer",
      text1: "Add money via P2p transfer",
      icon: svg.p2p,
    },
  ];

  return (
    <ScrollView>
      <View style={styles.viewContent}>
        <P
          style={{
            fontSize: 12,
            color: colors.gray,
            font: fonts.poppinsRegular,
            marginBottom: 8,
          }}
        >
          Choose a payment method to add money
        </P>
        {paymentType.map((item, index) => {
          return (
            <ListItemSelect
              text1={item.type}
              text2={item.text1}
              icon={item.icon}
              key={index}
              isActive={activeType === index}
              onPress={() => {
                setActiveType(index);
                setActiveTypeName(item.type)
                setTimeout(() => {
                  onPress();
                }, 1000);
              }}
              containerStyle={{ marginBottom: 16 }}
            />
          );
        })}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  viewContent: {
    width: "100%",
    paddingTop: 24,
  },
});
