import React, { useState } from "react";
import { StyleSheet, View } from "react-native";
import WheelDatePicker from "./WheelDatePicker";

interface DatePickerProps {
  selectedDate: string | Date;
  onDateChange: (date: string) => void;
  closeModal: () => void;
  dateofbirth?: boolean;
}

const DateOfBirthPicker: React.FC<DatePickerProps> = ({
  selectedDate,
  onDateChange,
  closeModal,
}) => {
  const [isDatePickerVisible] = useState(true);

  // Function to format date to YYYY-MM-DD
  const formatDateToString = (date: {
    month: string;
    day: number;
    year: number;
  }): string => {
    if (!date) return "";

    const monthIndex =
      [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
      ].indexOf(date.month) + 1;

    const month = String(monthIndex).padStart(2, "0");
    const day = String(date.day).padStart(2, "0");
    return `${date.year}-${month}-${day}`;
  };

  // Parse the selectedDate to get initial values
  const getInitialDate = () => {
    if (!selectedDate) return null;

    const date =
      typeof selectedDate === "string" ? new Date(selectedDate) : selectedDate;

    const months = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];

    return {
      month: months[date.getMonth()],
      day: date.getDate(),
      year: date.getFullYear(),
    };
  };

  // Handle date selection from the custom picker
  const handleDateSelected = (date: {
    month: string;
    day: number;
    year: number;
  }) => {
    const formattedDate = formatDateToString(date);
    onDateChange(formattedDate);
    closeModal();
  };

  return (
    <View style={styles.container}>
      <WheelDatePicker
        isVisible={isDatePickerVisible}
        onClose={closeModal}
        onDateSelected={handleDateSelected}
        initialDate={getInitialDate()}
        useModal={false}
        dateOfBirth={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    width: "100%",
    height: "100%",
  },
});

export default DateOfBirthPicker;
