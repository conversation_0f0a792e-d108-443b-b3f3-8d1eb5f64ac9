import {
  Dimensions,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  TextStyle,
} from "react-native";
import React, { CSSProperties } from "react";
import { Feather } from "@expo/vector-icons";
import P from "./P";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
const { width, height } = Dimensions.get("window");

interface PProps {
  navigation?: any;
  text?: string;
  iconComp?: any;
  contStyle?: CSSProperties;
  navStyle?: CSSProperties;
  showBorder?: boolean;
  iconBlack?: boolean;
  textStyle?: TextStyle;
  iconWhite?: boolean
}

export default function AuthenticationHedear2({
  navigation,
  text,
  iconComp,
  contStyle,
  navStyle,
  showBorder,
  iconBlack,
  textStyle, 
  iconWhite
}: PProps) {
  return (
    // @ts-ignore
    <View style={[styles.navCont, contStyle, showBorder && styles.navBorder]}>
      {/* @ts-ignore */}
      <View style={[styles.nav, navStyle]}>
        <View style={{ flexDirection: "row" }}>
          <TouchableOpacity
            onPress={() => {
              navigation.pop();
            }}
            style={{ flexDirection: "row" }}
          >
            <SvgXml
              xml={iconBlack ? svg.goBackIconBlack:  iconWhite? svg.goBackIconWhite  : svg.goBackIcon}
              color={"#000"}
            />
            <P style={[styles.navText, textStyle]}>{text}</P>
          </TouchableOpacity>
          <P style={styles.navText}> </P>
        </View>
        {iconComp}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  navCont: {
    width,
    height: 24,
    alignItems: "center",
    marginTop: 24,
    marginBottom: 16,
    // backgroundColor:"green",
  },
  nav: {
    width: "90%",
    flexDirection: "row",
    height: "100%",
    alignItems: "center",
    justifyContent: "space-between",
    paddingTop: 2,
  },
  navText: {
    // fontSize: (4.5 * width) / 100,
    color: "#000",
    fontFamily: fonts.poppinsMedium,
    marginLeft: 12,
  },
  navBorder: {
    borderBottomWidth: 1,
    borderColor: "#313030",
  },
});
