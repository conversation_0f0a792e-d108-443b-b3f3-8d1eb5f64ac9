export function formatDate(dateString) {
  const date = new Date(dateString);

  // Get the hours and minutes
  let hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, "0");

  // Determine AM/PM
  const ampm = hours >= 12 ? "pm" : "am";
  hours = hours % 12 || 12; // Convert 24-hour to 12-hour format, using 12 for 0

  // Get day, month, and year
  const day = date.getDate();
  const month = date.toLocaleString("default", { month: "short" });
  const year = date.getFullYear();

  // Format the result as "6:00 am • 12 jul 2014"
  return `${hours}:${minutes} ${ampm} • ${day} ${month} ${year}`;
}

export function formatDate2(dateString) {
  const date = new Date(dateString);
  const currentDate = new Date();

  // Check if the date is today
  if (
    date.getDate() === currentDate.getDate() &&
    date.getMonth() === currentDate.getMonth() &&
    date.getFullYear() === currentDate.getFullYear()
  ) {
    return `Today`;
  }

  // Check if the date is yesterday
  const yesterday = new Date(currentDate);
  yesterday.setDate(yesterday.getDate() - 1);
  if (
    date.getDate() === yesterday.getDate() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getFullYear() === yesterday.getFullYear()
  ) {
    return `Yesterday`;
  }

  // For other dates
  const options = {
    day: "2-digit",
    month: "short",
    year: "numeric",
    // hour: "2-digit",
    // minute: "2-digit",
  };
  // @ts-ignore
  return date.toLocaleDateString("en-US", options);
}
export function formatDate3(dateString) {
  const date = new Date(dateString);

  // Format and return only the time
  return date.toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
  });
}
