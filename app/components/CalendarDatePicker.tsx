import React, { useState, useEffect, useRef } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Text,
  Dimensions,
  ScrollView,
  FlatList,
  SafeAreaView,
  Platform,
} from "react-native";
import { Calendar, DateData } from "react-native-calendars";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import P from "./P";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";

const { height, width } = Dimensions.get("window");

interface DatePickerProps {
  isVisible?: boolean;
  onClose?: () => void;
  onDateSelected: (date: { month: string; day: number; year: number }) => void;
  initialDate?: { month: string; day: number; year: number };
  useModal?: boolean;
}

// Month names for conversion
const monthNames = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const CalendarDatePicker: React.FC<DatePickerProps> = ({
  isVisible = true,
  onClose = () => {},
  onDateSelected,
  initialDate,
  useModal = true,
}) => {
  // Convert initialDate to YYYY-MM-DD format for the calendar
  const getInitialDate = () => {
    if (initialDate) {
      const monthIndex = monthNames.indexOf(initialDate.month);
      if (monthIndex !== -1) {
        // Format: YYYY-MM-DD
        return `${initialDate.year}-${String(monthIndex + 1).padStart(
          2,
          "0"
        )}-${String(initialDate.day).padStart(2, "0")}`;
      }
    }
    // Default to current date
    const today = new Date();
    return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(
      2,
      "0"
    )}-${String(today.getDate()).padStart(2, "0")}`;
  };

  // State for selected date
  const [selectedDate, setSelectedDate] = useState<string>(getInitialDate());
  const [markedDates, setMarkedDates] = useState<{ [key: string]: any }>({});
  const [showMonthSelector, setShowMonthSelector] = useState(false);
  const [showYearSelector, setShowYearSelector] = useState(false);

  // Extract current month and year from selected date
  const [year, month] = selectedDate.split("-").map((num) => parseInt(num));

  // Generate years array (from 1900 to current year + 1)
  const currentYear = new Date().getFullYear();
  const years = Array.from(
    { length: currentYear - 1900 + 2 },
    (_, i) => 1900 + i
  );

  // Update marked dates when selected date changes
  useEffect(() => {
    setMarkedDates({
      [selectedDate]: {
        selected: true,
        selectedColor: colors.primary,
      },
    });
  }, [selectedDate]);

  const handleDayPress = (day: DateData) => {
    setSelectedDate(day.dateString);
    setShowMonthSelector(false);
    setShowYearSelector(false);
  };

  const handleMonthPress = () => {
    setShowMonthSelector(!showMonthSelector);
    setShowYearSelector(false);
  };

  const handleYearPress = () => {
    setShowYearSelector(!showYearSelector);
    setShowMonthSelector(false);
  };

  const handleMonthSelect = (monthIndex: number) => {
    // Create a new date with the selected month
    const newDate = new Date(selectedDate);
    newDate.setMonth(monthIndex);

    // Format the date as YYYY-MM-DD
    const formattedDate = `${newDate.getFullYear()}-${String(
      newDate.getMonth() + 1
    ).padStart(2, "0")}-${String(newDate.getDate()).padStart(2, "0")}`;
    setSelectedDate(formattedDate);
    setShowMonthSelector(false);
  };
  const handleYearSelect = (selectedYear: number) => {
    // Create a new date with the selected year
    const newDate = new Date(selectedDate);
    newDate.setFullYear(selectedYear);

    // Format the date as YYYY-MM-DD
    const formattedDate = `${newDate.getFullYear()}-${String(
      newDate.getMonth() + 1
    ).padStart(2, "0")}-${String(newDate.getDate()).padStart(2, "0")}`;
    setSelectedDate(formattedDate);
    setShowYearSelector(false);
  };

  const handleApply = () => {
    // Parse the selected date
    const [year, month, day] = selectedDate
      .split("-")
      .map((num) => parseInt(num));

    // Convert month number to month name
    const monthName = monthNames[month - 1];

    onDateSelected({
      month: monthName,
      day: day,
      year: year,
    });

    onClose();
  };

  // The actual date picker content
  const renderDatePickerContent = () => (
    <View style={styles.modalContainer}>
      {/* Custom Month/Year Selector */}
      <View style={styles.dateSelector}>
        <TouchableOpacity
          style={styles.selectorButton}
          onPress={handleMonthPress}
        >
          <P style={styles.selectorButtonText}>{monthNames[month - 1]}</P>
            <SvgXml xml={svg.arrowDownThick} width={24} height={24} />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.selectorButton}
          onPress={handleYearPress}
        >
          <Text style={styles.selectorButtonText}>{year}</Text>
          <SvgXml xml={svg.arrowDownThick} width={24} height={24} />
        </TouchableOpacity>
      </View>

      {/* Month Selector */}
      {showMonthSelector && (
        <View style={styles.selectorContainer}>
          <FlatList
            data={monthNames}
            numColumns={width < 350 ? 2 : 3}
            keyExtractor={(item) => item}
            showsVerticalScrollIndicator={false}
            renderItem={({ item, index }) => (
              <TouchableOpacity
                style={[
                  styles.monthItem,
                  month - 1 === index && styles.selectedItem,
                ]}
                onPress={() => handleMonthSelect(index)}
              >
                <P
                  style={[
                    styles.monthItemText,
                    month - 1 === index && styles.selectedItemText,
                  ]}
                >
                  {item.substring(0, 3)}
                </P>
              </TouchableOpacity>
            )}
          />
        </View>
      )}

      {/* Year Selector */}
      {showYearSelector && (
        <View style={styles.selectorContainer}>
          <FlatList
            data={years}
            numColumns={width < 350 ? 3 : 4}
            keyExtractor={(item) => item.toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[styles.yearItem, year === item && styles.selectedItem]}
                onPress={() => handleYearSelect(item)}
              >
                <P
                  style={[
                    styles.yearItemText,
                    year === item && styles.selectedItemText,
                  ]}
                >
                  {item}
                </P>
              </TouchableOpacity>
            )}
            initialScrollIndex={
              years.findIndex((y) => y === year) > 0
                ? Math.floor(
                    years.findIndex((y) => y === year) / (width < 350 ? 3 : 4)
                  ) - 2
                : 0
            }
            getItemLayout={(_, index) => ({
              length: 50,
              offset: 50 * Math.floor(index / (width < 350 ? 3 : 4)),
              index,
            })}
            showsVerticalScrollIndicator={false}
          />
        </View>
      )}

      {/* Only show calendar when selectors are hidden */}
      {!showMonthSelector && !showYearSelector && (
        <Calendar
          current={selectedDate}
          onDayPress={handleDayPress}
          markedDates={markedDates}
          enableSwipeMonths={true}
          hideExtraDays={false}
          theme={{
            backgroundColor: "#ffffff",
            calendarBackground: "#ffffff",
            textSectionTitleColor: "#b6c1cd",
            selectedDayBackgroundColor: colors.primary,
            selectedDayTextColor: "#ffffff",
            todayTextColor: colors.primary,
            dayTextColor: "#2d4150",
            textDisabledColor: "#d9e1e8",
            dotColor: colors.primary,
            selectedDotColor: "#ffffff",
            arrowColor: colors.primary,
            monthTextColor: "#2d4150",
            textDayFontFamily: fonts.poppinsRegular,
            textMonthFontFamily: fonts.poppinsMedium,
            textDayHeaderFontFamily: fonts.poppinsRegular,
            textDayFontSize: width < 350 ? 14 : 16,
            textMonthFontSize: width < 350 ? 16 : 18,
            textDayHeaderFontSize: width < 350 ? 12 : 14,
          }}
        />
      )}

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
          <P style={styles.cancelButtonText}>Cancel</P>
        </TouchableOpacity>

        <TouchableOpacity style={styles.applyButton} onPress={handleApply}>
          <P style={styles.applyButtonText}>Apply</P>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Conditionally render with or without modal
  return useModal ? (
    <Modal
      visible={isVisible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <SafeAreaView style={styles.modalOverlay}>
        {renderDatePickerContent()}
      </SafeAreaView>
    </Modal>
  ) : (
    renderDatePickerContent()
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: Platform.OS === "ios" ? 30 : 20,
    paddingTop: Platform.OS === "ios" ? 15 : (5 * height) / 100,
    paddingLeft: 0,
    paddingRight: 0,
    width: "100%",
    maxHeight: Platform.OS === "ios" ? height * 0.8 : height * 0.7,
  },
  header: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 16,
    position: "relative",
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: fonts.poppinsMedium,
  },
  closeButton: {
    position: "absolute",
    right: 20,
    top: 16,
  },
  closeButtonText: {
    fontSize: 18,
    color: colors.gray,
  },
  divider: {
    height: 4,
    width: 40,
    backgroundColor: "#E6E6E6",
    alignSelf: "center",
    marginBottom: 16,
    borderRadius: 2,
  },
  // Month/Year selector styles
  dateSelector: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 10,
    marginBottom: 10,
  },
  selectorButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginHorizontal: 5,
    borderRadius: 20,
    backgroundColor: "#F5F2FF",
    flexDirection: "row", 
    alignItems: "center", 
    justifyContent: "center"
  },
  selectorButtonText: {
    color: colors.primary,
    fontFamily: fonts.poppinsMedium,
    fontSize: 16,
  },
  selectorContainer: {
    marginVertical: 10,
    paddingHorizontal: width < 350 ? 5 : 10,
    height: Platform.OS === "ios" ? 250 : Math.min(250, height * 0.4),
  },
  monthItem: {
    flex: 1,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    margin: width < 350 ? 3 : 5,
    borderRadius: 10,
    backgroundColor: "#F5F5F5",
    minWidth: width < 350 ? 70 : 90,
  },
  yearItem: {
    flex: 1,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    margin: width < 350 ? 3 : 5,
    borderRadius: 10,
    backgroundColor: "#F5F5F5",
    minWidth: width < 350 ? 60 : 70,
  },
  selectedItem: {
    backgroundColor: colors.primary,
  },
  monthItemText: {
    fontSize: width < 350 ? 12 : 14,
    color: colors.dGray,
    fontFamily: fonts.poppinsRegular,
  },
  yearItemText: {
    fontSize: width < 350 ? 12 : 14,
    color: colors.dGray,
    fontFamily: fonts.poppinsRegular,
  },
  selectedItemText: {
    color: "white",
    fontFamily: fonts.poppinsMedium,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 25,
    borderWidth: 1,
    borderColor: "#E6E6E6",
    marginRight: 10,
  },
  cancelButtonText: {
    color: colors.primary,
  },
  applyButton: {
    flex: 1,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 25,
    backgroundColor: colors.primary,
    marginLeft: 10,
  },
  applyButtonText: {
    color: "white",
  },
});

export default CalendarDatePicker;
