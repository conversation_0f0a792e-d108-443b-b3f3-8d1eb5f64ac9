import React, { CSSProperties, useContext, useEffect, useState } from "react";
import {
  View,
  TouchableOpacity,
  Keyboard,
  Platform,
  StyleSheet,
  Dimensions,
} from "react-native";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "./P";
import Link from "./Link";
import BottomSheet from "./BottomSheet";
import { colors } from "../config/colors";
import { LanguageContext } from "../context/LanguageContext";
import i18n from "../../i18n";

const { width, height } = Dimensions.get("window");
const baseHeight = 800;
const languages = [
  { lang: "English", code: "en" },
  { lang: "Türkçe", code: "tr" },
];

interface PProps {
  contStyle?: CSSProperties;
  navigation?: any;
  absolute?: boolean;
}

export default function BottomComponent({
  absolute = true,
  navigation,
  contStyle,
}: PProps) {
  // @ts-ignore
  const { language, changeLanguage } = useContext(LanguageContext); // Consume the context
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [langSelector, setLangSelector] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  const activeLang =
    languages.find((lang) => lang.code === language) || languages[0];

  return (
    <View
      style={[
        styles.bottomCont,
        // @ts-ignore
        contStyle,
        {
          opacity: keyboardVisible ? 0 : 1,
          position: absolute ? "absolute" : "relative",
        },
      ]}
    >
      <TouchableOpacity
        style={styles.bottomIcons}
        onPress={() => setLangSelector(true)}
      >
        <SvgXml xml={svg.internet2} />
        <P style={styles.bottomIconsText}>{activeLang.lang}</P>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.bottomIcons}
        onPress={() => {
          navigation.navigate("Rate2");
        }}
      >
        <SvgXml xml={svg.rate} />
        <P style={styles.bottomIconsText}>{i18n.t("bComp.rate")}</P>
      </TouchableOpacity>
      {/* <TouchableOpacity
        style={styles.bottomIcons}
        onPress={() => navigation.navigate("Discover2")}
      >
        <SvgXml xml={svg.discover} />
        <P style={styles.bottomIconsText}>{i18n.t("bComp.discover")}</P>
      </TouchableOpacity> */}

      <BottomSheet
        isVisible={langSelector}
        onClose={() => setLangSelector(false)}
        showBackArrow={false}
        backspaceText={i18n.t("language")}
        modalContentStyle={{ height: "35%" }}
        extraModalStyle={{ height: "33%" }}
        components={
          <View style={{ marginTop: 24 }}>
            {languages.map((item, index) => (
              <TouchableOpacity
                key={index}
                style={{
                  paddingTop: 13,
                  paddingBottom: 13,
                  paddingLeft: 16,
                  paddingRight: 16,
                  marginBottom: 16,
                  backgroundColor:
                    language === item.code
                      ? colors.lowOpPrimary3
                      : "transparent",
                  borderRadius: 8,
                }}
                onPress={() => {
                  changeLanguage(item); // Change the language globally
                  setLangSelector(false);
                }}
              >
                <P style={{ fontSize: 12 }}>{item.lang}</P>
              </TouchableOpacity>
            ))}
          </View>
        }
      />
    </View>
  );
}

// Styles remain the same

const styles = StyleSheet.create({
  bottomCont: {
    position: "absolute",
    bottom: 0,
    paddingHorizontal: "15%",
    width: "100%",
    flexDirection: "row",
    backgroundColor: colors.white,
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: Platform.OS === "ios"? 16 :  8,
  },
  bottomIcons: {
    width: 104,
    height: 22,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  bottomIconsText: {
    fontSize: 12,
    lineHeight: 22,
    color: colors.black,
    marginLeft: 4,
    textDecorationLine: "underline",
    textDecorationColor: "#A5A1A1",
  },
});
