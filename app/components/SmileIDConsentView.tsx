import React from 'react';
import type { ViewStyle } from 'react-native';
import type { DirectEventHandler } from 'react-native/Libraries/Types/CodegenTypes';
import NativeSmileIDConsentView from './SmileIDConsentViewNativeComponent';

interface SmileIDConsentViewProps {
  style?: ViewStyle;
  onResult?: DirectEventHandler<{
    result: string;
    error?: string;
  }>;
}

/**
 * SmileIDConsentView is a wrapper around the native SmileIDConsentView component.
 * This component is used to display the Smile ID consent screen.
 *
 * @param props - The component props
 * @returns A React component
 */
const SmileIDConsentView: React.FC<SmileIDConsentViewProps> = (props) => {
  return <NativeSmileIDConsentView {...props} />;
};

export default SmileIDConsentView;
