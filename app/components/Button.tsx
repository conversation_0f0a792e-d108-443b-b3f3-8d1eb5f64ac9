import React, { useEffect, useState } from "react";
import { Dimensions, StyleSheet, TouchableOpacity, View, ViewStyle, TextStyle, StyleProp } from "react-native";
import { colors } from "../config/colors";
import P from "./P";
const { height } = Dimensions.get("window");
const baseHeight = 800;

interface ButtonProps {
  onPress?: () => void;
  btnText?: string;
  icon?: React.ReactNode;
  type?: "alt";
  style?: StyleProp<ViewStyle>;
  disabled?: boolean;
  btnTextStyle?: StyleProp<TextStyle>;
  loading?: boolean;
}

export default function Button({
  onPress,
  btnText,
  icon,
  type,
  style,
  disabled,
  btnTextStyle,
  loading = false,
}: ButtonProps) {
  const [activeDot, setActiveDot] = useState(0);

  // Create an array of numbers [1, 2, 3] for the dots
  const dotsArray = [1, 2, 3];

  // Cycle through the dots every 500ms
  useEffect(() => {
    if (loading) {
      const interval = setInterval(() => {
        setActiveDot((prevDot) => (prevDot + 1) % dotsArray.length); // Cycle through dots
      }, 200);
      return () => clearInterval(interval);
    }
  }, [loading]);

  return (
    <TouchableOpacity onPress={onPress} disabled={disabled || loading}>
      <View
        style={[
          styles.btnCont,
          {
            backgroundColor: type === "alt" ? "transparent" : colors.primary,
            borderColor: type === "alt" ? colors.stroke : "transparent",
            opacity: disabled? 0.5: 1
          },
          style,
        ]}
      >
        {loading ? (
          <View style={styles.loaderContainer}>
            {dotsArray.map((dot, index) => (
              <View
                key={dot}
                style={[
                  styles.dot,
                  {
                    backgroundColor:
                      activeDot === index
                        ? type === "alt"
                          ? colors.black
                          : colors.white
                        : type === "alt"
                        ? colors.gray
                        : "#D0B8FF",
                  },
                ]}
              />
            ))}
          </View>
        ) : (
          <>
            {btnText && <P style={[styles.btnText, btnTextStyle]}>{btnText}</P>}
            {icon}
          </>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  btnCont: {
    width: "100%",
    height: (44 / baseHeight) * height,
    borderWidth: 1,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  btnText: {
    fontSize: 14,
    color: colors.white,
  },
  loaderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: 30, // Space for the 3 dots
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 100,
    backgroundColor: colors.gray, // Default inactive color
    marginHorizontal: 2,
  },
});
