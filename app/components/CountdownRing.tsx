import React, { useEffect, useState, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Animated,
  Easing,
  Platform,
} from "react-native";
import P from "./P";

import { fonts } from "../config/Fonts";
import { colors } from "../config/colors";

const { width } = Dimensions.get("window");

// Helper component for a single animated digit with slide-down animation
const AnimatedDigit = ({ digit, color, fontSize }) => {
  const [oldDigit, setOldDigit] = useState(digit);
  const translateY = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (digit !== oldDigit) {
      // Animate current digit out
      Animated.timing(translateY, {
        toValue: 45,
        duration: 200,
        easing: Easing.linear,
        useNativeDriver: true,
      }).start(() => {
        setOldDigit(digit);
        translateY.setValue(-45); // Reset position for new digit to slide in from top
        // Animate new digit in
        Animated.timing(translateY, {
          toValue: 0,
          duration: 200,
          easing: Easing.linear,
          useNativeDriver: true,
        }).start();
      });
      Animated.timing(opacity, {
        toValue: 0,
        duration: 200,
        easing: Easing.linear,
        useNativeDriver: true,
      }).start(() => {
        Animated.timing(opacity, {
          toValue: 1,
          duration: 200,
          easing: Easing.linear,
          useNativeDriver: true,
        }).start();
      });
    }
  }, [digit, oldDigit]);

  return (
    <View style={styles.digitBox}>
      {/* Render the old digit for the slide-out effect */}
      {digit !== oldDigit && (
        <Animated.Text
          style={[
            styles.digitText,
            { color: color, fontSize: fontSize, position: "absolute" },
            {
              transform: [{ translateY: translateY }],
              opacity: Animated.multiply(opacity, new Animated.Value(0.5)),
            },
          ]}
        >
          {oldDigit}
        </Animated.Text>
      )}
      {/* Render the new digit for the slide-in effect */}
      <Animated.Text
        style={[
          styles.digitText,
          { color: color, fontSize: fontSize, position: "absolute" },
          { transform: [{ translateY: translateY }], opacity: opacity },
        ]}
      >
        {digit}
      </Animated.Text>
    </View>
  );
};

const CountdownRing = ({ totalSeconds = 1000 }) => {
  const [timeLeft, setTimeLeft] = useState(totalSeconds);
  const [newTimeleft, setNewTimeLeft] = useState(totalSeconds);

  useEffect(() => {
    const interval = setInterval(() => {
      setTimeLeft((prev) => {
        const newTime = prev > 0 ? prev - 1 : 0;
        setNewTimeLeft(newTime);
        console.log("Time left:", newTime);
        return newTime;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const formatTime = (seconds) => {
    const days = Math.floor(seconds / (3600 * 24));
    const remainingSecondsAfterDays = seconds % (3600 * 24);
    const hrs = Math.floor(remainingSecondsAfterDays / 3600);
    const mins = Math.floor((remainingSecondsAfterDays % 3600) / 60);
    const sec = remainingSecondsAfterDays % 60;

    return {
      days: String(days).padStart(2, "0"),
      hours: String(hrs).padStart(2, "0"),
      minutes: String(mins).padStart(2, "0"),
      seconds: String(sec).padStart(2, "0"),
    };
  };

  const { days, hours, minutes, seconds } = formatTime(timeLeft);

  return (
    <View style={styles.container}>
      <P style={styles.countdownTitle}>Countdown</P>
      <View style={styles.timeUnitsWrapper}>
        {/* Days */}
        {newTimeleft > 86400 ? (
          <>
            <View style={styles.timeUnitGroup}>
              <View style={styles.digitRow}>
                <View style={styles.roundOn} />
                <AnimatedDigit
                  digit={days.charAt(0)}
                  color={colors.black}
                  fontSize={20}
                />
                <View style={styles.divider} />
                <AnimatedDigit
                  digit={days.charAt(1)}
                  color={colors.black}
                  fontSize={20}
                />
                <View style={styles.roundOn1} />
              </View>
              <P style={styles.timeUnitLabel}>Days</P>
            </View>

            <P style={styles.colon}>:</P>
          </>
        ) : (
          <></>
        )}

        {/* Hours */}
        <View style={styles.timeUnitGroup}>
          <View style={styles.digitRow}>
            <View style={styles.roundOn} />
            <AnimatedDigit
              digit={hours.charAt(0)}
              color={colors.black}
              fontSize={20}
            />
            <View style={styles.divider} />
            <AnimatedDigit
              digit={hours.charAt(1)}
              color={colors.black}
              fontSize={20}
            />
            <View style={styles.roundOn1} />
          </View>
          <P style={styles.timeUnitLabel}>Hours</P>
        </View>

        <P style={styles.colon}>:</P>

        {/* Minutes */}
        <View style={styles.timeUnitGroup}>
          <View style={styles.digitRow}>
            <View style={styles.roundOn} />
            <AnimatedDigit
              digit={minutes.charAt(0)}
              color={colors.black}
              fontSize={20}
            />
            <View style={styles.divider} />
            <AnimatedDigit
              digit={minutes.charAt(1)}
              color={colors.black}
              fontSize={20}
            />
            <View style={styles.roundOn1} />
          </View>
          <P style={styles.timeUnitLabel}>Minutes</P>
        </View>
        {newTimeleft < 86400 ? (
          <>
            <P style={styles.colon}>:</P>
            {/* seconds*/}
            <View style={styles.timeUnitGroup}>
              <View style={styles.digitRow}>
                <View style={styles.roundOn} />
                <AnimatedDigit
                  digit={seconds.charAt(0)}
                  color={colors.black}
                  fontSize={20}
                />
                <View style={styles.divider} />
                <AnimatedDigit
                  digit={seconds.charAt(1)}
                  color={colors.black}
                  fontSize={20}
                />
                <View style={styles.roundOn1} />
              </View>
              <P style={styles.timeUnitLabel}>Seconds</P>
            </View>
          </>
        ) : (
          <></>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    // paddingVertical: 20,
    padding: 16,
    borderRadius: 12,
    backgroundColor: colors.white,
  },
  countdownTitle: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
    marginBottom: 10,
  },
  timeUnitsWrapper: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  timeUnitGroup: {
    alignItems: "center",
  },
  digitRow: {
    flexDirection: "row",
    gap: 8,
  },
  digitBox: {
    minWidth: 35,
    minHeight: 46,
    backgroundColor: colors.secBackground,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    padding: 8,
    borderWidth: 1,
    borderColor: colors.primary,
    overflow: "hidden",
  },
  digitText: {
    textAlign: "center",
    fontSize: 20,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
  },
  timeUnitLabel: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.dark500,
    marginTop: 4,
  },
  colon: {
    fontSize: 30,
    lineHeight: 30,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
    // marginHorizontal: 4,
    marginBottom: 15,
    alignSelf: "center",
    textAlign: "center",
  },
  divider: {
    width: Platform.OS === "ios" ? 17 : 18,
    height: 10,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderColor: colors.primary,
    alignSelf: "center",
    borderRadius: 100,
    position: "absolute",
    left: "40%",
    transform: [{ translateX: -1 }],
    zIndex: 100,
    backgroundColor: colors.white,
  },
  roundOn: {
    width: Platform.OS === "ios" ? 17 : 18,
    height: 10,
    backgroundColor: "white",
    borderRadius: 100,
    position: "absolute",
    left: "-17%",
    // transform: [{ translateX: 1 }],
    zIndex: 100,
    borderRightWidth: 1,
    borderColor: colors.primary,
    // top: "30%",
    alignSelf: "center",
  },
  roundOn1: {
    width: Platform.OS === "ios" ? 17 : 18,
    height: 10,
    backgroundColor: "white",
    borderRadius: 100,
    position: "absolute",
    right: "-17%",
    // transform: [{ translateX: 1 }],
    zIndex: 100,
    borderLeftWidth: 1,
    borderColor: colors.primary,
    // top: "30%",
    alignSelf: "center",
  },
});

export default CountdownRing;
