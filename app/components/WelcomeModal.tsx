import React, { useEffect, useRef } from "react";
import {
  Dimensions,
  StyleSheet,
  View,
  Image,
  TouchableOpacity,
  Animated,
  Easing,
} from "react-native";
import { colors } from "../config/colors";
import P from "./P";
import H4 from "./H4";
import { fonts } from "../config/Fonts";
import Button from "./Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useWelcomeModal } from "../context/WelcomeModalContext";
import { CommonActions } from "@react-navigation/native";
import { navigationRef } from "../navigation/navigationRef";

const { width, height } = Dimensions.get("window");

function WelcomeModal() {
  const { isWelcomeModalVisible, hideWelcomeModal, navigation } = useWelcomeModal();
  const slideAnim = useRef(new Animated.Value(height)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const removeFlag = async () => {
    await AsyncStorage.removeItem("newUser");
  };

  useEffect(() => {
    if (isWelcomeModalVisible) {
      // Reset animations
      slideAnim.setValue(height);
      fadeAnim.setValue(0);

      // Start animations
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 400,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isWelcomeModalVisible]);

  const handleClose = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: height,
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 250,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        useNativeDriver: true,
      }),
    ]).start(() => {
      hideWelcomeModal();
      removeFlag();
    });
  };

  const handleGetStarted = () => {
    removeFlag();
    if (navigation) {
      navigation.navigate("AccountVerificationPromt");
    }
    handleClose();
  };

  if (!isWelcomeModalVisible) return null;

  return (
    <View style={styles.modal}>
      <Animated.View 
        style={[
          styles.overlay,
          {
            opacity: fadeAnim,
          }
        ]}
      />
      <View style={styles.mCont}>
        <Animated.View
          style={[
            styles.modalCard,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <TouchableOpacity
            style={{ position: "absolute", top: 20, right: 20 }}
            onPress={handleClose}
          >
            <SvgXml xml={svg.xClose} />
          </TouchableOpacity>
          <Image
            source={require("../assets/celeb.png")}
            style={{ width: 80, height: 80 }}
          />
          <View style={{ marginTop: 24 }}>
            <H4 style={{ textAlign: "center" }}>Welcome to SFx money app</H4>
            <P
              style={{
                textAlign: "center",
                fontSize: 12,
                fontFamily: fonts.poppinsRegular,
              }}
            >
              Verify your account to Send money instantly to 14 African
              countries via Mobile Money & Bank Transfer.
            </P>
          </View>
          <View style={{ marginTop: 32, width: "75%" }}>
            <Button
              btnText="Get started"
              onPress={handleGetStarted}
            />
          </View>
        </Animated.View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  modal: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1000,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "#171717D9",
  },
  mCont: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  modalCard: {
    width: "90%",
    minHeight: "50%",
    backgroundColor: colors.white,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});

export default WelcomeModal;
