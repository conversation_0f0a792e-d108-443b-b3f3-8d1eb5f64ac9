import React, { CSSProperties, useState, useEffect } from "react";
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Dimensions,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import Modal from "react-native-modal";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import { fonts } from "../config/Fonts";
import { colors } from "../config/colors";

interface PProps {
  isVisible?: any;
  onClose?: any;
  components?: any;
  backspaceText?: string;
  showBackArrow?: Boolean;
  onBackArrowPress?: any;
  extraModalStyle?: CSSProperties;
  modalContentStyle?: CSSProperties;
  statusBarTranslucent?: true | false;
}

const { width, height } = Dimensions.get("window");
export default function BottomSheet2({
  isVisible,
  onClose,
  components,
  backspaceText,
  showBackArrow = true,
  onBackArrowPress,
  extraModalStyle,
  modalContentStyle,
  statusBarTranslucent = true,
}: PProps) {
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  return (
    <Modal
      isVisible={isVisible}
      style={styles.modal}
      statusBarTranslucent={statusBarTranslucent}
    >
      <View
        style={[
          styles.modalContent,
          { marginBottom: keyboardVisible ? 200 : -40 },
          // @ts-ignore
          modalContentStyle,
        ]}
      >
        {components}
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modal: {
    // backgroundColor: 'red',
    justifyContent: "flex-end",
    margin: 0,
    height,
    position: "absolute",
  },
  modalContent: {
    height: "55%",
    width,
    backgroundColor: colors.secBackground,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 0,
    marginBottom: -40,
  },
  extraModal: {
    width: "80%",
    height: "53%",
    position: "absolute",
    alignSelf: "center",
    borderRadius: 20,
    backgroundColor: "#fff",
  },
  section1: {
    height: 61,
    // backgroundColor: 'red',
    width: "100%",
    borderBottomColor: "rgba(139, 144, 154, 0.25)",
    borderBottomWidth: 1,
    flexDirection: "row",
    alignItems: "center",
    marginTop: 11,
  },
  bodyWrapper: {
    height: "50%",
    //    backgroundColor: 'red',
  },
});
