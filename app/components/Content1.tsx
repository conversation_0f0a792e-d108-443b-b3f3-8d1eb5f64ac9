import {
  StyleSheet,
  Text,
  View,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import React from "react";
import { CSSProperties } from "react";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "./P";
const { width, height } = Dimensions.get("window");

interface PProps {
  header: string;
  style?: CSSProperties;
  body?: string;
  svg1?: any;
  onPress?: any;
  bottomBorder?: boolean;
  sWidth?: number;
  sHeight?: number;
  arrowRight?: Boolean;
  rightComponent?: any;
  rightComponent1?: any;
}

export default function Content1({
  header,
  body,
  svg1,
  onPress,
  bottomBorder = true,
  sWidth = 24,
  sHeight = 24,
  arrowRight = true,
  rightComponent,
  rightComponent1,
  style,
}: PProps) {
  return (
    <TouchableOpacity
      style={[
        styles.content,
        { borderBottomWidth: bottomBorder ? 1 : 0 },
        // @ts-ignore
        style,
      ]}
      onPress={onPress}
    >
      <View
        style={{
          width: rightComponent1 ? "55%" : "80%",
          flexDirection: "row",
          alignItems: "center", 
          gap: 9
        }}
      >
        <SvgXml xml={svg1} width={sWidth} height={sHeight} />
        {/* @ts-ignore */}
        <P style={styles.contentHeader}>{header}</P>
        {/* @ts-ignore */}
      </View>
      <View style={{flexDirection: "row", alignItems: "center", gap: 4}}>
      {rightComponent1}
      {arrowRight && <SvgXml xml={svg.arrowBlack} />}
      </View>
      {rightComponent}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  content: {
    width: "90%",
    height: 72,
    borderBottomWidth: 1,
    borderColor: "#E6E5E5",
    // backgroundColor:"red",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  contentHeader: { fontSize: 12, fontFamily: fonts.poppinsMedium },
  contentText: {
    fontSize: 12,
    color: "#A5A1A1",
    fontFamily: fonts.poppinsRegular,
  },
});
