import React, { useEffect, useRef, useState, useContext } from "react";
import {
  Animated,
  Dimensions,
  Modal,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
  Keyboard,
  TextInput,
  Alert,
} from "react-native";
import { SvgXml } from "react-native-svg";
import { Circle, Svg } from "react-native-svg";
import { colors } from "../config/colors";
import { svg } from "../config/Svg";
import P from "./P";
import Div from "./Div";
import { fonts } from "../config/Fonts";
import Button from "./Button";
import * as LocalAuthentication from "expo-local-authentication";
import { GetUserDetails, ValidatePin } from "../RequestHandlers/User";
import { LanguageContext } from "../context/LanguageContext";
import i18n from "../../i18n";
import Link from "./Link";
import { useToast } from "../context/ToastContext";
import { encryptPIN } from "../Utils/encrypt";
import * as SecureStore from "expo-secure-store";
import { FingerPrintStatus } from "../context/FingerPrintContext";
import { ActivityIndicator } from "react-native";

const { width, height } = Dimensions.get("window");

interface PProps {
  visible?: boolean;
  onClose?: any;
  secondaryFunction?: any;
}

const radius = 150; // Adjust to change the size of the circle
const strokeWidth = 5; // Adjust the thickness of the circle stroke
const AnimatedCircle = Animated.createAnimatedComponent(Circle);
const baseHeight = 800;
const baseWidth = 360;
export default function EnableBiometricComponent({
  visible,
  onClose,
  secondaryFunction,
}: PProps) {
  const calculateCircumference = (radius) => 2 * Math.PI * radius;
  const [percentage, setPercentage] = useState(0); // Progress percentage
  const animatedValue = useRef(new Animated.Value(0)).current; // Animated value
  const [animationComplete, setAnimationComplete] = useState(false); // State for animation completion
  const circumference = calculateCircumference(radius);
  const ref_input1 = useRef();
  const ref_input2 = useRef();
  const ref_input3 = useRef();
  const ref_input4 = useRef();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const refs = [ref_input1, ref_input2, ref_input3, ref_input4];
  const [fields, setFields] = useState(["", "", "", ""]);
  const [show, setShow] = useState(false);
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [postAnimationLoading, setPostAnimationLoading] = useState(false);
  const { handleToast } = useToast();
  const [userId, setUserId] = useState("");
  const { storedPrivateKey, setStoredPrivateKey } =
    useContext(FingerPrintStatus);
  // @ts-ignore
  const { language, changeLanguage } = useContext(LanguageContext);
  const focusNextField = (nextField: any) => {
    nextField.current.focus();
  };

  const handleKeyPress = (index: any, event: any) => {
    const { key, nativeEvent } = event;
    if (nativeEvent.key === "Backspace" || nativeEvent.key === "Delete") {
      if (fields[index] === "") {
        const prevIndex = index - 1;
        if (prevIndex >= 0) {
          setFields((prevFields) => {
            const updatedFields = [...prevFields];
            updatedFields[prevIndex] = "";
            return updatedFields;
          });
          focusNextField(refs[prevIndex]);
        }
      } else {
        setFields((prevFields) => {
          const updatedFields = [...prevFields];
          updatedFields[index] = "";
          return updatedFields;
        });
      }
    }
  };
  const handleChangeText = (index: any, text: any) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;
      if (text !== "") {
        const nextIndex = index + 1;
        if (nextIndex < updatedFields.length) {
          focusNextField(refs[nextIndex]);
        } else {
          if (index === updatedFields.length - 1) {
            Keyboard.dismiss();
          }
        }
      }
      return updatedFields;
    });
  };
  const handlePaste = (index: any, pastedText: string) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      const characters = pastedText.split("");
      for (let i = 0; i < characters.length; i++) {
        const fieldIndex = index + i;
        if (fieldIndex < updatedFields.length) {
          updatedFields[fieldIndex] = characters[i];
        }
      }

      return updatedFields;
    });
  };
  const handleFocus = (index: number) => {
    setActiveIndex(index);
  };

  const handleBlur = () => {
    setActiveIndex(null);
  };

  const ValidatePIN = async (pin) => {
    try {
      const body = {
        pin: await encryptPIN(pin),
        activityType: "register-biometrics",
      };
      const validate = await ValidatePin(body);
      if (validate.error) {
        handleToast(validate.message, "error");
        Alert.alert(validate.message);
      } else {
        showSensor();
        setShow(false);
        const privateKey = await encryptPIN(pin);
        await SecureStore.setItemAsync(`privateKey${userId}`, privateKey);
        // @ts-ignore
        if (setStoredPrivateKey) setStoredPrivateKey(privateKey);
      }
    } catch (error) {
      console.error("Error in ValidatePIN:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    setIsSubmitted(true);
    if (fields.every((field) => field !== "")) {
      setLoading(true);
      ValidatePIN(String(fields.join("")));
    } else {
    }
  };

  const getInputBorderStyle = (index) => {
    // Red border if field is empty and the form has been submitted
    return {
      borderColor: fields[index] === "" && isSubmitted ? colors.red : "#E6E5E5",
    };
  };

  // Animation function
  const animateProgress = (toValue) => {
    Animated.timing(animatedValue, {
      toValue: toValue,
      duration: 2000, // Duration of the animation in ms
      useNativeDriver: false, // Set to false for non-UI props (like strokeDashoffset)
    }).start(() => {
      if (toValue !== 0) {
        setAnimationComplete(true);
        setPostAnimationLoading(true);

        // Show success message
        handleToast(
          "Biometric authentication enabled successfully!",
          "success"
        );

        // Wait a moment then execute secondary function
        setTimeout(() => {
          if (secondaryFunction) {
            secondaryFunction();
          }

          // Close modal after secondary function completes
          setTimeout(() => {
            setPostAnimationLoading(false);
            onClose();
          }, 1000);
        }, 500);
      }
    });
  };

  // Animate progress when component mounts or percentage changes
  useEffect(() => {
    if (percentage === 0) {
      setAnimationComplete(false); // Reset completion status if percentage is 0
    } else {
      setAnimationComplete(false); // Reset before starting a new animation
      animateProgress(percentage);
    }
  }, [percentage]);

  // Convert animated value to strokeDashoffset
  const strokeDashoffset = animatedValue.interpolate({
    inputRange: [0, 100],
    outputRange: [circumference, 0],
  });

  const showSensor = async () => {
    try {
      const isCompatible = await LocalAuthentication.hasHardwareAsync();
      if (!isCompatible) {
        throw new Error("Your device isn't compatible.");
      }
      // Check if the user has biometrics enrolled
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      if (!isEnrolled) {
        throw new Error("No Faces / Fingers found.");
      }
      // Perform the authentication
      const biometricAuthResult = await LocalAuthentication.authenticateAsync();
      if (!biometricAuthResult.success) {
      } else {
        setPercentage(100);
      }
    } catch (error) {}
  };
  const getUserId = async () => {
    try {
      const res = await GetUserDetails();
      if (res.id) {
        setUserId(res.id);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getUserId();
  }, []);

  return (
    <Modal visible={visible} statusBarTranslucent={true} style={styles.body}>
      <View style={styles.innerBody}>
        <Div>
          <View style={styles.nav}>
            <TouchableOpacity onPress={onClose}>
              <SvgXml xml={svg.xClose} style={{ marginRight: 8 }} />
            </TouchableOpacity>
            <P>{Platform.OS === "ios" ? "Face ID" : "Fingerprint"}</P>
          </View>
          <View style={styles.textWrap}>
            <P style={styles.text1}>
              Start scanning with your{" "}
              {Platform.OS === "ios" ? "Face ID" : "Fingerprint"}
            </P>
            <P style={styles.text2}>
              Tap the button to enable{" "}
              {Platform.OS === "ios" ? "Face ID" : "Fingerprint"}
            </P>
          </View>
          <View style={styles.progContainer}>
            <Svg
              height={radius * 2 + strokeWidth}
              width={radius * 2 + strokeWidth}
            >
              {/* Background Circle */}
              <Circle
                cx={radius + strokeWidth / 2}
                cy={radius + strokeWidth / 2}
                r={radius}
                stroke="#D0B8FF"
                strokeWidth={strokeWidth}
                fill="none"
              />
              {/* Progress Circle */}
              <AnimatedCircle
                cx={radius + strokeWidth / 2} // Adjust position to center
                cy={radius + strokeWidth / 2} // Adjust position to center
                r={radius}
                stroke="#8B52FF"
                strokeWidth={strokeWidth}
                strokeLinecap="round"
                fill="none"
                strokeDasharray={circumference}
                strokeDashoffset={strokeDashoffset}
                transform={`rotate(-90, ${radius + strokeWidth / 2}, ${
                  radius + strokeWidth / 2
                })`} // Rotate to start at 12 o'clock
              />
            </Svg>
            {/* Fingerprint Icon */}
            <View style={styles.fingerprintIcon}>
              {Platform.OS === "ios" ? (
                <>
                  {animationComplete ? (
                    <SvgXml xml={svg.lfaceActive} />
                  ) : (
                    <SvgXml xml={svg.lface} />
                  )}
                </>
              ) : (
                <>
                  {animationComplete ? (
                    <SvgXml xml={svg.lFingerPrintActive} />
                  ) : (
                    <SvgXml xml={svg.lFingerPrint} />
                  )}
                </>
              )}
            </View>
          </View>
          <View style={styles.btnCont}>
            {postAnimationLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <P style={styles.loadingText}>Processing...</P>
              </View>
            ) : (
              <Button
                btnText="Get Started"
                onPress={() => {
                  setShow(true);
                }}
                disabled={percentage === 100}
              />
            )}
          </View>
        </Div>
        {show && (
          <View style={styles.pinCont}>
            <Div>
              <View style={styles.nav}>
                <TouchableOpacity onPress={() => setShow(false)}>
                  <SvgXml xml={svg.goBackIcon} style={{ marginRight: 8 }} />
                </TouchableOpacity>
                <P>Enter Pin</P>
              </View>
              <View style={styles.textWrap}>
                {/* @ts-ignore */}
                <P style={[styles.text1, { fontSize: 14 }]}>
                  Enter your pin to enable to{" "}
                  {Platform.OS === "ios" ? "Face ID" : "Fingerprint"}
                </P>
              </View>
              <View style={styles.section3Wrap}>
                <P style={{ fontSize: 12, marginBottom: 16 }}>{i18n.t("ep")}</P>
                <View style={styles.con}>
                  {refs.map((ref, index) => (
                    <View
                      style={[
                        styles.pinInput,
                        {
                          borderColor:
                            activeIndex === index
                              ? colors.primary
                              : getInputBorderStyle(index).borderColor,
                        },
                      ]}
                      key={index}
                    >
                      <TextInput
                        style={styles.pinTextInput}
                        placeholderTextColor="#000"
                        keyboardType="numeric"
                        ref={ref}
                        maxLength={1}
                        onChangeText={(text) => {
                          // Force single character by taking only first character
                          const singleChar = text.slice(0, 1);
                          handleChangeText(index, singleChar);
                        }}
                        onKeyPress={(event) => handleKeyPress(index, event)}
                        // @ts-ignore
                        onTextInput={(event) => {
                          // Handle paste/autofill by taking first character
                          const pastedText = event.nativeEvent.text.slice(0, 1);
                          handlePaste(index, pastedText);
                        }}
                        value={fields[index]}
                        secureTextEntry={show}
                        onFocus={() => handleFocus(index)}
                        onBlur={handleBlur}
                        // Add these additional props for extra protection
                        autoComplete="off"
                        autoCorrect={false}
                        autoCapitalize="none"
                      />
                    </View>
                  ))}
                </View>

                {/* <Link
                  style={{
                    textDecorationLine: "underline",
                    marginBottom: (24 / baseHeight) * height,
                    fontSize: 12,
                  }}
                  onPress={() => {
                    navigation.navigate("ExistingFp", { email: email });
                  }}
                >
                  {i18n.t("fp2")}?
                </Link> */}
              </View>
              <View style={[styles.btnCont, { width: "80%" }]}>
                {animationComplete ? (
                  <>
                    <View
                      style={{
                        width: "100%",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <ActivityIndicator
                        size={"large"}
                        color={colors.primary}
                      />
                    </View>
                  </>
                ) : (
                  <Button
                    btnText={i18n.t("continue")}
                    onPress={handleSubmit}
                    loading={loading}
                  />
                )}
              </View>
            </Div>
          </View>
        )}
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  body: {
    backgroundColor: colors.secBackground,
    flex: 1,
    position: "absolute",
  },
  innerBody: {
    backgroundColor: colors.secBackground,
    width,
    height: (105 * height) / 100,
  },
  nav: {
    width: "90%",
    alignSelf: "center",
    height: 53,
    flexDirection: "row",
    alignItems: "center",
  },
  progContainer: {
    width: radius * 2 + strokeWidth, // Adjust container size to accommodate stroke width
    height: radius * 2 + strokeWidth, // Adjust container size to accommodate stroke width
    justifyContent: "center",
    alignItems: "center",
    marginTop: 48, // Add margin for better alignment
    backgroundColor: "#fff",
    alignSelf: "center",
    borderRadius: 10000,
  },
  fingerprintIcon: {
    position: "absolute",
    justifyContent: "center",
    alignItems: "center",
  },
  textWrap: {
    width: "90%",
    marginTop: 24,
    alignSelf: "center",
    alignItems: "center",
  },
  text1: {
    fontSize: 16,
    fontFamily: fonts.poppinsMedium,
  },
  text2: {
    marginTop: 4,
    fontSize: 12,
    color: colors.gray,
  },
  btnCont: {
    marginTop: (8 * height) / 100,
    width: "60%",
    alignSelf: "center",
  },
  pinCont: {
    width,
    height: (105 * height) / 100,
    backgroundColor: colors.secBackground,
    position: "absolute",
  },
  section3Wrap: {
    paddingTop: (24 / baseHeight) * height,
    paddingBottom: (24 / baseHeight) * height,
    width: "90%",
    marginTop: (24 / baseHeight) * height,
    borderColor: colors.stroke,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    borderRadius: 12,
  },
  con: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: (264 / baseWidth) * width,
    alignSelf: "center",
    // marginBottom: 32,
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    marginHorizontal: (5 / baseWidth) * width,
    width: (48 / baseWidth) * width,
    height: (48 / baseWidth) * width,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: (18 / baseHeight) * height,
    marginBottom: (16 / baseHeight) * height,
  },
  pinTextInput: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
    width: (48 / baseWidth) * width,
    height: (48 / baseWidth) * width,
  },
  btnCont2: {
    marginTop: (20 * height) / 100,
    width: "80%",
    alignSelf: "center",
    position: "absolute",
    bottom: 40,
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 16,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
});
