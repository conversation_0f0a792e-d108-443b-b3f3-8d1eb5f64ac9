import P from "./P";
import { Image, StyleSheet, TouchableOpacity, View } from "react-native";
import { colorTone } from "react-native-color-matrix-image-filters";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";

interface PProps {
  Imageurl?: string;
  isKycDone?: string;
  message?: string;
  navigation?: any;
}

// Helper function to ensure HTTPS URLs
const ensureHttps = (url: string) => {
  if (!url) return url;
  if (url.startsWith("http://")) {
    return url.replace("http://", "https://");
  }
  return url;
};

export default function NonVerifiedAppHeader({
  Imageurl,
  isKycDone,
  message,
  navigation,
}: PProps) {
  return (
    <View style={styles.container}>
      <View style={[styles.mainCont]}>
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            gap: 8,
            width: "80%",
          }}
        >
          <Image
            source={
              !Imageurl ||
              Imageurl === "" ||
              Imageurl === null ||
              Imageurl === undefined
                ? require("../assets/defualtAvatar.png")
                : { uri: ensureHttps(Imageurl) }
            }
            style={{
              width: 38,
              height: 38,
              borderRadius: 100,
              objectFit: "cover",
            }}
          />
          <View style={{ width: "80%" }}>
            <P style={{ fontSize: 12 }}>
              {isKycDone === "pending"
                ? "Verification in progress"
                : isKycDone === "failed"
                ? "Verification failed"
                : "Verify your identity"}
            </P>
            <P
              style={{
                fontSize: 12,
                fontFamily: fonts.poppinsRegular,
                lineHeight: 15,
              }}
            >
              {isKycDone === "failed"
                ? message
                : "Easiest way to receive money abroad"}
            </P>
          </View>
        </View>
        <TouchableOpacity
          style={{
            paddingHorizontal: 12,
            paddingVertical: 6.5,
            backgroundColor: colors.primary,
            borderRadius: 100,
            maxWidth: "20%",
          }}
          onPress={() => {
            if (isKycDone === "pending") {
              navigation.navigate("AccountVerificationPending");
            } else if (isKycDone === "false") {
              navigation.navigate("AccountVerificationPromt");
            } else if (isKycDone === "failed") {
              navigation.navigate("AccountVerification4");
            } else {
            }
          }}
        >
          <P style={styles.text}>
            {isKycDone === "failed" ? "Retry" : "Verify"}
          </P>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    alignItems: "center",
    marginTop: 12,
    // marginBottom: 12,
  },
  mainCont: {
    width: "90%",
    display: "flex",
    justifyContent: "space-between",
    flexDirection: "row",
    backgroundColor: colors.white,
    minHeight: 54,
    borderRadius: 12,
    padding: 12,
    alignItems: "center",
  },
  text: {
    fontSize: 12,
    // lineHeight: 15,
    fontFamily: fonts.poppinsMedium,
    color: colors.white,
  },
});
