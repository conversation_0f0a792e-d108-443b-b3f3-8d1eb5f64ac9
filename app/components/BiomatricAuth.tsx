// biometricAuth.ts
import * as LocalAuthentication from "expo-local-authentication";
import { GetUserDetails } from "../RequestHandlers/User";
import { useToast } from "../context/ToastContext";

// Utility function to handle biometric authentication
export const biometricAuthentication = async (navigation: any) => {
  const {handleToast} = useToast()
  try {
    const userDetails = await GetUserDetails();
    if (!userDetails) {
      handleToast("Biometric not enabled", "error");
      return;
    }
    // Check if device is compatible with biometrics
    const isCompatible = await LocalAuthentication.hasHardwareAsync();
    if (!isCompatible) {
      throw new Error("Your device isn't compatible.");
    }

    // Check if the user has biometrics enrolled
    const isEnrolled = await LocalAuthentication.isEnrolledAsync();
    if (!isEnrolled) {
      throw new Error("No Faces / Fingers found.");
    }

    // Perform the authentication
    const biometricAuthResult = await LocalAuthentication.authenticateAsync();
    if (!biometricAuthResult.success) {
      handleToast("Biometric Failed, use Password", "error");
    } else {
      // If successful, navigate to the desired screen
      navigation.reset({
        index: 0,
        routes: [{ name: "Home" }],
      });
    }
  } catch (error) {

  }
};
