import React, { useState, useEffect } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  TextInput,
} from "react-native";
import P from "./P";
import { svg } from "../config/Svg";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import ListItemSelect from "./ListItemSelect";
import { SvgXml } from "react-native-svg";
import { countries } from "./counties";

interface PProps {
  onPress?: any;
  onActiveCountryChange?: (item: any) => void;
  offHeader?: Boolean;
  excludedCountries?: any;
}

const { width, height } = Dimensions.get("window");

export default function CountrySelect2({
  onPress,
  onActiveCountryChange,
  offHeader = false,
  excludedCountries,
}: PProps) {
  const [activeType, setActiveType] = useState<number | null>(null);
  const [activeCountry, setActiveCountry] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Array of countries to be excluded
  const filteredCountries = searchQuery
    ? countries
        .filter((country) =>
          country.country.toLowerCase().includes(searchQuery.toLowerCase())
        )
        .filter(
          (country) => !excludedCountries?.includes(country?.country) // Exclude countries
        )
    : countries.filter(
        (country) => !excludedCountries?.includes(country?.country) // Exclude countries
      );

  useEffect(() => {
    if (onActiveCountryChange && activeCountry) {
      onActiveCountryChange(activeCountry); // Passing the entire country object
    }
  }, [activeCountry]);

  return (
    <View style={styles.viewContent}>
      <View style={styles.search}>
        <SvgXml xml={svg.search} style={{ marginRight: 8 }} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search country"
          placeholderTextColor={colors.dGray}
          cursorColor={colors.black}
          value={searchQuery}
          onChangeText={(text) => setSearchQuery(text)}
        />
      </View>
      {!offHeader && (
        <P
          style={{
            fontSize: 12,
            color: colors.gray,
            font: fonts.poppinsRegular,
            marginBottom: 8,
          }}
        >
          Select the country you want to add money from
        </P>
      )}

      <ScrollView showsVerticalScrollIndicator={false}>
        {filteredCountries.map((item, index) => (
          <ListItemSelect
            text1={item.country}
            image={item.flag}
            key={index}
            isActive={activeType === index}
            onPress={() => {
              setActiveType(index);
              setActiveCountry(item); // Set the entire country object
              if (onPress) onPress(item); // Pass the entire object to onPress if provided
            }}
            containerStyle={{
              marginBottom: index === filteredCountries.length - 1 ? 400 : 16,
            }}
          />
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  viewContent: {
    width: "100%",
    paddingTop: 24,
  },
  search: {
    width: "100%",
    height: (5.5 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    marginBottom: 24,
    borderWidth: 1,
    borderRadius: 99,
    borderColor: colors.stroke,
    padding: 12,
    paddingLeft: 14,
    paddingRight: 14,
    flexDirection: "row",
  },
  searchInput: {
    width: "90%",
    height: 24,
    fontFamily: fonts.poppinsRegular,
    lineHeight: 24,
    fontSize: 14,
  },
});
