import { Text, TextStyle, StyleProp } from "react-native";
import React from "react";
import { fonts } from "../config/Fonts";
import { colors } from "../config/colors";

interface PProps {
  children: React.ReactNode;
  style?: StyleProp<TextStyle>;
  numberOfLines?: number;
  onPress?: () => void;
}

export default function P({ children, style, numberOfLines, onPress }: PProps) {
  return (
    <Text
      onPress={onPress}
      numberOfLines={numberOfLines}
      style={[
        {
          fontFamily: fonts.poppinsMedium,
          fontSize: 14,
          color: colors.black,
          lineHeight: 18,
          letterSpacing: 0,
        },
        style,
      ]}
    >
      {children}
    </Text>
  );
}
