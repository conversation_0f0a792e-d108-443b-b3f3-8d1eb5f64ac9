import React, { useState, useEffect, useRef } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Text,
  ScrollView,
  Dimensions,
  NativeSyntheticEvent,
  NativeScrollEvent
} from "react-native";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import P from "./P";

const { height } = Dimensions.get('window');

interface DatePickerProps {
  isVisible?: boolean;
  onClose?: () => void;
  onDateSelected: (date: { month: string; day: number; year: number }) => void;
  initialDate?: { month: string; day: number; year: number };
  useModal?: boolean;
}

const CustomDatePicker: React.FC<DatePickerProps> = ({
  isVisible = true,
  onClose = () => {},
  onDateSelected,
  initialDate,
  useModal = true,
}) => {
  // Create refs for the ScrollViews
  const monthScrollViewRef = useRef<ScrollView>(null);
  const dayScrollViewRef = useRef<ScrollView>(null);
  const yearScrollViewRef = useRef<ScrollView>(null);

  // Item height
  const ITEM_HEIGHT = 50;

  // Generate years from 1900 to current year + 1
  const currentYear = new Date().getFullYear();
  const startYear = 1900;
  const endYear = currentYear + 1; // Include next year
  const years = Array.from(
    { length: endYear - startYear + 1 },
    (_, i) => startYear + i
  ); // Years in ascending order from 1900 to current year + 1

  // Months array
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // Days array (1-31)
  const days = Array.from({ length: 31 }, (_, i) => i + 1);

  // State for selected date
  const [selectedMonth, setSelectedMonth] = useState<string>(initialDate?.month || months[4]); // Default to May
  const [selectedDay, setSelectedDay] = useState<number>(initialDate?.day || 3);
  const [selectedYear, setSelectedYear] = useState<number>(initialDate?.year || currentYear);

  // Update state when initialDate changes
  useEffect(() => {
    if (initialDate) {
      setSelectedMonth(initialDate.month);
      setSelectedDay(initialDate.day);
      setSelectedYear(initialDate.year);
    }
  }, [initialDate]);

  // Function to handle scroll on month ScrollView
  const handleMonthScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    const visibleHeight = 350 - 54; // pickerContainer height - selectionHighlight height
    const centerY = offsetY + visibleHeight / 2;
    const index = Math.round(centerY / ITEM_HEIGHT);

    if (index >= 0 && index < months.length) {
      setSelectedMonth(months[index]);
    }
  };

  // Function to handle scroll on day ScrollView
  const handleDayScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    const visibleHeight = 350 - 54; // pickerContainer height - selectionHighlight height
    const centerY = offsetY + visibleHeight / 2;
    const index = Math.round(centerY / ITEM_HEIGHT);

    if (index >= 0 && index < days.length) {
      setSelectedDay(days[index]);
    }
  };

  // Function to handle scroll on year ScrollView
  const handleYearScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    const visibleHeight = 350 - 54; // pickerContainer height - selectionHighlight height
    const centerY = offsetY + visibleHeight / 2;
    const index = Math.round(centerY / ITEM_HEIGHT);

    if (index >= 0 && index < years.length) {
      setSelectedYear(years[index]);
    }
  };

  // Initialize ScrollViews to the selected values
  useEffect(() => {
    setTimeout(() => {
      const visibleHeight = 350 - 54; // pickerContainer height - selectionHighlight height

      if (monthScrollViewRef.current) {
        const index = months.indexOf(selectedMonth);
        if (index !== -1) {
          const targetY = index * ITEM_HEIGHT - (visibleHeight / 2 - ITEM_HEIGHT / 2);
          monthScrollViewRef.current.scrollTo({ y: targetY, animated: false });
        }
      }

      if (dayScrollViewRef.current) {
        const index = days.indexOf(selectedDay);
        if (index !== -1) {
          const targetY = index * ITEM_HEIGHT - (visibleHeight / 2 - ITEM_HEIGHT / 2);
          dayScrollViewRef.current.scrollTo({ y: targetY, animated: false });
        }
      }

      if (yearScrollViewRef.current) {
        const index = years.indexOf(selectedYear);
        if (index !== -1) {
          const targetY = index * ITEM_HEIGHT - (visibleHeight / 2 - ITEM_HEIGHT / 2);
          yearScrollViewRef.current.scrollTo({ y: targetY, animated: false });
        }
      }
    }, 300); // Increased delay to ensure component is fully rendered
  }, []);

  const handleApply = () => {
    onDateSelected({
      month: selectedMonth,
      day: selectedDay,
      year: selectedYear
    });
    onClose();
  };

  // The actual date picker content
  const renderDatePickerContent = () => (
    <View style={styles.modalContainer}>
      <View style={styles.header}>
        <P style={styles.headerTitle}>Date</P>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Text style={styles.closeButtonText}>✕</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.divider} />

      <View style={styles.columnHeaders}>
        <View style={styles.columnHeader}>
          <P style={styles.columnHeaderText}>Month</P>
        </View>
        <View style={styles.columnHeader}>
          <P style={styles.columnHeaderText}>Day</P>
        </View>
        <View style={styles.columnHeader}>
          <P style={styles.columnHeaderText}>Year</P>
        </View>
      </View>

      <View style={styles.pickerContainer}>
        {/* Selection highlight in the center */}
        <View style={styles.selectionHighlight} />

        {/* Month ScrollView */}
        <ScrollView
          ref={monthScrollViewRef}
          style={styles.column}
          showsVerticalScrollIndicator={false}
          decelerationRate={0.95}
          onMomentumScrollEnd={handleMonthScroll}
          contentContainerStyle={styles.scrollContentContainer}
        >
          {months.map((month) => (
            <TouchableOpacity
              key={month}
              style={styles.itemContainer}
              onPress={() => {
                setSelectedMonth(month);
                setTimeout(() => {
                  if (monthScrollViewRef.current) {
                    const index = months.indexOf(month);
                    const visibleHeight = 350 - 54; // pickerContainer height - selectionHighlight height
                    const targetY = index * ITEM_HEIGHT - (visibleHeight / 2 - ITEM_HEIGHT / 2);
                    monthScrollViewRef.current.scrollTo({ y: targetY, animated: true });
                  }
                }, 50);
              }}
            >
              <P style={[
                styles.itemText,
                selectedMonth === month && styles.selectedItemText
              ]}>
                {month}
              </P>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Day ScrollView */}
        <ScrollView
          ref={dayScrollViewRef}
          style={styles.column}
          showsVerticalScrollIndicator={false}
          decelerationRate={0.95}
          onMomentumScrollEnd={handleDayScroll}
          contentContainerStyle={styles.scrollContentContainer}
        >
          {days.map((day) => (
            <TouchableOpacity
              key={day}
              style={styles.itemContainer}
              onPress={() => {
                setSelectedDay(day);
                setTimeout(() => {
                  if (dayScrollViewRef.current) {
                    const index = days.indexOf(day);
                    const visibleHeight = 100 - 54; // pickerContainer height - selectionHighlight height
                    const targetY = index * ITEM_HEIGHT - (visibleHeight / 2 - ITEM_HEIGHT / 2);
                    console.log(targetY);
                    dayScrollViewRef.current.scrollTo({ y: targetY, animated: true });
                  }
                }, 50);
              }}
            >
              <P style={[
                styles.itemText,
                selectedDay === day && styles.selectedItemText
              ]}>
                {day}
              </P>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Year ScrollView */}
        <ScrollView
          ref={yearScrollViewRef}
          style={styles.column}
          showsVerticalScrollIndicator={false}
          decelerationRate={0.95}
          onMomentumScrollEnd={handleYearScroll}
          contentContainerStyle={styles.scrollContentContainer}
        >
          {years.map((year) => (
            <TouchableOpacity
              key={year}
              style={styles.itemContainer}
              onPress={() => {
                setSelectedYear(year);
                setTimeout(() => {
                  if (yearScrollViewRef.current) {
                    const index = years.indexOf(year);
                    const visibleHeight = 350 - 54; // pickerContainer height - selectionHighlight height
                    const targetY = index * ITEM_HEIGHT - (visibleHeight / 2 - ITEM_HEIGHT / 2);
                    yearScrollViewRef.current.scrollTo({ y: targetY, animated: true });
                  }
                }, 50);
              }}
            >
              <P style={[
                styles.itemText,
                selectedYear === year && styles.selectedItemText
              ]}>
                {year}
              </P>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={onClose}
        >
          <P style={styles.cancelButtonText}>Cancel</P>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.applyButton}
          onPress={handleApply}
        >
          <P style={styles.applyButtonText}>Apply</P>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Conditionally render with or without modal
  return useModal ? (
    <Modal
      visible={isVisible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        {renderDatePickerContent()}
      </View>
    </Modal>
  ) : (
    renderDatePickerContent()
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    paddingTop: 5 * height / 100,
    paddingLeft: 0, paddingRight: 0,
    width: '100%',
    maxHeight: height * 0.7,
  },
  scrollContentContainer: {
    paddingTop: 200,
    paddingBottom: 200,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    position: 'relative',
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: fonts.poppinsMedium,
  },
  closeButton: {
    position: 'absolute',
    right: 20,
    top: 16,
  },
  closeButtonText: {
    fontSize: 18,
    color: colors.gray,
  },
  divider: {
    height: 4,
    width: 40,
    backgroundColor: '#E6E6E6',
    alignSelf: 'center',
    marginBottom: 16,
    borderRadius: 2,
  },
  columnHeaders: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 8,
    backgroundColor: '#F5F2FF',
    borderRadius: 20,
    marginHorizontal: 20,
    paddingVertical: 10,
  },
  columnHeader: {
    flex: 1,
    alignItems: 'center',
  },
  columnHeaderText: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.poppinsMedium,
  },
  pickerContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    height: 350,
    marginTop: 20,
    position: 'relative'
  },
  selectionHighlight: {
    position: 'absolute',
    left: 20,
    right: 20,
    height: 54,
    top: '50%',
    marginTop: -27,
    backgroundColor: '#F5F2FF',
    borderRadius: 20,
    zIndex: -1,
    borderWidth: 1,
    borderColor: '#E6E6E6',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  column: {
    flex: 1,
    marginHorizontal: 5,
  },
  itemContainer: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 2,
  },
  selectedItemContainer: {
    backgroundColor: '#8B52FF',
    borderRadius: 20,
  },
  itemText: {
    fontSize: 14,
    color: colors.gray,
  },
  selectedItemText: {
    color: colors.primary,
    fontFamily: fonts.poppinsMedium,
    fontSize: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 25,
    borderWidth: 1,
    borderColor: '#E6E6E6',
    marginRight: 10,
  },
  cancelButtonText: {
    color: colors.primary,
  },
  applyButton: {
    flex: 1,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 25,
    backgroundColor: colors.primary,
    marginLeft: 10,
  },
  applyButtonText: {
    color: 'white',
  },
});

export default CustomDatePicker;
