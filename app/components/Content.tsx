import {
  StyleSheet,
  Text,
  View,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import React from "react";
import { CSSProperties } from "react";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "./P";
const { width, height } = Dimensions.get("window");

interface PProps {
  header: string;
  style?: CSSProperties;
  body?: string;
  svg1?: any;
  onPress?: any;
  bottomBorder?: boolean;
  sWidth?: number;
  sHeight?: number;
  arrowRight?: Boolean;
  rightComponent?: any;
  isCardFreezed?: true | false;
  disabled?: true | false;
}

export default function Content({
  header,
  body,
  svg1,
  onPress,
  bottomBorder = true,
  sWidth = 24,
  sHeight = 24,
  arrowRight = true,
  rightComponent,
  isCardFreezed,
  disabled,
}: PProps) {
  return (
    <TouchableOpacity
      style={[
        styles.content,
        {
          borderBottomWidth: bottomBorder ? 1 : 0,
          opacity: isCardFreezed ? 0.5 : 1,
        },
      ]}
      disabled={isCardFreezed || disabled}
      onPress={onPress}
    >
      <SvgXml xml={svg1} width={sWidth} height={sHeight} />
      <View style={{ width: "80%" }}>
        {/* @ts-ignore */}
        <P style={styles.contentHeader}>{header}</P>
        {/* @ts-ignore */}
        {body && <P style={styles.contentText}>{body}</P>}
      </View>
      {arrowRight && <SvgXml xml={svg.arrowBlack} />}
      {rightComponent}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  content: {
    width: "90%",
    minHeight: 72,
    borderBottomWidth: 1,
    borderColor: "#E6E5E5",
    paddingBottom: 16,
    paddingTop: 16,
    // backgroundColor:"red",
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
  },
  contentHeader: { fontSize: 12, fontFamily: fonts.poppinsMedium },
  contentText: {
    fontSize: 12,
    color: "#A5A1A1",
    fontFamily: fonts.poppinsRegular,
  },
});
