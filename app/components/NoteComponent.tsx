import React from "react";
import { StyleSheet, View } from "react-native";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "./P";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";

interface PProps {
  text?: string;
}
export default function NoteComponent({ text }: PProps) {
  return (
    <View style={styles.info}>
      <SvgXml xml={svg.alertCircle} style={{ marginRight: 8,}} />
      <P style={{ fontSize: 10, lineHeight: 16, fontFamily: fonts.poppinsRegular, color: colors.black }}>{text}</P>
    </View>
  );
}

const styles = StyleSheet.create({
  info: {
    width: "100%",
    backgroundColor: colors.lowOpPrimary2,
    padding: 16,
    paddingRight: 32,
    paddingTop: 8,
    paddingBottom: 8,
    borderRadius: 8,
    flexDirection: "row",
    // alignItems: "center",
  },
});
