import React, { useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  Image,
  ImageBackground,
  Dimensions,
} from "react-native";
import { colors } from "../config/colors";
import P from "./P";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import { fonts } from "../config/Fonts";
import { LinearGradient } from "expo-linear-gradient";
import { GetUserDetails } from "../RequestHandlers/User";

const { width } = Dimensions.get("window");
interface PProps {
  accStatus?: string;
  navigation?: any;
  text?: string;
}
export default function CardIssue({ accStatus, navigation, text }: PProps) {
  const getUserDetails = async () => {
    try {
      const details = await GetUserDetails();
    } catch (error) {}
  };

  useEffect(() => {
    getUserDetails();
  }, []);
  return (
    <ImageBackground
      source={require("../assets/prFrame/pr2.png")}
      resizeMode="cover"
      style={styles.rCard}
    >
      {/* <View style={styles.rCard}> */}
      {accStatus === "verified" ? (
        <Image
          source={require("../assets/success.png")}
          style={{ width: 48, height: 48, marginRight: 8 }}
        />
      ) : (
        <SvgXml xml={svg.redCard} />
      )}

      <View style={{ width: "75%" }}>
        <P style={styles.text2}>{text}</P>
        {/* </View> */}
      </View>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  rCard: {
    width: width * 0.9,
    // padding: 16,
    paddingLeft: 8,
    borderRadius: 12,
    overflow: "hidden",
    flexDirection: "row",
    alignItems: "center",
    // justifyContent: "space-between",
  },
  text1: {
    fontSize: 14,
    fontFamily: fonts.poppinsMedium,
  },
  text2: {
    fontSize: 12,
    fontFamily: fonts.poppinsSemibold,
    color: "#FDD3D4",
  },
});
