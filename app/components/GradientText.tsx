import React from "react";
import { Text, TextStyle, ViewStyle } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import MaskedView from "@react-native-masked-view/masked-view"; // Install: npx expo install @react-native-masked-view/masked-view
import { fonts } from "../config/Fonts";

interface GradientTextProps {
  children: React.ReactNode;
  colors?: readonly [string, string, ...string[]];
  start?: { x: number; y: number };
  end?: { x: number; y: number };
  style?: TextStyle;
  gradientStyle?: ViewStyle;
}

export default function GradientText({
  children,
  colors = ["#8C52FF", "#FF6B6B"] as const, // Default blue to purple gradient
  start = { x: 0.2, y: 0 },
  end = { x: 1, y: 0 },
  style,
  gradientStyle,
}: GradientTextProps) {
  return (
    <MaskedView
      style={{ flexDirection: "row" }}
      maskElement={
        <Text
          style={[
            {
              backgroundColor: "transparent",
              fontFamily: fonts.poppinsSemibold,
              lineHeight: 28.8,
              fontSize: 24,
              fontWeight: "600",
            },
            style,
          ]}
        >
          {children}
        </Text>
      }
    >
      <LinearGradient
        colors={colors}
        start={start}
        end={end}
        style={[{ flex: 1 }, gradientStyle]}
      >
        <Text
          style={[
            {
              fontFamily: fonts.poppinsSemibold,
              fontSize: 24,
              fontWeight: "600",
              opacity: 0,
              lineHeight: 28.8,
            },
            style,
          ]}
        >
          {children}
        </Text>
      </LinearGradient>
    </MaskedView>
  );
}
