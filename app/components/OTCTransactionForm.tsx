import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, TextInput, Alert, Keyboard } from 'react-native';
import { SvgXml } from 'react-native-svg';
import { svg } from '../config/Svg';
import P from './P';
import { colors } from '../config/colors';
import { fonts } from '../config/Fonts';
import Button from './Button';
import WhatsAppShare from './WhatsAppShare';
import { useToast } from '../context/ToastContext';
import { formatToTwoDecimals } from '../Utils/numberFormat';

interface OTCTransactionFormProps {
  isAddMoney?: boolean; // true for add money, false for withdraw
  otcPhoneNumber: string;
  onSuccess?: () => void;
  exchangeRate?: number; // Exchange rate between USD and local currency
  localCurrencyCode?: string; // e.g., "TRY" for Turkish Lira
  userID?: string; // User ID for the message
  minAmount?: number; // Minimum transaction amount in USD
}

const OTCTransactionForm: React.FC<OTCTransactionFormProps> = ({
  isAddMoney = true,
  otcPhoneNumber,
  onSuccess,
  exchangeRate = 1,
  localCurrencyCode = "TRY",
  userID = "",
  minAmount = 3
}) => {
  const [amount, setAmount] = useState('');
  const [isUsdInput, setIsUsdInput] = useState(true);
  const [showWhatsAppModal, setShowWhatsAppModal] = useState(false);
  const { handleToast } = useToast();

  // Calculate converted amount based on input and currency selection
  const getConvertedAmount = () => {
    const numericAmount = parseFloat(amount) || 0;
    if (isUsdInput) {
      return numericAmount * exchangeRate; // USD to local currency
    } else {
      return numericAmount / exchangeRate; // Local currency to USD
    }
  };

  // Toggle between USD and local currency input
  const toggleCurrency = () => {
    if (amount) {
      // Convert the current amount to the new currency
      const convertedAmount = formatToTwoDecimals(getConvertedAmount());
      setAmount(convertedAmount);
    }
    setIsUsdInput(!isUsdInput);
  };

  // Validate input and show WhatsApp modal
  const handleSubmit = () => {
    Keyboard.dismiss();

    const numericAmount = parseFloat(amount) || 0;
    const usdAmount = isUsdInput ? numericAmount : numericAmount / exchangeRate;

    if (numericAmount <= 0) {
      handleToast("Please enter a valid amount", "error");
      return;
    }

    if (usdAmount < minAmount) {
      handleToast(`Minimum amount is $${minAmount} USD`, "error");
      return;
    }

    setShowWhatsAppModal(true);
  };

  // Prepare WhatsApp message
  const getWhatsAppMessage = () => {
    const usdAmount = isUsdInput ? amount : formatToTwoDecimals(Number(amount) / Number(exchangeRate));
    const localAmount = isUsdInput ? formatToTwoDecimals(Number(amount) * exchangeRate) : amount;

    return `Hello SFx OTC,\n\nI would like to ${isAddMoney ? 'add' : 'withdraw'} money via OTC.\n\nAmount: $${formatToTwoDecimals(Number(usdAmount))} USD (${localCurrencyCode} ${formatToTwoDecimals(Number(localAmount))})\n${userID ? `User ID: ${userID}\n` : ''}\nThank you!`;
  };

  return (
    <View style={styles.container}>
      {/* Amount Input Section */}
      <View style={styles.inputContainer}>
        <View style={styles.currencyToggleContainer}>
          <P style={styles.currencyLabel}>
            {isUsdInput ? 'USD' : localCurrencyCode}
          </P>
          <TouchableOpacity onPress={toggleCurrency} style={styles.toggleButton}>
            <SvgXml xml={svg.upDown} width={20} height={20} />
          </TouchableOpacity>
        </View>

        <TextInput
          style={styles.amountInput}
          value={amount}
          onChangeText={setAmount}
          placeholder="0.00"
          keyboardType="numeric"
          placeholderTextColor={colors.gray}
        />
      </View>

      {/* Converted Amount Display */}
      {amount ? (
        <View style={styles.convertedContainer}>
          <P style={styles.convertedLabel}>
            {isUsdInput ? localCurrencyCode : 'USD'} Equivalent
          </P>
          <P style={styles.convertedAmount}>
            {isUsdInput ? localCurrencyCode : '$'}
          </P>
        </View>
      ) : null}

      {/* Submit Button */}
      <Button
        btnText={`Continue to ${isAddMoney ? 'Add' : 'Withdraw'} Money`}
        onPress={handleSubmit}
        style={styles.submitButton}
      />

      {/* WhatsApp Modal */}
      {showWhatsAppModal && (
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowWhatsAppModal(false)}
            >
              <SvgXml xml={svg.xClose} width={24} height={24} />
            </TouchableOpacity>

            <SvgXml xml={svg.whatsApp} width={60} height={60} style={styles.whatsAppIcon} />

            <P style={styles.modalTitle}>
              Proceed to SFx OTC desk on WhatsApp
            </P>

            <P style={styles.modalSubtitle}>
              To complete your OTC transaction, please continue on WhatsApp
            </P>

            <WhatsAppShare
              phoneNumber={otcPhoneNumber}
              message={getWhatsAppMessage()}
              showText={true}
              text="Start OTC on WhatsApp"
              containerStyle={styles.whatsAppButton}
              onShareComplete={() => {
                setShowWhatsAppModal(false);
                if (onSuccess) onSuccess();
              }}
            />
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    padding: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  currencyToggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
    borderRightWidth: 1,
    borderRightColor: colors.lightGray,
    paddingRight: 12,
  },
  currencyLabel: {
    fontFamily: fonts.poppinsMedium,
    fontSize: 16,
    marginRight: 8,
  },
  toggleButton: {
    padding: 4,
  },
  amountInput: {
    flex: 1,
    fontFamily: fonts.poppinsRegular,
    fontSize: 18,
    color: colors.black,
  },
  convertedContainer: {
    marginBottom: 24,
    padding: 12,
    // backgroundColor: colors.lightPurple,
    borderRadius: 8,
  },
  convertedLabel: {
    fontFamily: fonts.poppinsRegular,
    fontSize: 12,
    // color: colors.darkGray,
    marginBottom: 4,
  },
  convertedAmount: {
    fontFamily: fonts.poppinsMedium,
    fontSize: 16,
    color: colors.black,
  },
  submitButton: {
    marginTop: 16,
  },
  modalContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContent: {
    width: '90%',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    zIndex: 1,
  },
  whatsAppIcon: {
    marginBottom: 16,
  },
  modalTitle: {
    fontFamily: fonts.poppinsSemibold,
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 8,
  },
  modalSubtitle: {
    fontFamily: fonts.poppinsRegular,
    fontSize: 14,
    textAlign: 'center',
    // color: colors.darkGray,
    marginBottom: 24,
  },
  whatsAppButton: {
    backgroundColor: '#25D366', // WhatsApp green
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    width: '100%',
  },
});

export default OTCTransactionForm;
