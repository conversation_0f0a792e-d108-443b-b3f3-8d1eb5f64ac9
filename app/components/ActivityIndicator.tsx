import React from "react";
import AnimatedLoader from "./AnimatedLoader";

interface PProps {
  visible?: any;
  loading?: boolean;
  showRetry?: boolean;
  onRetry?: () => void;
  message?: string;
}

export default function Loader({
  visible = true,
  loading = true,
  showRetry = false,
  onRetry,
  message = "",
}: PProps) {
  return (
    <AnimatedLoader
      visible={visible}
      loading={loading}
      showRetry={showRetry}
      onRetry={onRetry}
      message={message}
    />
  );
}

// Styles moved to AnimatedLoader component
