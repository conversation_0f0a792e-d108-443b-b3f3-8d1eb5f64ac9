import React, { useEffect, useRef, useState } from "react";
import { CameraView, CameraType, useCameraPermissions } from "expo-camera";
import {
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Image,
  Dimensions,
  Animated,
  Linking,
  Platform,
} from "react-native";
import P from "./P";
import { colors } from "../config/colors";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import { fonts } from "../config/Fonts";
import Button from "./Button";


interface BarCodeScannerProps {
  visible: boolean;
  onClose: () => void;
  onScan?: (data: string) => void;
}

const baseHeight = 800;
const { width, height } = Dimensions.get("window");

export default function BarCodeScanner({ visible, onClose, onScan }: BarCodeScannerProps) {
  const [facing, setFacing] = useState<CameraType>("back");
  const [permission, requestPermission] = useCameraPermissions();
  const [torchOn, setTorchOn] = useState(false);
  const [hasScanned, setHasScanned] = useState(false);
  const [isCameraReady, setIsCameraReady] = useState(false);

  // Animated value for the moving bar
  const moveAnim = useRef(new Animated.Value(0)).current;
  const animationRef = useRef<Animated.CompositeAnimation | null>(null);
  const mountedRef = useRef(true);

  // Handle component mount/unmount
  useEffect(() => {
    mountedRef.current = true;

    return () => {
      mountedRef.current = false;

      // Ensure we clean up any pending operations
      if (animationRef.current) {
        animationRef.current.stop();
        animationRef.current = null;
      }
    };
  }, []);

  // Handle visibility changes
  useEffect(() => {
    if (visible) {
      // Reset states when modal becomes visible
      setHasScanned(false);

      // Use a small delay to ensure the camera is properly initialized
      const timer = setTimeout(() => {
        if (mountedRef.current) {
          setIsCameraReady(true);
        }
      }, 300);

      return () => {
        clearTimeout(timer);
        setIsCameraReady(false);
      };
    } else {
      setIsCameraReady(false);
    }
  }, [visible]);

  // Start the animation for the moving bar
  useEffect(() => {
    if (visible && isCameraReady) {
      // Start animation
      animationRef.current = Animated.loop(
        Animated.sequence([
          Animated.timing(moveAnim, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(moveAnim, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
          }),
        ])
      );

      animationRef.current.start();
    }

    // Cleanup animation when component unmounts or modal closes
    return () => {
      if (animationRef.current) {
        animationRef.current.stop();
        animationRef.current = null;
      }
    };
  }, [visible, isCameraReady, moveAnim]);

  // Request camera permission when modal becomes visible
  useEffect(() => {
    if (visible && permission && !permission.granted) {
      requestPermission().catch(error => {
        console.error("Error requesting camera permission:", error);
      });
    }
  }, [visible, permission, requestPermission]);

  const handleRequestPermission = async () => {
    try {
      const result = await requestPermission();
      if (result && !result.granted) {
        Linking.openSettings();
      }
    } catch (error) {
      console.error("Error requesting camera permission:", error);
    }
  };

  const translateY = moveAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 200],
  });

  return (
    <Modal
      style={styles.container}
      statusBarTranslucent={true}
      visible={visible}
      onRequestClose={onClose}
      animationType="slide"
    >
      {!permission?.granted ? (
        <View style={styles.reqContainer}>
          <SvgXml xml={svg.cam} style={{ marginBottom: 16 }} />
          <P>Camera permission</P>
          <Text style={styles.message}>
            Enable camera access to quickly scan{"\n"}and verify document
            securely
          </Text>
          <View style={{ width: "50%", marginTop: 32 }}>
            <Button btnText="Continue" onPress={handleRequestPermission} />
          </View>
        </View>
      ) : !isCameraReady ? (
        <View style={styles.reqContainer}>
          <P>Initializing camera...</P>
        </View>
      ) : (
        <CameraView
          style={styles.camera}
          facing={facing}
          barcodeScannerSettings={{
            barcodeTypes: ["qr"],
          }}
          onBarcodeScanned={hasScanned ? undefined : (e) => {
            try {
              if (e?.data) {
                setHasScanned(true);
                onScan(e.data);

                // Small delay before closing to prevent UI glitches
                setTimeout(() => {
                  if (mountedRef.current) {
                    onClose();
                  }
                }, 300);
              }
            } catch (error) {
              console.error("Error processing barcode:", error);
              if (mountedRef.current) {
                onClose();
              }
            }
          }}
          enableTorch={torchOn}
        >
          <View style={styles.overlayContainer}>
            {/* Scan Area */}
            <View style={styles.scanArea}>
              <Image
                source={require("../assets/lc.png")}
                style={[styles.conners, { top: -4, left: 0, zIndex: 100 }]}
              />
              <Image
                source={require("../assets/rightConner.png")}
                style={[styles.conners, { top: -4, right: -4 }]}
              />
              <Image
                source={require("../assets/blc.png")}
                style={[styles.conners, { bottom: -4, left: -4 }]}
              />
              <Image
                source={require("../assets/brc.png")}
                style={[styles.conners, { bottom: -4, right: -4 }]}
              />

              {/* Moving bar */}
              <Animated.View
                style={[
                  styles.movingBar,
                  {
                    transform: [{ translateY }],
                  },
                ]}
              />
            </View>

            {/* Header with Close Button */}
            <View style={styles.header}>
              <TouchableOpacity
                onPress={onClose}
                style={{ flexDirection: "row", alignItems: "center" }}
              >
                <SvgXml xml={svg.bGrayLight} style={{ marginRight: 8 }} />
                <P style={{ color: colors.white }}>Scan</P>
              </TouchableOpacity>
            </View>

            {/* Bottom instructions and torch button */}
            <View
              style={{
                position: "absolute",
                bottom: (100 / baseHeight) * height,
              }}
            >
              <P
                style={{
                  color: colors.white,
                  paddingTop: 32,
                  alignSelf: "center",
                }}
              >
                Scan QR code
              </P>
              <P
                style={{
                  color: colors.gray,
                  fontFamily: fonts.poppinsRegular,
                  alignSelf: "center",
                  marginTop: "20%",
                }}
              >
                Tap to turn on flashlight
              </P>

              <TouchableOpacity
                style={{ alignSelf: "center", marginTop: "5%" }}
                onPress={() => setTorchOn((prevState) => !prevState)}
              >
                <SvgXml xml={svg.bulb} />
              </TouchableOpacity>
            </View>
          </View>
        </CameraView>
      )}
    </Modal>
  );
}

const styles = StyleSheet.create({
  reqContainer: {
    width,
    height,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
  },
  container: {
    flex: 1,
    justifyContent: "center",
  },
  message: {
    textAlign: "center",
    fontSize: 12,
    marginTop: 4,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
  },
  camera: {
    flex: 1,
    alignItems: "center",
    backgroundColor: 'black', // Ensure black background
  },
  overlayContainer: {
    width,
    height: "100%",
    position: "absolute",
    alignItems: "center",
    zIndex: 100,
  },
  scanArea: {
    width: "70%",
    height: "30%",
    borderRadius: 8,
    top: "30.4%",
    position: "absolute",
    alignItems: "center",
  },
  movingBar: {
    width: "80%",
    height: 4,
    backgroundColor: colors.white,
    position: "absolute",
    alignSelf: "center",
    borderRadius: 10,
  },
  conners: {
    width: 23,
    height: 23,
    position: "absolute",
    zIndex: 1,
  },
  header: {
    width: "90%",
    top: Platform.OS === 'ios' ? 68 : 48,
    flexDirection: "row",
    alignItems: "center",
    position: "absolute",
    zIndex: 200,
  },
});
