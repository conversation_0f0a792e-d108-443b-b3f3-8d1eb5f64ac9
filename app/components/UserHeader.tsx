import React, { CSSProperties, useEffect, useState, useCallback } from "react";
import { View, StyleSheet, Image, TouchableOpacity, TextStyle } from "react-native";
import P from "./P";
import MicroBtn from "./MicroBtn";
import { svg } from "../config/Svg";
import { fonts } from "../config/Fonts";
import { GetUserDetails } from "../RequestHandlers/User";
import { useFocusEffect } from "@react-navigation/native";
import { useToast } from '../context/ToastContext';
import { withApiErrorToast } from '../Utils/withApiErrorToast';

interface UserHeaderProps {
  showStatus?: boolean;
  text1: string | React.ReactElement;
  showName?: string;
  text1Style?: TextStyle;
  style?: TextStyle;
  icon1?: string;
  icon2?: string;
  onPress?: () => void;
  brCodePressed?: () => void;
  bottomComponent?: React.ReactElement;
  imgPressed?: () => void;
}

export default function UserHeader({
  showStatus = true,
  text1 = "John Doe",
  showName,
  text1Style,
  style,
  icon1 = svg.barCode,
  icon2 = svg.notiw2,
  onPress,
  brCodePressed,
  bottomComponent,
  imgPressed,
}: UserHeaderProps) {
  const [profile, setProfile] = useState(null);
  const [details, setDetails] = useState([]);
  const { handleToast } = useToast();

  function ensureHttps(url) {
    if (url?.startsWith("http://")) {
      return url?.replace("http://", "https://");
    }
    return url; // Return the URL as is if it already starts with "https://"
  }
  const getUserDetails = async () => {
    try {
      const userDetails = await withApiErrorToast(GetUserDetails(), handleToast);
      if (userDetails.email) {
        setProfile(ensureHttps(userDetails.picture));
        setDetails(userDetails);
      } else {
      }
    } catch (error) {
      ;
    }
  };

  useFocusEffect(
    useCallback(() => {
      getUserDetails();
    }, [])
  );
  return (
    <View style={styles.cont}>
      <View style={styles.mainCont}>
        <View style={styles.content1}>
          <TouchableOpacity onPress={imgPressed}>
            <Image
              source={
                profile === null
                  ? require("../assets/defualtAvatar.png")
                  : { uri: profile }
              }
              style={{ width: 38, height: 38, borderRadius: 100 }}
            />
          </TouchableOpacity>
          <View style={styles.info}>
            {typeof text1 === 'string' ? (
              <P
                style={[
                  {
                    fontSize: 14,
                    color: "rgba(22, 24, 23, 1)",
                    lineHeight: 21,
                    marginBottom: 4,
                  },
                  text1Style,
                ]}
              >
                {text1}
              </P>
            ) : (
              text1
            )}
            {showStatus && (
              <View style={styles.statusCont}>
                <P style={{ fontSize: 12, color: "rgba(201, 4, 4, 1)" }}>
                  Not verified
                </P>
              </View>
            )}
            {showName && (
              <P
                // @ts-ignore
                style={[
                  {
                    fontSize: 14,
                    color: "#fff",
                    fontFamily: fonts.poppinsRegular,
                  },
                  style,
                ]}
              >
                {showName}
              </P>
            )}
            {bottomComponent}
          </View>
        </View>
        <View style={styles.content2}>
          <MicroBtn
            icon={icon1}
            style={{ marginRight: 8 }}
            onPress={brCodePressed}
          />
          <MicroBtn icon={icon2} onPress={onPress} />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  cont: {
    width: "100%",
    alignItems: "center",
    marginTop: 8,
    marginBottom: 12,
  },
  mainCont: {
    width: "90%",
    display: "flex",
    justifyContent: "space-between",
    flexDirection: "row",
  },
  content1: {
    width: "70%",
    flexDirection: "row",
    alignItems: "center",
  },
  content2: {
    width: 72,
    justifyContent: "flex-end",
    alignItems: "center",
    flexDirection: "row",
    // alignItems: 'center',
  },
  info: {
    marginLeft: 12,
  },
  statusCont: {
    width: 101,
    height: 20,
    backgroundColor: "#fff",
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
  },
});
