import React, { useEffect, useState, useRef } from 'react';
import { StyleSheet, Animated, Dimensions } from 'react-native';
import * as SplashScreen from 'expo-splash-screen';
import { colors } from '../config/colors';
import { SvgXml } from 'react-native-svg';
import { svg } from '../config/Svg';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

const { width, height } = Dimensions.get('window');

interface CustomSplashScreenProps {
  onFinish: () => void;
  appIsReady?: boolean;
}

const CustomSplashScreen: React.FC<CustomSplashScreenProps> = ({ onFinish, appIsReady = false }) => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // State to track animation phases
  const [minDisplayTimeElapsed, setMinDisplayTimeElapsed] = useState(false);
  const [animationComplete, setAnimationComplete] = useState(false);

  // Start the pulse animation immediately
  useEffect(() => {
    console.log("Starting pulse animation");

    // Create a pulsing animation that loops
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    // Start the pulse animation
    pulseAnimation.start();

    // Clean up animation when component unmounts
    return () => {
      pulseAnimation.stop();
    };
  }, [pulseAnim]);

  // Set a minimum display time for the splash screen
  useEffect(() => {
    console.log("Setting minimum display time");

    // Minimum time the splash screen should be visible (3 seconds)
    const timer = setTimeout(() => {
      console.log("Minimum display time elapsed");
      setMinDisplayTimeElapsed(true);
    }, 3000);

    // Clean up timer if component unmounts
    return () => clearTimeout(timer);
  }, []);

  // Start fade-out animation when both app is ready and minimum time has elapsed
  useEffect(() => {
    if (appIsReady && minDisplayTimeElapsed && !animationComplete) {
      console.log("Starting fade-out animation");

      // Start fade-out animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
      ]).start(async () => {
        console.log("Fade-out animation complete");

        // Hide the native splash screen
        await SplashScreen.hideAsync();

        // Mark animation as complete
        setAnimationComplete(true);

        // Notify parent component that splash screen is finished
        onFinish();
      });
    }
  }, [appIsReady, minDisplayTimeElapsed, animationComplete, fadeAnim, scaleAnim, onFinish]);

  // Render the splash screen
  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }]
        }
      ]}
    >
      {/* Use a regular View with animated transform instead of trying to animate the SVG directly */}
      <Animated.View
        style={[
          styles.svgContainer,
          {
            transform: [{ scale: pulseAnim }]
          }
        ]}
      >
        <SvgXml
          xml={svg.appLogo}
          width={width * 0.3}
          height={height * 0.1}
        />
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 999,
  },
  image: {
    // Width and height are now set directly on the SVG component
  },
  svgContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default CustomSplashScreen;
