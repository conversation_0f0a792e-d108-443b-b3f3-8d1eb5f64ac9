import React from 'react';
import { View, StyleSheet } from 'react-native';
import WelcomeModal from './WelcomeModal';
import { useWelcomeModal } from '../context/WelcomeModalContext';
import { useNavigation } from '@react-navigation/native';

export default function GlobalWelcomeModal() {
  const { isWelcomeModalVisible } = useWelcomeModal();

  if (!isWelcomeModalVisible) return null;

  return (
    <View style={styles.container}>
      <WelcomeModal/>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999, // Higher than BottomSheet
  },
}); 