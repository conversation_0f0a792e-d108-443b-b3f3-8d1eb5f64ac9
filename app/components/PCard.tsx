import React from "react";
import { StyleSheet, View, Text, Image, ImageBackground, Dimensions } from "react-native";
import { colors } from "../config/colors";
import P from "./P";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import { fonts } from "../config/Fonts";
import { LinearGradient } from "expo-linear-gradient";

interface PProps {
  accStatus?: string;
  navigation?: any;
  isProcessing?: boolean;
  errorMessage?: string
}

const { width } = Dimensions.get("window");
export default function PCard({ accStatus, navigation, isProcessing, errorMessage }: PProps) {
  return (
    <ImageBackground
      source={require("../assets/prFrame/pr4.png")}
      resizeMode="cover"
      style={styles.rCard}
    >
      {/* <View style={styles.rCard}> */}
      {accStatus === "verified" ? (
        <Image
          source={require("../assets/success.png")}
          style={{ width: 48, height: 48, marginRight: 8 }}
        />
      ) : (
        <SvgXml xml={svg.idPurple} />
      )}
      <View style={{ width: "75%" }}>
        <P style={styles.text2}>
          {accStatus === "pending" || isProcessing
            ? `Your account identity verification is currently processing.`
            : accStatus === "failed"
            ? `Your identity verification failed. Reason ${errorMessage ? errorMessage : "None"}`
            : accStatus === "verified"
            ? `Your account identity verification is successfully verified.`
            : `Verify your account identity to unlock the full SFx money app features.`}{" "}
          {/* <Text
            // onPress={() => {
            //   accStatus === "verified"
            //     ? ""
            //     : navigation.navigate("AccountVerificationPromt");
            // }}
            style={{
              fontFamily: fonts.poppinsMedium,
              textDecorationLine: "underline",
            }}
          >
            {accStatus === "pending"
              ? `Please check status`
              : accStatus === "failed"
              ? ` try again`
              : accStatus === "verified"
              ? `exclusive offers`
              : `account verification`}
          </Text>{" "} */}
        </P>
        {/* </View> */}
      </View>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  rCard: {
    width: width * 0.9,
    padding: 6,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    overflow: "hidden",
    // justifyContent: "space-between",
  },
  text1: {
    fontSize: 14,
    fontFamily: fonts.poppinsMedium,
  },
  text2: {
    fontSize: 12,
    fontFamily: fonts.poppinsSemibold,
    color: "#F8F5FF",
  },
});
