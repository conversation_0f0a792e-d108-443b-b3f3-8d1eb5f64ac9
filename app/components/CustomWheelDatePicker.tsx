import React, { useState, useEffect, useRef } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Text,
  Dimensions,
  Platform,
  SafeAreaView,
  ScrollView,
  NativeSyntheticEvent,
  NativeScrollEvent,
} from "react-native";
import DatePicker from "react-native-date-picker";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import P from "./P";

const { height, width } = Dimensions.get("window");

// Month names for conversion
const monthNames = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

interface DatePickerProps {
  isVisible?: boolean;
  onClose?: () => void;
  onDateSelected: (date: { month: string; day: number; year: number }) => void;
  initialDate?: { month: string; day: number; year: number };
  useModal?: boolean;
  dateOfBirth?: boolean;
}

const CustomWheelDatePicker: React.FC<DatePickerProps> = ({
  isVisible = true,
  onClose = () => {},
  onDateSelected,
  initialDate,
  useModal = true,
  dateOfBirth = false,
}) => {
  // Refs for ScrollViews
  const monthScrollRef = useRef<ScrollView>(null);
  const dayScrollRef = useRef<ScrollView>(null);
  const yearScrollRef = useRef<ScrollView>(null);

  // Convert initialDate to Date object for the date picker
  const getInitialDate = (): Date => {
    if (initialDate) {
      const monthIndex = monthNames.indexOf(initialDate.month);
      if (monthIndex !== -1) {
        return new Date(initialDate.year, monthIndex, initialDate.day);
      }
    }
    // Default to current date
    return new Date();
  };

  // Calculate min and max dates for date of birth
  const getMinMaxDates = () => {
    if (dateOfBirth) {
      const maxDate = new Date(); // Today
      const minDate = new Date();
      minDate.setFullYear(1900); // Set minimum year to 1900
      return { minDate, maxDate };
    }
    // Default date range (current year +/- 10 years)
    const currentYear = new Date().getFullYear();
    const minDate = new Date();
    minDate.setFullYear(currentYear - 10);
    const maxDate = new Date();
    maxDate.setFullYear(currentYear + 1); // Next year
    return { minDate, maxDate };
  };

  const { minDate, maxDate } = getMinMaxDates();

  // State for selected date
  const [selectedDate, setSelectedDate] = useState<Date>(getInitialDate());

  // Generate arrays for days, months, and years
  const days = Array.from({ length: 31 }, (_, i) => i + 1);
  const years = Array.from(
    { length: maxDate.getFullYear() - minDate.getFullYear() + 1 },
    (_, i) => minDate.getFullYear() + i
  );

  // Update state when initialDate changes
  useEffect(() => {
    if (initialDate) {
      setSelectedDate(getInitialDate());
    }
  }, [initialDate]);

  // Scroll to initial positions
  useEffect(() => {
    if (Platform.OS === 'android') {
      const monthIndex = selectedDate.getMonth();
      const dayIndex = selectedDate.getDate() - 1;
      const yearIndex = years.indexOf(selectedDate.getFullYear());

      setTimeout(() => {
        monthScrollRef.current?.scrollTo({ y: monthIndex * 50, animated: false });
        dayScrollRef.current?.scrollTo({ y: dayIndex * 50, animated: false });
        yearScrollRef.current?.scrollTo({ y: yearIndex * 50, animated: false });
      }, 100);
    }
  }, []);

  const handleMonthScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const y = event.nativeEvent.contentOffset.y;
    const monthIndex = Math.round(y / 50);
    if (monthIndex >= 0 && monthIndex < monthNames.length) {
      const newDate = new Date(selectedDate);
      newDate.setMonth(monthIndex);
      setSelectedDate(newDate);
    }
  };

  const handleDayScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const y = event.nativeEvent.contentOffset.y;
    const dayIndex = Math.round(y / 50);
    if (dayIndex >= 0 && dayIndex < days.length) {
      const newDate = new Date(selectedDate);
      newDate.setDate(dayIndex + 1);
      setSelectedDate(newDate);
    }
  };

  const handleYearScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const y = event.nativeEvent.contentOffset.y;
    const yearIndex = Math.round(y / 50);
    if (yearIndex >= 0 && yearIndex < years.length) {
      const newDate = new Date(selectedDate);
      newDate.setFullYear(years[yearIndex]);
      setSelectedDate(newDate);
    }
  };

  const handleApply = () => {
    // Convert the Date object to the expected format
    const month = monthNames[selectedDate.getMonth()];
    const day = selectedDate.getDate();
    const year = selectedDate.getFullYear();

    onDateSelected({
      month,
      day,
      year,
    });

    onClose();
  };

  // The actual date picker content
  const renderDatePickerContent = () => (
    <View style={styles.modalContainer}>
      <ScrollView
        contentContainerStyle={{ paddingBottom: 50 }}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.pickerContainer}>
          {Platform.OS === 'ios' ? (
            <DatePicker
              date={selectedDate}
              onDateChange={setSelectedDate}
              mode="date"
              minimumDate={minDate}
              maximumDate={maxDate}
              dividerColor={colors.primary}
              theme="light"
              style={styles.datePicker}
              is24hourSource="locale"
            />
          ) : (
            <View style={styles.androidPickerContainer}>
              <View style={styles.androidPickerColumn}>
                <ScrollView
                  ref={monthScrollRef}
                  showsVerticalScrollIndicator={false}
                  onMomentumScrollEnd={handleMonthScroll}
                  snapToInterval={50}
                  decelerationRate="fast"
                >
                  {monthNames.map((month, index) => (
                    <TouchableOpacity
                      key={month}
                      style={[
                        styles.androidPickerItem,
                        selectedDate.getMonth() === index && styles.androidPickerItemSelected,
                      ]}
                      onPress={() => {
                        const newDate = new Date(selectedDate);
                        newDate.setMonth(index);
                        setSelectedDate(newDate);
                        monthScrollRef.current?.scrollTo({ y: index * 50, animated: true });
                      }}
                    >
                      <P style={[
                        styles.androidPickerItemText,
                        selectedDate.getMonth() === index && styles.androidPickerItemTextSelected,
                      ]}>
                        {month}
                      </P>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
              <View style={styles.androidPickerColumn}>
                <ScrollView
                  ref={dayScrollRef}
                  showsVerticalScrollIndicator={false}
                  onMomentumScrollEnd={handleDayScroll}
                  snapToInterval={50}
                  decelerationRate="fast"
                >
                  {days.map((day, index) => (
                    <TouchableOpacity
                      key={day}
                      style={[
                        styles.androidPickerItem,
                        selectedDate.getDate() === day && styles.androidPickerItemSelected,
                      ]}
                      onPress={() => {
                        const newDate = new Date(selectedDate);
                        newDate.setDate(day);
                        setSelectedDate(newDate);
                        dayScrollRef.current?.scrollTo({ y: index * 50, animated: true });
                      }}
                    >
                      <P style={[
                        styles.androidPickerItemText,
                        selectedDate.getDate() === day && styles.androidPickerItemTextSelected,
                      ]}>
                        {day}
                      </P>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
              <View style={styles.androidPickerColumn}>
                <ScrollView
                  ref={yearScrollRef}
                  showsVerticalScrollIndicator={false}
                  onMomentumScrollEnd={handleYearScroll}
                  snapToInterval={50}
                  decelerationRate="fast"
                >
                  {years.map((year, index) => (
                    <TouchableOpacity
                      key={year}
                      style={[
                        styles.androidPickerItem,
                        selectedDate.getFullYear() === year && styles.androidPickerItemSelected,
                      ]}
                      onPress={() => {
                        const newDate = new Date(selectedDate);
                        newDate.setFullYear(year);
                        setSelectedDate(newDate);
                        yearScrollRef.current?.scrollTo({ y: index * 50, animated: true });
                      }}
                    >
                      <P style={[
                        styles.androidPickerItemText,
                        selectedDate.getFullYear() === year && styles.androidPickerItemTextSelected,
                      ]}>
                        {year}
                      </P>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>
          )}
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
            <P style={styles.cancelButtonText}>Cancel</P>
          </TouchableOpacity>

          <TouchableOpacity style={styles.applyButton} onPress={handleApply}>
            <P style={styles.applyButtonText}>Apply</P>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );

  // Conditionally render with or without modal
  return useModal ? (
    <Modal
      visible={isVisible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <SafeAreaView style={styles.modalOverlay}>
        {renderDatePickerContent()}
      </SafeAreaView>
    </Modal>
  ) : (
    renderDatePickerContent()
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: Platform.OS === "ios" ? 30 : 20,
    paddingTop: Platform.OS === "ios" ? 15 : (5 * height) / 100,
    paddingLeft: 0,
    paddingRight: 0,
    width: "100%",
    maxHeight: Platform.OS === "ios" ? height * 0.8 : height * 0.7,
    backgroundColor: colors.white,
  },
  pickerContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 20,
  },
  datePicker: {
    width: Math.min(width * 0.9, 350),
    height: 220,
  },
  androidPickerContainer: {
    flexDirection: "row",
    width: Math.min(width * 0.9, 350),
    height: 220,
    backgroundColor: colors.white,
    borderRadius: 12,
    overflow: "hidden",
  },
  androidPickerColumn: {
    flex: 1,
    borderRightWidth: 1,
    borderRightColor: "#E6E6E6",
  },
  androidPickerItem: {
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 10,
  },
  androidPickerItemSelected: {
    backgroundColor: "#F5F2FF",
  },
  androidPickerItemText: {
    fontSize: 16,
    color: colors.dGray,
    fontFamily: fonts.poppinsRegular,
  },
  androidPickerItemTextSelected: {
    color: colors.primary,
    fontFamily: fonts.poppinsMedium,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 25,
    borderWidth: 1,
    borderColor: "#E6E6E6",
    marginRight: 10,
  },
  cancelButtonText: {
    color: colors.primary,
  },
  applyButton: {
    flex: 1,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 25,
    backgroundColor: colors.primary,
    marginLeft: 10,
  },
  applyButtonText: {
    color: "white",
  },
});

export default CustomWheelDatePicker; 