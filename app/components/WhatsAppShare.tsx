import React, { useState } from 'react';
import { View, TouchableOpacity, Linking, StyleSheet, Platform } from 'react-native';
import { SvgXml } from 'react-native-svg';
import { svg } from '../config/Svg';
import P from './P';
import { colors } from '../config/colors';
import { fonts } from '../config/Fonts';
import { useToast } from '../context/ToastContext';

interface WhatsAppShareProps {
  phoneNumber?: string;
  message: string;
  size?: number;
  containerStyle?: object;
  showText?: boolean;
  text?: string;
  onShareComplete?: () => void;
}

/**
 * WhatsAppShare component for sharing content via WhatsApp
 * 
 * @param phoneNumber - Optional WhatsApp phone number to send message to (include country code without +)
 * @param message - The message to share
 * @param size - Size of the WhatsApp icon (default: 40)
 * @param containerStyle - Additional styles for the container
 * @param showText - Whether to show text next to the icon (default: false)
 * @param text - Text to display next to the icon (default: "Share via WhatsApp")
 * @param onShareComplete - Callback function to execute after sharing
 */
const WhatsAppShare: React.FC<WhatsAppShareProps> = ({
  phoneNumber,
  message,
  size = 40,
  containerStyle,
  showText = false,
  text = "Share via WhatsApp",
  onShareComplete
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { handleToast } = useToast();

  const handleShare = async () => {
    setIsLoading(true);
    
    try {
      // Encode the message for URL
      const encodedMessage = encodeURIComponent(message);
      
      // Create the WhatsApp URL
      let url = phoneNumber 
        ? `https://wa.me/${phoneNumber}?text=${encodedMessage}` 
        : `whatsapp://send?text=${encodedMessage}`;
      
      // Check if WhatsApp is installed
      const canOpen = await Linking.canOpenURL(url);
      
      if (canOpen) {
        await Linking.openURL(url);
        if (onShareComplete) {
          onShareComplete();
        }
      } else {
        // If WhatsApp is not installed, try web version for non-phone number shares
        if (!phoneNumber) {
          url = `https://web.whatsapp.com/send?text=${encodedMessage}`;
          if (await Linking.canOpenURL(url)) {
            await Linking.openURL(url);
            if (onShareComplete) {
              onShareComplete();
            }
          } else {
            handleToast("WhatsApp is not installed on your device", "error");
          }
        } else {
          handleToast("WhatsApp is not installed on your device", "error");
        }
      }
    } catch (error) {
      console.error("Error sharing via WhatsApp:", error);
      handleToast("Could not open WhatsApp", "error");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <TouchableOpacity 
      style={[styles.container, containerStyle]} 
      onPress={handleShare}
      disabled={isLoading}
    >
      <SvgXml 
        xml={svg.whatsApp} 
        width={size} 
        height={size} 
      />
      {showText && (
        <P style={styles.text}>{text}</P>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
  },
  text: {
    marginLeft: 10,
    fontFamily: fonts.poppinsRegular,
    color: colors.black,
    fontSize: 14,
  }
});

export default WhatsAppShare;
