import React from 'react';
import { View, ViewStyle, Platform } from 'react-native';

interface DashedBorderViewProps {
  children?: React.ReactNode;
  borderColor?: string;
  borderWidth?: number;
  style?: ViewStyle;
  dashLength?: number;
  gapLength?: number;
}

const DashedBorderView: React.FC<DashedBorderViewProps> = ({
  children,
  borderColor = '#E5E5E5',
  borderWidth = 1,
  style,
  dashLength = 4,
  gapLength = 4,
}) => {
  // For iOS, use a different approach to avoid rendering issues
  if (Platform.OS === 'ios') {
    return (
      <View style={style}>
        {children}
        {/* Create dashed effect using multiple small views */}
        <View
          style={{
            flexDirection: 'row',
            height: borderWidth,
            marginTop: 8,
          }}
        >
          {Array.from({ length: 50 }, (_, index) => (
            <View
              key={index}
              style={{
                width: dashLength,
                height: borderWidth,
                backgroundColor: index % 2 === 0 ? borderColor : 'transparent',
                marginRight: index % 2 === 0 ? gapLength : 0,
              }}
            />
          ))}
        </View>
      </View>
    );
  }

  // For Android, use the standard border style
  return (
    <View
      style={[
        {
          borderBottomWidth: borderWidth,
          borderBottomColor: borderColor,
          borderStyle: 'dashed',
          // paddingBottom: 8,
        },
        style,
      ]}
    >
      {children}
    </View>
  );
};

export default DashedBorderView;
