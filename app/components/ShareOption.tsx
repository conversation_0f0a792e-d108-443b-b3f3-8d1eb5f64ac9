import React from "react";
import { View, Text, TouchableOpacity, StyleSheet, Share } from "react-native";

const ShareOption = () => {
  const handleShare = async (message) => {
    try {
      const result = await Share.share({
        message: message,
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
        } else {
       
        }
      } else if (result.action === Share.dismissedAction) {
      }
    } catch (error) {
      console.error("Error sharing:", error.message);
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.button}
        onPress={() => handleShare("Check this out on WhatsApp!")}
      >
        <Text style={styles.buttonText}>Share on WhatsApp</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.button}
        onPress={() => handleShare("Check this out on Telegram!")}
      >
        <Text style={styles.buttonText}>Share on Telegram</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.button}
        onPress={() => handleShare("Check this out on X (Twitter)!")}
      >
        <Text style={styles.buttonText}>Share on X (Twitter)</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={() => handleShare("Check this out!")}>
        <Text style={styles.buttonText}>Show More</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  button: {
    padding: 10,
    backgroundColor: "#007bff",
    marginBottom: 10,
    borderRadius: 5,
  },
  buttonText: {
    color: "#fff",
    textAlign: "center",
    fontSize: 16,
  },
});

export default ShareOption;
