import React, { CSSProperties } from "react";
import { StyleSheet, TouchableOpacity, View, ViewStyle } from "react-native";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";

interface PProps {
  navigation: any;
  style?: ViewStyle;
}

function AuthHeader({ navigation, style }: PProps) {
  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={[styles.btn]}
        onPress={() => {
          navigation.pop();
        }}
      >
        <SvgXml xml={svg.arrowLeft} />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "90%",
    alignSelf: "center",
    marginTop: 6,
  },
  btn: {
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
  },
});

export default AuthHeader;
