import React, { CSSProperties, useState, useEffect } from "react";
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Dimensions,
  TouchableWithoutFeedback,
  Keyboard,
  ScrollView,
  Platform,
  Modal,
  ImageBackground,
} from "react-native";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import { fonts } from "../config/Fonts";
import { colors } from "../config/colors";

interface PProps {
  isVisible?: any;
  onClose?: any;
  components?: any;
  backspaceText?: string;
  showBackArrow?: Boolean;
  onBackArrowPress?: any;
  extraModalStyle?: CSSProperties;
  modalContentStyle?: CSSProperties;
  componentHolderStyle?: CSSProperties;
  headerStyle?: CSSProperties;
  addBg?: boolean;
}

const { width, height } = Dimensions.get("window");
export default function BottomSheet({
  isVisible,
  onClose,
  components,
  backspaceText,
  showBackArrow = true,
  onBackArrowPress,
  extraModalStyle,
  modalContentStyle,
  componentHolderStyle,
  headerStyle,
  addBg = false,
}: PProps) {
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  return (
    <Modal
      visible={isVisible}
      style={styles.modal}
      statusBarTranslucent={true}
      animationType="slide"
      transparent
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.bodyWrapper}></View>
      </TouchableWithoutFeedback>
      {/* <View
        style={[
          styles.extraModal,
          { top: keyboardVisible ? (19.5 * height) / 100 : "" },
          extraModalStyle,
        ]}
      ></View> */}
      <View
        style={[
          styles.modalContent,
          // @ts-ignore
          modalContentStyle,
        ]}
      >
        {addBg &&  <ImageBackground
          source={require("../assets/background.png")}
          style={{ position: "absolute", bottom: 0, width: "100%", height: 120 }}
        /> }
       
        <View style={styles.dash}></View>
        {/* @ts-ignore */}
        <View style={[styles.section1, headerStyle]}>
          {showBackArrow && (
            <TouchableOpacity onPress={onBackArrowPress}>
              <SvgXml xml={svg.goBackIcon} style={{ marginRight: 12 }} />
            </TouchableOpacity>
          )}

          <Text
            style={{
              fontSize: 14,
              fontFamily: fonts.poppinsMedium,
              lineHeight: 21,
            }}
          >
            {backspaceText}
          </Text>
          <TouchableOpacity
            style={{ position: "absolute", right: 24 }}
            onPress={onClose}
          >
            <SvgXml xml={svg.xClose} />
          </TouchableOpacity>
        </View>
        {/* @ts-ignore */}
        <View
          // @ts-ignore
          style={[{ paddingLeft: 24, paddingRight: 24 }, componentHolderStyle]}
        >
          {components}
        </View>
        {/* <ScrollView>

        </ScrollView> */}
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modal: {
    // backgroundColor: 'red',
    justifyContent: "flex-end",
    margin: 0,
    height,
    position: "absolute",
  },
  modalContent: {
    height: "56%",
    width,
    backgroundColor: colors.white,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    paddingTop: 0,
    position: "absolute",
    bottom: 0,
    marginBottom: -30,
  },
  extraModal: {
    width: "90%",
    height: "53%",
    position: "absolute",
    alignSelf: "center",
    borderRadius: 10,
    backgroundColor: "rgba(230, 229, 229, 1)",
    bottom: 0,
  },
  section1: {
    height: 61,
    // backgroundColor: 'red',
    width: "100%",
    borderBottomColor: "rgba(139, 144, 154, 0.25)",
    borderBottomWidth: 1,
    flexDirection: "row",
    alignItems: "center",
    marginTop: 11,
    paddingLeft: 24,
    paddingRight: 24,
  },
  bodyWrapper: {
    height: "100%",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  dash: {
    width: 36,
    height: 5,
    borderRadius: 100,
    backgroundColor: "#3C3C434D",
    alignSelf: "center",
    marginTop: 12,
    position: "absolute",
  },
});
