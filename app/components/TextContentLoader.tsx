import React from 'react';
import ContentLoader, { Rect } from 'react-content-loader/native';
import { colors } from '../config/colors';
interface Props{
  width?:any
}
const TextContentLoader = ({width}:Props) => (
  <ContentLoader 
    width={width ||400}
    height={20}
    backgroundColor={colors.stroke}
    foregroundColor={colors.secBackground}
  >
    <Rect x="0" y="0" rx="5" ry="5" width="120" height="15" /> 
  </ContentLoader>
);

export default TextContentLoader;
