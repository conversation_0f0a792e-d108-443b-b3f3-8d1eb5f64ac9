import React, { useState, useEffect } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  TextInput,
} from "react-native";
import P from "./P";
import { svg } from "../config/Svg";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import ListItemSelect from "./ListItemSelect";
import { SvgXml } from "react-native-svg";
import { countries } from "./counties";
import { securityQuestions } from "./securityQuestions";

interface PProps {
  onPress?: any;
  setQuestion?: any;
  onActiveCountryChange?: (index: String | null) => void;
  onActiveFlag?: (index: String | null) => void;
}

const { width, height } = Dimensions.get("window");
export default function SecurityQuestionSelect({
  onPress,
  onActiveCountryChange,
  onActiveFlag,
  setQuestion
}: PProps) {
  const [activeType, setActiveType] = useState(null);
  const [activeCountry, setActiveCountry] = useState("");
  const [activeFlag, setActiveFlag] = useState(null);
  const [searchQuery, setSearchQuery] = useState<string>("");

  const filteredCountries = securityQuestions;

  useEffect(() => {
    if (onActiveCountryChange) {
      onActiveCountryChange(activeCountry);
    }
  }, [activeType]);
  useEffect(() => {
    if (onActiveFlag) {
      onActiveFlag(activeFlag)
    }
  }, [activeFlag]);
  return (
    <View style={styles.viewContent}>
      {/* <View style={styles.search}>
        <SvgXml xml={svg.search} style={{ marginRight: 8 }} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search country"
          cursorColor={colors.black}
          value={searchQuery}
          onChangeText={(text) => setSearchQuery(text)}
        />
      </View> */}
      <P
        style={{
          fontSize: 12,
          color: colors.gray,
          font: fonts.poppinsRegular,
          marginBottom: 8,
        }}
      >
       Select your security question
      </P>
      <ScrollView showsVerticalScrollIndicator={false}>
        {filteredCountries.map((item, index) => (
          <ListItemSelect
            text1={item}
            image={""}
            key={index}
            isActive={activeType === index}
            onPress={() => {
              setQuestion(item)
              setTimeout(() => {
                onPress(index);
              }, 200);
            }}
            containerStyle={{
              marginBottom: index === filteredCountries.length - 1 ? 400 : 16,
            }}
          />
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  viewContent: {
    width: "100%",
    paddingTop: 24,
  },
  search: {
    width: "100%",
    height: (5.5 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    marginBottom: 24,
    borderWidth: 1,
    borderRadius: 99,
    borderColor: colors.stroke,
    padding: 12,
    paddingLeft: 14,
    paddingRight: 14,
    flexDirection: "row",
  },
  searchInput: {
    width: "90%",
    height: 24,
    // backgroundColor: 'red',
    fontFamily: fonts.poppinsRegular,
    lineHeight: 24,
    fontSize: 14,
    // paddingLeft: 16,
  },
});
