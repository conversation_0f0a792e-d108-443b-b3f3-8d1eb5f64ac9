import React from "react";
import { Dimensions, View } from "react-native";
import ContentLoader, { Rect, Circle } from "react-content-loader/native";
import { color } from "react-native-reanimated";
import { colors } from "../config/colors";

const { width, height } = Dimensions.get("window");
export const HomeSkeleton = () => {
  return (
    <View>
      <ContentLoader
        speed={2}
        width={"100%"}
        height={height}
        viewBox="0 0 400 800"
        backgroundColor="#f3f3f3"
        foregroundColor="#ecebeb"
      >
        {/* Circle for profile picture */}
        <Circle cx="40" cy="50" r="30" />

        {/* Rectangles for Welcome and username */}
        <Rect x="90" y="35" rx="5" ry="5" width="200" height="15" />
        <Rect x="90" y="55" rx="5" ry="5" width="150" height="15" />

        {/* Tab buttons - Accounts, Rate, Discover */}
        <Rect x="20" y="100" rx="10" ry="10" width="80" height="30" />
        <Rect x="120" y="100" rx="10" ry="10" width="50" height="30" />
        <Rect x="190" y="100" rx="10" ry="10" width="80" height="30" />

        {/* Available balance block */}
        <Rect x="20" y="150" rx="10" ry="10" width="360" height="100" />

        {/* Buttons for Send, Swap, Card, Reward */}
        <Rect x="20" y="270" rx="10" ry="10" width="70" height="50" />
        <Rect x="110" y="270" rx="10" ry="10" width="70" height="50" />
        <Rect x="200" y="270" rx="10" ry="10" width="70" height="50" />
        <Rect x="290" y="270" rx="10" ry="10" width="70" height="50" />

        {/* Personalized recommendation block */}
        <Rect x="20" y="340" rx="10" ry="10" width="360" height="60" />

        {/* Recent transaction block */}
        <Rect x="20" y="420" rx="10" ry="10" width="360" height="120" />

        {/* Bottom navigation (Home, Wallet, History, Settings) */}
        <Rect x="20" y="570" rx="10" ry="10" width="70" height="50" />
        <Rect x="110" y="570" rx="10" ry="10" width="70" height="50" />
        <Rect x="200" y="570" rx="10" ry="10" width="70" height="50" />
        <Rect x="290" y="570" rx="10" ry="10" width="70" height="50" />
      </ContentLoader>
    </View>
  );
};

export const WalletSkeleton = () => {
  return (
    <View>
      <ContentLoader
        speed={2}
        width={"100%"}
        height={height}
        viewBox="0 0 400 800"
        backgroundColor={colors.stroke}
        foregroundColor={colors.secBackground}
      >
        {/* Tabs: Account and Insight */}
        <Rect x="20" y="20" rx="10" ry="10" width="90" height="35" />
        <Rect x="120" y="20" rx="10" ry="10" width="90" height="35" />

        {/* Total Wallet Balance text */}
        <Rect x="20" y="70" rx="10" ry="10" width="140" height="20" />

        {/* Wallet balance block for USD */}
        <Rect x="20" y="100" rx="20" ry="20" width="360" height="80" />

        {/* Wallet balance block for TRY */}
        <Rect x="20" y="190" rx="20" ry="20" width="360" height="80" />

        {/* Info text */}
        <Rect x="20" y="290" rx="10" ry="10" width="360" height="50" />

        {/* Wallet section title */}
        <Rect x="20" y="360" rx="10" ry="10" width="100" height="20" />

        {/* Wallet details for USD Coin */}
        <Rect x="20" y="390" rx="10" ry="10" width="360" height="80" />

        {/* Wallet details for Tether */}
        <Rect x="20" y="480" rx="10" ry="10" width="360" height="80" />

        {/* Bottom navigation */}
        <Rect x="20" y="580" rx="10" ry="10" width="70" height="50" />
        <Rect x="110" y="580" rx="10" ry="10" width="70" height="50" />
        <Rect x="200" y="580" rx="10" ry="10" width="70" height="50" />
        <Rect x="290" y="580" rx="10" ry="10" width="70" height="50" />
      </ContentLoader>
    </View>
  );
};

export const WalletDetailsSkeleton = () => {
  return (
    <View>
      <ContentLoader
        speed={2}
        width={"100%"}
        height={height}
        viewBox="0 0 400 800"
        backgroundColor="#f3f3f3"
        foregroundColor="#ecebeb"
      >
        {/* QR code block */}
        <Rect x="80" y="50" rx="10" ry="10" width="240" height="240" />

        {/* 'Network arrival in...' text */}
        <Rect x="120" y="310" rx="5" ry="5" width="160" height="20" />

        {/* Wallet Address section */}
        <Rect x="40" y="370" rx="5" ry="5" width="100" height="15" />
        <Rect x="150" y="370" rx="5" ry="5" width="200" height="15" />

        {/* Copy button */}
        <Rect x="350" y="370" rx="5" ry="5" width="40" height="15" />

        {/* Network section */}
        <Rect x="40" y="420" rx="5" ry="5" width="300" height="40" />

        {/* Note message */}
        <Rect x="40" y="480" rx="10" ry="10" width="360" height="80" />

        {/* Bottom notice */}
        <Rect x="40" y="580" rx="5" ry="5" width="320" height="20" />
      </ContentLoader>
    </View>
  );
};

export const HistorySkeleton = () => {
  return (
    <View>
      <ContentLoader
        speed={2}
        width={"100%"}
        height={height}
        viewBox={`0 0 ${width} ${height}`}
        backgroundColor="#f3f3f3"
        foregroundColor="#ecebeb"
      >
        {/* Filter buttons (All categories and All status) */}
        <Rect x="20" y="20" rx="10" ry="10" width="160" height="30" />
        <Rect x="200" y="20" rx="10" ry="10" width="160" height="30" />

        {/* Transactions group title - Today */}
        <Rect x="20" y="70" rx="5" ry="5" width="100" height="20" />

        {/* Transaction rows - Icons and details */}
        {Array.from({ length: 5 }).map((_, index) => (
          <React.Fragment key={index}>
            {/* Circle for the transaction icon */}
            <Circle cx="40" cy={120 + index * 70} r="20" />

            {/* Transaction title */}
            <Rect
              x="70"
              y={105 + index * 70}
              rx="5"
              ry="5"
              width="220"
              height="15"
            />

            {/* Time of transaction */}
            <Rect
              x="70"
              y={125 + index * 70}
              rx="5"
              ry="5"
              width="100"
              height="15"
            />

            {/* Amount */}
            <Rect
              x="300"
              y={105 + index * 70}
              rx="5"
              ry="5"
              width="60"
              height="20"
            />
          </React.Fragment>
        ))}

        {/* Transactions group title - Yesterday */}
        <Rect x="20" y={120 + 5 * 70} rx="5" ry="5" width="100" height="20" />

        {/* Yesterday transactions (2 items) */}
        {Array.from({ length: 2 }).map((_, index) => (
          <React.Fragment key={index}>
            {/* Circle for the transaction icon */}
            <Circle cx="40" cy={240 + 5 * 70 + index * 70} r="20" />

            {/* Transaction title */}
            <Rect
              x="70"
              y={225 + 5 * 70 + index * 70}
              rx="5"
              ry="5"
              width="220"
              height="15"
            />

            {/* Time of transaction */}
            <Rect
              x="70"
              y={245 + 5 * 70 + index * 70}
              rx="5"
              ry="5"
              width="100"
              height="15"
            />

            {/* Amount */}
            <Rect
              x="300"
              y={225 + 5 * 70 + index * 70}
              rx="5"
              ry="5"
              width="60"
              height="20"
            />
          </React.Fragment>
        ))}

        {/* Bottom navigation (Home, Wallet, History, Settings) */}
        <Rect x="20" y={height - 80} rx="10" ry="10" width="70" height="50" />
        <Rect x="110" y={height - 80} rx="10" ry="10" width="70" height="50" />
        <Rect x="200" y={height - 80} rx="10" ry="10" width="70" height="50" />
        <Rect x="290" y={height - 80} rx="10" ry="10" width="70" height="50" />
      </ContentLoader>
    </View>
  );
};
