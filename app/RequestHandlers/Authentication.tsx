import AsyncStorage from "@react-native-async-storage/async-storage";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./Request";

const request = new RequestHandler();

export function Login(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    request.post("user/login-user", body, "", [
      (data: any, error: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(data);
        }
      },
    ]);
  });
}

export function CreateUser(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    request.post("user/create-user", body, "", [
      (data: any, error: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(data);
        }
      },
    ]);
  });
}

export function CheckUsername(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    request.post("user/check-username", body, "", [
      (data: any, error: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(data);
        }
      },
    ]);
  });
}

export function SendOtp(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    request.post("user/send-otp", body, "", [
      (data: any, error: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(data);
        }
      },
    ]);
  });
}

export function VerifyOtp(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token || "";
        request.post("user/verify-otp", body, token, [
          (data: any, error: any) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function ResetPasswordVerifyOtp(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    request.post("user/verify-otp", body, "", [
      (data: any, error: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(data);
        }
      },
    ]);
  });
}
export function NewUserVerifyOtp(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    request.post("user/verify-otp", body, "", [
      (data: any, error: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(data);
        }
      },
    ]);
  });
}
export function VerifyOtpSession(body: object, token: string): Promise<any> {
  return new Promise((resolve, reject) => {
    request.post("user/verify-otp", body, token, [
      (data: any, error: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(data);
        }
      },
    ]);
  });
}

export function ForgotPassword(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    request.post("user/forgot-password", body, "", [
      (data: any, error: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(data);
        }
      },
    ]);
  });
}

export function ResetPassword(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    request.post("user/reset-password", body, "", [
      (data: any, error: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(data);
        }
      },
    ]);
  });
}
export function ValidateGoogleToken(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    request.post("user/verify-id-token", body, "", [
      (data: any, error: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(data);
        }
      },
    ]);
  });
}
export function ValidateAppleToken(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    request.post("user/apple-auth", body, "", [
      (data: any, error: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(data);
        }
      },
    ]);
  });
}
