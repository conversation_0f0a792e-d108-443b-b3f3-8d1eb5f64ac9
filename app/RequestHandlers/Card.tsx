import AsyncStorage from "@react-native-async-storage/async-storage";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./Request";
import { RequestHandler2 } from "./Request2";

const request = new RequestHandler();
const request2 = new RequestHandler2();

export function PayForCard(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`card/create-card`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}

export function Getcards(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`card/get`, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}

export function GetcardById(id: string): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`card/get/${id}`, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function GetcardByToken(tkn: string): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request2.get(
          `v1/issuing/cards/get_card_details_from_token?token=${tkn}`,
          token,
          [(data: any, error?: any) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          }]
        );
      })
      .catch((error) => reject(error));
  });
}

export function WithdrawFromCard(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`card/withdraw`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}

export function TopUpCard(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`card/fund`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}

export function FreezeCard(id): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.patch(`card/freeze-card/${id}`, {}, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function UnFreezeCard(id): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.patch(`card/unfreeze-card/${id}`, {}, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}
export function ChangeCardColor(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`card/update-color`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}

export function UpdateCardPin(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`card/update-pin`, body, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}

export function DeleteCard(cardId: string): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.delete(`card/delete-card/${cardId}`, {}, token, [(data, error) => {
          if (error) {
            reject(error);
          } else {
            resolve(data);
          }
        }]);
      })
      .catch((error) => reject(error));
  });
}