import AsyncStorage from "@react-native-async-storage/async-storage";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./Request";

const request = new RequestHandler();

export function GetNotifications(
  page: number,
  limit: number,
  path: string
): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        const url = `notification/get?page=${page}&limit=${limit}&status=all&type=all&path=${path}`;
        request.get(url, token, [
          (data: any, error: any) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function MarkAsRead(id: string): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        const url = `notification/mark-read/${id}`;
        request.patch(url, {}, token, [
          (data: any, error: any) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function MarkAllAsRead(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        const url = `notification/mark-all-as-read`;
        request.patch(url, {}, token, [
          (data: any, error: any) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
