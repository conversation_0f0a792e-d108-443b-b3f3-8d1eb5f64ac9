import AsyncStorage from "@react-native-async-storage/async-storage";
import Constants from "expo-constants";
import DeviceInfo from "react-native-device-info";
import { Platform } from "react-native";
import { encryptGCM } from "../Utils/encrypt";

export async function getDeviceFingerprint(): Promise<string> {
  const info = [
    DeviceInfo.getSystemName(),
    DeviceInfo.getSystemVersion(),
    await DeviceInfo.getUniqueId(),
    Platform.OS,
    Platform.Version,
  ].join("|");

  let hash = 0;
  for (let i = 0; i < info.length; i++) {
    const char = info.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash;
  }

  const macBytes = [];
  for (let i = 0; i < 6; i++) {
    macBytes.push(
      ((Math.abs(hash) >> (i * 4)) & 0xff).toString(16).padStart(2, "0")
    );
  }

  return macBytes.join(":");
}
// utils/time.ts
export function getUTCTimestamp(): string {
  const now = new Date();
  return (
    `${now.getUTCDate().toString().padStart(2, "0")}/` +
    `${(now.getUTCMonth() + 1).toString().padStart(2, "0")}/` +
    `${now.getUTCFullYear()} ` +
    `${now.getUTCHours().toString().padStart(2, "0")}:` +
    `${now.getUTCMinutes().toString().padStart(2, "0")}:` +
    `${now.getUTCSeconds().toString().padStart(2, "0")}.` +
    `${now.getUTCMilliseconds().toString().padStart(3, "0")}`
  );
}


// Generate encrypted API key for requests
export async function generateEncryptedApiKey(): Promise<string> {
  try {
    const mac = await getDeviceFingerprint();
    const timestamp = getUTCTimestamp();
    const dataToEncrypt = `${mac}|${timestamp}`;

    return await encryptGCM(dataToEncrypt);
  } catch (error) {
    console.error('Failed to generate encrypted API key:', error);
    throw new Error('Failed to generate encrypted API key');
  }
}

export class RequestHandler {
  constructor() {}
  private BASE_URL = Constants.expoConfig.extra.STAGING_BASE_URL;
  // private BASE_URL = Constants.expoConfig.extra.PROD_BASE_URL;
  // private BASE_URL = "https://dev-api.sfxchange.co/";
  // private BASE_URL = "https://prod-api.sfxchange.co/";
  private async getUniqueID(): Promise<string | null> {
    return await AsyncStorage.getItem("uniqueID");
  }

  private async _request(
    method: string,
    path: string,
    body?: object,
    token?: string,
    functions?: Array<(data: any, error?: any) => void>,
    timeout: number = 60000 // 60 seconds
  ) {
    const UNIQUE_ID = await this.getUniqueID();
    const deviceName = await DeviceInfo.getDeviceName();
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    // Generate encrypted API key for this request
    let encryptedApiKey: string;
    try {
      encryptedApiKey = await generateEncryptedApiKey();      
    } catch (error) {
      console.error('Failed to generate encrypted API key:', error);
      // Fallback to empty string or handle as needed
      encryptedApiKey = '';
    }

    let fetchOptions: any = {
      method,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        "x-device-id": UNIQUE_ID || "",
        "x-device": deviceName,
        "x-os": Platform.OS,
        "x-mobile-app": "true",
        'x-api-key': encryptedApiKey,
        Authorization: `Bearer ${token}`,
      },
      signal: controller.signal,
    };
    if (body && method !== "GET") {
      fetchOptions.body = JSON.stringify(body);
    }
    try {
      const res = await fetch(`${this.BASE_URL}${path}`, fetchOptions);
      clearTimeout(timeoutId);
      const data = await res.json();
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(data, undefined));
      } else {
        return data;
      }
    } catch (error: any) {
      clearTimeout(timeoutId);
      let errObj =
        error?.name === "AbortError"
          ? { message: "Request timed out", code: "TIMEOUT" }
          : {
              message: error?.message || "Network error",
              code: "NETWORK_ERROR",
            };
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(undefined, errObj));
      } else {
        throw errObj;
      }
    }
  }

  public async get(
    path: string,
    token?: string,
    functions?: Array<(data: any, error?: any) => void>,
    timeout?: number
  ) {
    return this._request("GET", path, undefined, token, functions, timeout);
  }
  public async post(
    path: string,
    body: object,
    token?: string,
    functions?: Array<(data: any, error?: any) => void>,
    timeout?: number
  ) {
    return this._request("POST", path, body, token, functions, timeout);
  }
  public async put(
    path: string,
    body: object,
    token?: string,
    functions?: Array<(data: any, error?: any) => void>,
    timeout?: number
  ) {
    return this._request("PUT", path, body, token, functions, timeout);
  }
  public async patch(
    path: string,
    body: object,
    token?: string,
    functions?: Array<(data: any, error?: any) => void>,
    timeout?: number
  ) {
    return this._request("PATCH", path, body, token, functions, timeout);
  }
  public async delete(
    path: string,
    body: object,
    token?: string,
    functions?: Array<(data: any, error?: any) => void>,
    timeout?: number
  ) {
    return this._request("DELETE", path, body, token, functions, timeout);
  }
}
