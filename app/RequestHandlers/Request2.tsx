import AsyncStorage from "@react-native-async-storage/async-storage";
import DeviceInfo from "react-native-device-info";
import { Platform } from "react-native";

export class RequestHandler2 {
  constructor() {}
  private BASE_URL = "https://issuecards-api-bridgecard-co.relay.evervault.com/";

  private async getUniqueID(): Promise<string | null> {
    return await AsyncStorage.getItem("uniqueID");
  }

  public async get(
    path: string,
    token?: string,
    functions?: Array<(data: any) => void>
  ) {
    const UNIQUE_ID = await this.getUniqueID();
    const deviceName = await DeviceInfo.getDeviceName();
    fetch(`${this.BASE_URL}${path}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        "x-device-id": UNIQUE_ID || "",
        "x-device": deviceName,
        "x-os": Platform.OS,
        Authorization: `Bearer ${token}`,
      },
    })
      .then((res) => res.json())
      .then((data) => {
        if (functions && functions.length > 0) {
          functions.forEach((func) => func(data));
        } else {
          return data;
        }
      });
  }
}
