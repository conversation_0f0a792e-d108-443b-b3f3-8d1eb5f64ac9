import React, { createContext, useContext, useState, useCallback } from 'react';
import { NavigationProp } from '@react-navigation/native';

interface WelcomeModalContextType {
  isWelcomeModalVisible: boolean;
  showWelcomeModal: () => void;
  hideWelcomeModal: () => void;
  setNavigation: (navigation: NavigationProp<any>) => void;
  navigation: NavigationProp<any> | null;
}

const WelcomeModalContext = createContext<WelcomeModalContextType | undefined>(undefined);

export function WelcomeModalProvider({ children }: { children: React.ReactNode }) {
  const [isWelcomeModalVisible, setIsWelcomeModalVisible] = useState(false);
  const [navigation, setNavigation] = useState<NavigationProp<any> | null>(null);

  const showWelcomeModal = useCallback(() => {
    setIsWelcomeModalVisible(true);
  }, []);

  const hideWelcomeModal = useCallback(() => {
    setIsWelcomeModalVisible(false);
  }, []);

  return (
    <WelcomeModalContext.Provider
      value={{
        isWelcomeModalVisible,
        showWelcomeModal,
        hideWelcomeModal,
        setNavigation,
        navigation
      }}
    >
      {children}
    </WelcomeModalContext.Provider>
  );
}

export function useWelcomeModal() {
  const context = useContext(WelcomeModalContext);
  if (context === undefined) {
    throw new Error('useWelcomeModal must be used within a WelcomeModalProvider');
  }
  return context;
} 