import React, { createContext, useState, useContext, ReactNode } from "react";
import GlobalErrorBottomSheet from "../components/GlobalErrorBottomSheet";

interface GlobalModalContextProps {
  showAddMoneyFailedModal: (errorMessage?: string) => void;
  hideAddMoneyFailedModal: () => void;
}

const GlobalModalContext = createContext<GlobalModalContextProps>({
  showAddMoneyFailedModal: (errorMessage?: string) => {},
  hideAddMoneyFailedModal: () => {},
});

export const useGlobalModal = () => useContext(GlobalModalContext);

export const GlobalModalProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isAddMoneyFailedVisible, setIsAddMoneyFailedVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);

  const showAddMoneyFailedModal = (errorMsg?: string) => {
    setErrorMessage(errorMsg);
    setIsAddMoneyFailedVisible(true);
  };

  const hideAddMoneyFailedModal = () => {
    setIsAddMoneyFailedVisible(false);
    setErrorMessage(undefined);
  };

  const handleExploreOptions = () => {
    hideAddMoneyFailedModal();
    // Simply close the modal, no navigation
  };

  return (
    <GlobalModalContext.Provider
      value={{
        showAddMoneyFailedModal,
        hideAddMoneyFailedModal,
      }}
    >
      {children}
      <GlobalErrorBottomSheet
        isVisible={isAddMoneyFailedVisible}
        onClose={hideAddMoneyFailedModal}
        onExploreOptions={handleExploreOptions}
        errorMessage={errorMessage}
      />
    </GlobalModalContext.Provider>
  );
};

