import React, { createContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import i18n from '../../i18n'; // Adjust the path to your i18n config

// @ts-ignore
export const LanguageContext = createContext();

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('en'); // Default language

  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const storedLang = await AsyncStorage.getItem('language');
        const defaultLang = storedLang || 'en';
        i18n.locale = defaultLang; // Set the i18n locale to the stored language
        setLanguage(defaultLang);  // Set the state to trigger re-render
      } catch (error) {
      }
    };

    loadLanguage();
  }, []);

  const changeLanguage = async (selectedLang) => {
    try {
      const langCode = selectedLang.code;
      await AsyncStorage.setItem('language', langCode); // Save language in storage
      i18n.locale = langCode; // Set i18n locale
      setLanguage(langCode);  // Update the state to trigger re-render
    } catch (error) {
      console.log('Error saving language', error);
    }
  };

  return (
    <LanguageContext.Provider value={{ language, changeLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
};
