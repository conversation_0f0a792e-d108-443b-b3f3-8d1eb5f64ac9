// ToastContext.tsx
import React, { createContext, useState, useContext, useRef } from "react";
import {
  Animated,
  Dimensions,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { SvgXml } from "react-native-svg";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import { svg } from "../config/Svg";
import P from "../components/P";
const { width, height } = Dimensions.get("window");
import Constants from "expo-constants";
const baseHeight = 802;

interface ToastContextType {
  handleToast: (text: string, type?: "error" | "success" | "pending") => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
};

export const ToastProvider = ({ children }: { children: React.ReactNode }) => {
  const [show, setShow] = useState(false);
  const [text, setText] = useState("");
  const [type, setType] = useState("");
  const fadeAnim = useState(new Animated.Value(0))[0];
  const translateY = useState(new Animated.Value(-100))[0];
  const toastTimeout = useRef<NodeJS.Timeout | number | null>(null);

  const hideToast = () => {
    if (toastTimeout.current) clearTimeout(toastTimeout.current);
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: -100,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShow(false);
    });
  };

  const handleToast = (
    text: string,
    type?: "error" | "success" | "pending"
  ) => {
    if (toastTimeout.current) clearTimeout(toastTimeout.current);
    fadeAnim.stopAnimation();
    translateY.stopAnimation();

    setText(text);
    setType(type || "success");
    setShow(true);

    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    toastTimeout.current = setTimeout(() => {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: -100,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setShow(false);
      });
    }, 4000);
  };

  return (
    <ToastContext.Provider value={{ handleToast }}>
      {children}
      {show && (
        <Animated.View
          style={[styles.popBody, { opacity: fadeAnim }]}
          pointerEvents="box-none"
        >
          <Animated.View
            style={[
              styles.popCard,
              {
                transform: [{ translateY }],
                backgroundColor: colors.white,
              },
            ]}
          >
            {type === "error" ? (
              <SvgXml xml={svg.toastFailed} style={{ marginLeft: 5 }} />
            ) : type === "pending" ? (
              <SvgXml xml={svg.toastPending} style={{ marginLeft: 5 }} />
            ) : (
              <SvgXml xml={svg.toastSuccess} style={{ marginLeft: 5 }} />
            )}
            <P style={styles.headerText}>{text}</P>
            <TouchableOpacity onPress={hideToast} style={styles.closeButton}>
              <SvgXml xml={svg.xClose} />
            </TouchableOpacity>
          </Animated.View>
        </Animated.View>
      )}
    </ToastContext.Provider>
  );
};

const styles = StyleSheet.create({
  popBody: {
    width,
    height,
    flex: 1,
    backgroundColor: "transparent",
    position: "absolute",
    alignItems: "center",
    justifyContent: "flex-start",
    zIndex: 8000000,
  },
  popCard: {
    width: "90%",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    paddingLeft: 16,
    paddingRight: 16,
    position: "absolute",
    top: 20,
    flexDirection: "row",
    minHeight: 44,
    marginTop: Constants.statusBarHeight,
    // iOS shadow
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    // Android shadow
    elevation: 15,
  },
  headerText: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    textAlign: "left",
    marginLeft: 12,
    color: colors.black,
    flex: 1,
  },
  closeButton: {
    padding: 8,
    marginLeft: 8,
  },
});
