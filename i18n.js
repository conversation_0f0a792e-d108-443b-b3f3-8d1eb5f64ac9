import { getLocales } from 'expo-localization';
import { I18n } from 'i18n-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

import en from './locales/en.json';
import tr from './locales/tr.json';

// Define translations
const translations = {
  en,
  tr,
};

// Create an instance of I18n
const i18n = new I18n(translations);

// Set the default locale and fallback
i18n.defaultLocale = 'en';
i18n.enableFallback = true;

// Function to initialize language settings
export const initLanguage = async () => {
  try {
    const storedLang = await AsyncStorage.getItem('language');
    if (storedLang) {
      i18n.locale = storedLang; // Use the stored language code directly
    } else {
      // Fallback to device locale
      i18n.locale = getLocales()[0]?.languageCode ?? 'en';
    }
  } catch (error) {
    i18n.locale = 'en'; // Fallback to English in case of error
  }
};

export default i18n;
