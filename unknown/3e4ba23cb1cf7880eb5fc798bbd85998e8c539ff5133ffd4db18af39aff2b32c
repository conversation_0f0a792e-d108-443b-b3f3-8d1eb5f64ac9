import React from 'react';
import { View, ViewStyle } from 'react-native';
import { SvgXml } from 'react-native-svg';

interface DashedLineProps {
  color?: string;
  width?: number | string;
  height?: number;
  dashLength?: number;
  gapLength?: number;
  style?: ViewStyle;
}

const DashedLine: React.FC<DashedLineProps> = ({
  color = '#E5E5E5',
  width = '100%',
  height = 1,
  dashLength = 4,
  gapLength = 4,
  style,
}) => {
  const svgWidth = typeof width === 'string' ? 300 : width;
  
  return (
    <View style={[{ alignItems: 'center' }, style]}>
      <SvgXml
        xml={`
          <svg width="100%" height="${height}" viewBox="0 0 ${svgWidth} ${height}" xmlns="http://www.w3.org/2000/svg">
            <line 
              x1="0" 
              y1="${height / 2}" 
              x2="${svgWidth}" 
              y2="${height / 2}" 
              stroke="${color}" 
              strokeWidth="${height}" 
              strokeDasharray="${dashLength},${gapLength}"
            />
          </svg>
        `}
        width={width}
        height={height}
      />
    </View>
  );
};

export default DashedLine;
